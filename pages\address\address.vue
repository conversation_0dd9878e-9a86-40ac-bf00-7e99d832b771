<template>
	<view class="container">
		<block v-if="isload">
			<view class="topsearch flex-y-center">
				<view class="f1 flex-y-center">
					<image class="img" src="/static/img/search_ico.png"></image>
					<input :value="keyword" :placeholder="lang('address tearch tips')"
						placeholder-style="font-size:24rpx;color:#C2C2C2" @confirm="searchConfirm"
						@input="searchChange"></input>
				</view>
			</view>
			<view v-for="(item, index) in datalist" :key="index" class="content" @tap.stop="setdefault(item)"
				:data-id="item.id">
				<view class="f1">
					<text class="t1">{{item.name}}</text>
					<text class="t2">{{item.tel}}</text>
					<text class="t2" v-if="item.company">{{item.company}}</text>
					<text class="flex1"></text>
					<image class="t3" src="/static/img/edit.png" @tap.stop="goto"
						:data-url="'/pages/address/addressadd?id=' + item.id + '&type=' + (item.latitude>0 ? '1' : '0')">
				</view>
				<view class="f2">{{item.area}} {{item.address}}</view>
				<view class="f3">
					<view class="flex-y-center">
						<view class="radio" :style="item.isdefault ? 'border:0;background:'+t('color1') : ''">
							<image class="radio-img" src="/static/img/checkd.png" />
						</view>
						<view class="mrtxt" @tap="setDefaultAddress(item)">{{item.isdefault ? lang("default address") : lang('set it to default')}}
						</view>
					</view>
					<view class="flex1"></view>


					<view class="del" :style="{color:t('color1')}" @tap.stop="del" :data-id="item.id">{{lang("delete")}}
					</view>
				</view>
			</view>
			<nodata v-if="nodata"></nodata>
			<view style="height:140rpx"></view>
			<view class="btn-add" :class="menuindex>-1?'tabbarbot':'notabbarbot3'"
				:style="'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'"
				@tap="goto" :data-url="'/pages/address/addressadd?type=' + type">
				<image src="/static/img/add.png" style="width:28rpx;height:28rpx;margin-right:6rpx" />
				{{lang('add address')}}
			</view>
		</block>
		<loading v-if="loading"></loading>
		<dp-tabbar :opt="opt" @getmenuindex="getmenuindex"></dp-tabbar>
		<popmsg ref="popmsg"></popmsg>
	</view>
</template>

<script>
var app = getApp();
export default {
  data() {
    return {
      opt: {},
      loading: false,
      isload: false,
      menuindex: -1,

      datalist: [],
      type: "",
      state: "",
      keyword: "",
      nodata: false,
    };
  },
  onLoad: function (opt) {
    this.opt = app.getopts(opt);
    this.type = this.opt.type || "";
    this.state = this.opt.state || "";
    this.getdata();
    uni.setNavigationBarTitle({
      title: app.lang("Shipping address"),
      success() {
        console.log(app.lang("Shipping address"));
      },
    });
  },
  onPullDownRefresh: function () {
    this.getdata();
  },
  methods: {
    lang: function (k) {
      return app.lang(k);
    },
    getdata: function () {
      var that = this;
      that.loading = true;
      that.nodata = false;
      app.get(
        "ApiAddress/address",
        {
          type: that.opt.type,
          keyword: that.keyword,
        },
        function (res) {
          that.loading = false;
          var datalist = res.data;
          if (datalist.length == 0 && that.keyword == "") {
            uni.redirectTo({
              url: "/pages/address/addressadd?type=" + (that.opt.type || 0),
            });
          } else if (datalist.length == 0) {
            that.datalist = datalist;
            that.nodata = true;
          } else {
            that.datalist = datalist;
          }
          that.loaded();
        }
      );
    },
    //选择收货地址
    setdefault: function (e) {
      var that = this;
      var fromPage = this.opt.fromPage;
      var addressId = e.id;
      app.post(
        "ApiAddress/setdefault",
        {
          addressid: addressId,
        },
        function (data) {
          uni.redirectTo({
            url: "/pages/order/warehouse",
          });
        }
      );
    },
    del: function (e) {
      var that = this;
      var addressId = e.currentTarget.dataset.id;
      console.log(addressId);
      app.confirm(app.lang("delete address ask"), function () {
        app.post(
          "ApiAddress/del",
          {
            addressid: addressId,
          },
          function (res) {
            app.success(res.msg);
            that.getdata();
          }
        );
      });
    },
    searchChange: function (e) {
      this.keyword = e.detail.value;
    },
    searchConfirm: function (e) {
      var that = this;
      var keyword = e.detail.value;
      that.keyword = keyword;
      that.getdata();
    },
    getweixinaddress: function () {
      var that = this;
      wx.chooseAddress({
        success(res) {
          app.showLoading(app.lang("please wait"));
          app.post(
            "ApiAddress/addressadd",
            {
              type: that.type,
              addressid: "",
              name: res.userName,
              tel: res.telNumber,
              area:
                res.provinceName + "," + res.cityName + "," + res.countyName,
              address: res.detailInfo,
            },
            function (res) {
              app.showLoading(false);
              if (res.status == 0) {
                app.alert(res.msg);
                return;
              }
              that.getdata();
              app.success(app.lang("adding succeeded"));
            }
          );
        },
      });
    },
  },
};
</script>
<style>
.topsearch {
  width: 94%;
  margin: 16rpx 3%;
}

.topsearch .f1 {
  height: 60rpx;
  border-radius: 30rpx;
  border: 0;
  background-color: #fff;
  flex: 1;
}

.topsearch .f1 .img {
  width: 24rpx;
  height: 24rpx;
  margin-left: 10px;
}

.topsearch .f1 input {
  height: 100%;
  flex: 1;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
}

.content {
  width: 94%;
  margin: 20rpx 3%;
  background: #fff;
  border-radius: 5px;
  padding: 20rpx 40rpx;
}

.content .f1 {
  height: 96rpx;
  line-height: 96rpx;
  display: flex;
  align-items: center;
}

.content .f1 .t1 {
  color: #2b2b2b;
  font-weight: bold;
  font-size: 30rpx;
}

.content .f1 .t2 {
  color: #999999;
  font-size: 28rpx;
  margin-left: 10rpx;
}

.content .f1 .t3 {
  width: 28rpx;
  height: 28rpx;
}

.content .f2 {
  color: #2b2b2b;
  font-size: 26rpx;
  line-height: 42rpx;
  padding-bottom: 20rpx;
  border-bottom: 1px solid #f2f2f2;
}

.content .f3 {
  height: 96rpx;
  display: flex;
  align-items: center;
}

.content .radio {
  flex-shrink: 0;
  width: 36rpx;
  height: 36rpx;
  background: #ffffff;
  border: 3rpx solid #bfbfbf;
  border-radius: 50%;
}

.content .radio .radio-img {
  width: 100%;
  height: 100%;
}

.content .mrtxt {
  color: #2b2b2b;
  font-size: 26rpx;
  margin-left: 10rpx;
}

.content .del {
  font-size: 24rpx;
}

.container .btn-add {
  width: 90%;
  max-width: 700px;
  margin: 0 auto;
  height: 96rpx;
  line-height: 96rpx;
  text-align: center;
  color: #fff;
  font-size: 30rpx;
  font-weight: bold;
  border-radius: 40rpx;
  position: fixed;
  left: 0px;
  right: 0;
  bottom: 0;
  margin-bottom: 20rpx;
}

.container .btn-add2 {
  width: 43%;
  max-width: 700px;
  margin: 0 auto;
  height: 96rpx;
  line-height: 96rpx;
  text-align: center;
  color: #fff;
  font-size: 30rpx;
  font-weight: bold;
  border-radius: 40rpx;
  position: fixed;
  left: 5%;
  bottom: 0;
  margin-bottom: 20rpx;
}

.container .btn-add3 {
  width: 43%;
  max-width: 700px;
  margin: 0 auto;
  height: 96rpx;
  line-height: 96rpx;
  text-align: center;
  color: #fff;
  font-size: 30rpx;
  font-weight: bold;
  border-radius: 40rpx;
  position: fixed;
  right: 5%;
  bottom: 0;
  margin-bottom: 20rpx;
}
</style>
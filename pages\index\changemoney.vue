<template>
	<view>
		<view class="info-item" @tap="changeMoney('cny')" data-url="setnickname">
			<view class="t1">CNY</view>
			<view class="t2"></view>
			<image class="t3" src="/static/img/arrowright.png" />
		</view>
		<view class="info-item" @tap="changeMoney('twd')" data-url="setnickname">
			<view class="t1">TWD</view>
			<view class="t2"></view>
			<image class="t3" src="/static/img/arrowright.png" />
		</view>
		<view class="info-item" @tap="changeMoney('esd')" data-url="setnickname">
			<view class="t1">USD</view>
			<view class="t2"></view>
			<image class="t3" src="/static/img/arrowright.png" />
		</view>
		<view class="info-item" @tap="changeMoney('vnd')" data-url="setnickname">
			<view class="t1">VND</view>
			<view class="t2"></view>
			<image class="t3" src="/static/img/arrowright.png" />
		</view>
		<view class="info-item" @tap="changeMoney('thb')" data-url="setnickname">
			<view class="t1">THB</view>
			<view class="t2"></view>
			<image class="t3" src="/static/img/arrowright.png" />
		</view>
		<view class="info-item" @tap="changeMoney('inr')" data-url="setnickname">
			<view class="t1">INR</view>
			<view class="t2"></view>
			<image class="t3" src="/static/img/arrowright.png" />
		</view>
		<view class="info-item" @tap="changeMoney('myr')" data-url="setnickname">
			<view class="t1">MYR</view>
			<view class="t2"></view>
			<image class="t3" src="/static/img/arrowright.png" />
		</view>
	</view>
</template>

<script>
	var app = getApp();
	export default {
		data() {
			return {
				opt: {},
			}
		},
		onLoad: function(opt) {
			this.opt = app.getopts(opt);
			console.log(opt)
		},
		methods: {
			lang: function(k) {
				return app.lang(k);
			},
			changeMoney(currency) {
				var lang = uni.getStorageSync('mylang')
				app.post("ApiMy/choose_lan_currency", {
					lang: lang,
					currency: currency
				}, function(data) {
					app.showLoading(false);
					if (data.status == 1) {
						app.success(data.msg);
						setTimeout(function() {
							app.changeMoney(lang)
						}, 1000);
					} else {
						app.error(data.msg);
					}
				});

			},
		}
	}
</script>

<style>
	.content {
		width: 94%;
		margin: 20rpx 3%;
		background: #fff;
		border-radius: 5px;
		padding: 0 20rpx;
	}

	.info-item {
		display: flex;
		align-items: center;
		width: 100%;
		background: #fff;
		padding: 0 3%;
		border-bottom: 1px #f3f3f3 solid;
		height: 96rpx;
		line-height: 96rpx;
	}

	.info-item:last-child {
		border: none;
	}

	.info-item .t1 {
		width: 300rpx;
		color: #8b8b8b;
		font-weight: bold;
		height: 96rpx;
		line-height: 96rpx;
	}

	.info-item .t2 {
		color: #8b8b8b;
		text-align: right;
		flex: 1;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 1;
		overflow: hidden;
	}

	.info-item .t3 {
		width: 26rpx;
		height: 26rpx;
		margin-left: 20rpx;
	}
</style>

<template>
<view class="container">
	<block v-if="isload">
		<form @submit="formSubmit" @reset="formReset">
		<view class="title" :style="{color:t('color1')}">{{lang('reset password')}}</view>
		<view class="regform">
				<view class="form-item">
							 <image src="/static/img/sign-up_07.jpg" class="img" />
						
							<select  class="select" @change=changes() v-model="selected" style="width: 80%;font-size:30rpx;" placeholder-style="font-size:30rpx;color:#B2B5BE"  name="selectList" >
								<option :value="item.code" v-for="(item,index) in countries" :key="index">{{item.name}}</option>
							</select>
							<!-- <img class="downimg" src="https://qi-1311633249.cos.ap-nanjing.myqcloud.com/gbshopee/down.png" alt="" srcset=""> -->
						</view>
			<view class="form-item">
				<image src="/static/img/sign-up_06.jpg" class="img"/>
				<input type="text" class="input" :placeholder="lang('please enter your mobile phone number')" placeholder-style="font-size:30rpx;color:#B2B5BE" name="tel" value="" @input="telinput"/>
			</view>
			<view class="form-item">
				<image src="/static/img/sign-up_08.jpg" class="img"/>
				<input type="text" class="input"  placeholder-style="font-size:30rpx;color:#B2B5BE" name="smscode" value=""/>
				<view class="code" :style="{color:t('color1')}" @tap="smscode">{{smsdjs||lang('obtaining the verification code')}}</view>
			</view>
			<view class="form-item">
				<image src="/static/img/sign-up_10.jpg" class="img"/>
				<input type="text" class="input" :placeholder="lang('password combination tips')" placeholder-style="font-size:30rpx;color:#B2B5BE" name="pwd" value="" :password="true"/>
			</view>
			<view class="form-item">
				<image src="/static/img/sign-up_10.jpg" class="img"/>
				<input type="text" class="input" :placeholder="lang('enter the login password again')" placeholder-style="font-size:30rpx;color:#B2B5BE" name="repwd" value="" :password="true"/>
			</view>
			<button class="form-btn" form-type="submit" :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}">{{lang('confirm')}}</button>
		</view>
		</form>
	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
			opt:{},
			loading:false,
      isload: false,
			menuindex:-1,
			
			logintype_1:true,
			logintype_2:false,
			logintype_3:false,
			xystatus:0,
			xycontent:'',
			needsms:false,
			showxieyi:false,
			isagree:false,
      smsdjs: '',
			tel:'',
      hqing: 0,
	  selected:"+65",
	  	countries:    [ 
		  {
		    "code": "+61",
		    "name": app.lang("Australia +61")
		  },
		  {
		    "code": "+673",
		    "name": app.lang("Brunei +673") 
		  },
		  {
		    "code": "+880",
		    "name": app.lang("Bangladesh +880") 
		  },
		  {
		    "code": "+855",
		    "name":  app.lang("Cambodia +855")
		  },
		  {
		    "code": "+86",
		    "name": app.lang("China +86") 
		  },
		  {
		    "code": "+886",
		    "name":  app.lang("Hong Kong +852")
		  },
		
		  {
		    "code": "+91",
		    "name": app.lang("India +91") 
		  },
		  {
		    "code": "+81",
		    "name":  app.lang("Japan +81")
		  },
		  {
		    "code": "+60",
		    "name":  app.lang("Malaysia +60")
		  },
		  {
		    "code": "+95",
		    "name":  app.lang("Myanmar +95")
		  },
		  {
		    "code": "+64",
		    "name":  app.lang("New Zealand +64")
		  },
		  
		  {
		    "code": "+968",
		    "name":  app.lang("Oman +968")
		  },
		  {
		    "code": "+63",
		    "name":  app.lang("Philippines +63")
		  },
			{
			  "code": "+65",
			  "name":  app.lang("Singapore +65")
			},
			
			
			{
			  "code": "+82",
			  "name": app.lang( "South Korea +82")
			},
			{
			  "code": "+94",
			  "name": app.lang( "Sri Lanka +94")
			},
		
			{
			  "code": "+66",
			  "name": app.lang("Thailand +66") 
			},
			{
			  "code": "+886",
			  "name":  app.lang("Taiwan   +886")
			},
			{
			  "code": "+84",
			  "name": app.lang("Vietnam +84") 
			},
			{
			  "code": "+62",
			  "name": app.lang("Indonesia +62") 
			},
		
		] ,
    };
  },

  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.getdata();
		this.getcodedata()
  },
	onPullDownRefresh: function () {
		this.getdata();
		this.getcodedata()
	},
  methods: {
	  lang: function(k) {
	  	return app.lang(k);
	  },
		getdata: function () {
			this.loaded();
		},
		getcodedata:function(){
			var that = this;
			that.loading = true;
			app.get('ApiIndex/areas', {  }, function (res) {
			  that.loading = false;
			  console.log('codedata',res)
				that.countries=res.data
			   that.selected= res.default
			  })
		},  
		  changes:function(e){
				  console.log(this.selected)
			  },
    formSubmit: function (e) {
			var that = this;
      var formdata = e.detail.value;
      if (formdata.tel == ''){
        app.alert(app.lang('please enter your mobile phone number'));
        return;
      }
      if (formdata.pwd == '') {
        app.alert(app.lang('please enter your password'));
        return;
      }
      if (formdata.pwd.length < 6) {
        app.alert(app.lang('password combination tips'));
        return;
      }
      if (formdata.repwd == '') {
        app.alert(app.lang('enter the login password again'));
        return;
      }
      if (formdata.pwd != formdata.repwd) {
        app.alert(app.lang('two passwords are different'));
        return;
      }
			if (formdata.smscode == '') {
				app.alert(app.lang('please enter your mobile verify code'));
				return;
			}
			
			app.showLoading(app.lang('please wait'));
			const editedText = that.selected.slice(1)
      app.post("ApiIndex/getpwd", {code:editedText,tel:formdata.tel,pwd:formdata.pwd,smscode:formdata.smscode}, function (data) {
				app.showLoading(false);
        if (data.status == 1) {
          app.success(data.msg);
          setTimeout(function () {
            app.goto('/pages/index/login');
          }, 1000);
        } else {
          app.error(data.msg);
        }
      });
    },
    telinput: function (e) {
      this.tel = e.detail.value
    },
    smscode: function () {
      var that = this;
      if (that.hqing == 1) return;
      that.hqing = 1;
      var tel = that.tel;
      if (tel == '') {
        app.alert(app.lang('please enter your mobile phone number'));
        that.hqing = 0;
        return false;
      }
      // if (!/^1[3456789]\d{9}$/.test(tel)) {
      //   app.alert(app.lang('wrong cell phone number'));
      //   that.hqing = 0;
      //   return false;
      // }
	  const editedText = that.selected.slice(1)
      app.post("ApiIndex/sendsms", {code:editedText,tel: tel}, function (data) {
        if (data.status != 1) {
          app.alert(data.msg);
        }
      });
      var time = 120;
      var interval1 = setInterval(function () {
        time--;
        if (time < 0) {
          that.smsdjs = app.lang('obtain again');
          that.hqing = 0;
          clearInterval(interval1);
        } else if (time >= 0) {
          that.smsdjs = time + app.lang('second');
        }
      }, 1000);
    }
  }
};
</script>

<style>
page{background:#ffffff}
.container{width:100%;}
.title{margin:70rpx 50rpx 50rpx 40rpx;height:60rpx;line-height:60rpx;font-size: 48rpx;font-weight: bold;color: #000000;}
.regform{ width:100%;padding:0 50rpx;border-radius:5px;background: #FFF;}
.regform .form-item{display:flex;align-items:center;width:100%;border-bottom: 1px #ededed solid;height:88rpx;line-height:88rpx;border-bottom:1px solid #F0F3F6;margin-top:20rpx}
.regform .form-item:last-child{border:0}
.regform .form-item .img{width:33rpx;height:44rpx;margin-right:30rpx}
.regform .form-item .input{flex:1;color: #000;}
.regform .form-item .code{font-size:30rpx}
.regform .xieyi-item{display:flex;align-items:center;margin-top:50rpx}
.regform .xieyi-item{font-size:24rpx;color:#B2B5BE}
.regform .xieyi-item .checkbox{transform: scale(0.6);}
.regform .form-btn{margin-top:60rpx;width:100%;height:96rpx;line-height:96rpx;color:#fff;font-size:30rpx;border-radius: 48rpx;}

.othertip{height:auto;overflow: hidden;display:flex;align-items:center;width:580rpx;padding:20rpx 20rpx;margin:0 auto;margin-top:60rpx;}
.othertip-line{height: auto; padding: 0; overflow: hidden;flex:1;height:0;border-top:1px solid #F2F2F2}
.othertip-text{padding:0 32rpx;text-align:center;display:flex;align-items:center;justify-content:center}
.othertip-text .txt{color:#A3A3A3;font-size:22rpx}

.othertype{width:70%;margin:20rpx 15%;display:flex;justify-content:center;}
.othertype-item{width:50%;display:flex;flex-direction:column;align-items:center;}
.othertype-item .img{width:88rpx;height:88rpx;margin-bottom:20rpx}
.othertype-item .txt{color:#A3A3A3;font-size:24rpx}

.xieyibox{width:100%;height:100%;position:fixed;top:0;left:0;z-index:99;background:rgba(0,0,0,0.7)}
.xieyibox-content{width:90%;margin:0 auto;height:80%;margin-top:20%;background:#fff;color:#333;padding:5px 10px 50px 10px;position:relative;border-radius:2px}
.select{
	border: none;
	background: #f6f6f6;
	height: 23px;
	width: 85%!important;
	color:#b2b5be !important;
}
</style>
<template>
<view>
	<block v-if="isload">
		<view class="container">
			<view class="info-box">
				<view class="info-item">
					<text class="t1">{{lang('merchant name')}}:</text>
					<text class="t2">{{name}}</text>
				</view>
				<view class="info-item" v-if="mdlist.length>0">
					<text class="t1">{{lang('select stores')}}:</text>
					<text class="t2" @tap="selectmd">{{mdlist[mdkey].name}}</text>
				</view>
				<view class="info-item2">
					<text class="t1">{{lang('pay the amount')}}:</text><br />
				</view>
				<view>
					<view class="t2 flex-y-center ts2" style="justify-content:flex-start"><text class="fontbigsize">$ |</text><input type="digit" @input="inputMoney" value="" placeholder=""></input> </view>
				</view>
			</view>
			<view class="info-box">
				<view class="dkdiv-item flex" v-if="userinfo.discount>0 && userinfo.discount<10">
					<text class="f1">{{t('会员')}}{{lang('discount')}}({{userinfo.discount*100/100}}{{lang('fold')}})</text>
					<text class="f2" style="color: #e94745;">-{{disprice}}</text>
				</view>
				<view class="dkdiv-item flex">
					<text class="f1">{{t('优惠券')}}</text>
					<text class="f2" @tap="showCouponList" style="color:#e94745">{{couponrid!=0?couponList[couponkey].couponname:lang('please select a')+t('优惠券')}}</text>
					<image class="f3" src="/static/img/arrowright.png"></image>
				</view>
				<view class="dkdiv-item flex">
					<checkbox-group @change="scoredk" class="flex" style="width:100%">
						<text class="f1">{{userinfo.score*1}} {{t('积分')}}{{lang('deductible')}} <text style="color:#e94745">{{userinfo.dkmoney*1}}</text> {{lang('yuan')}}</text>
						<view class="f2">{{lang('use')}}{{t('积分')}}{{lang('deduction')}}
							<checkbox value="1" style="margin-left:6px;transform:scale(.8)"></checkbox>
						</view>
					</checkbox-group>
				</view>
			</view>
			<view class="info-box">
				<view class="info-item">
					<text class="t1">{{lang('amount of real pay')}}:</text>
					<text class="t2">{{paymoney}}</text>
				</view>
			</view>
			<view class="op">
				<view class="btn" @tap="topay" :style="{background:t('color1')}">{{lang('to pay for')}}</view>
			</view>
		</view>
		<view v-if="couponvisible" class="popup__container">
			<view class="popup__overlay" @tap.stop="handleClickMask"></view>
			<view class="popup__modal">
				<view class="popup__title">
					<text class="popup__title-text">{{lang('please select a')}}{{t('优惠券')}}</text>
					<image src="/static/img/close.png" class="popup__close" style="width:36rpx;height:36rpx" @tap.stop="handleClickMask"/>
				</view>
				<view class="popup__content">
					<couponlist :couponlist="couponList" :choosecoupon="true" :selectedrid="couponrid" @chooseCoupon="chooseCoupon"></couponlist>
				</view>
			</view>
		</view>
	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
			opt:{},
			loading:false,
      isload: false,
			menuindex:-1,
			
			bid:0,
      hiddenmodalput: true,
			wxpayst:'',
			alipay:'',
      paypwd: '',
      moneypay: '',
      mdlist: "",
      name: "",
      userinfo: "",
      couponList: [],
      couponrid: 0,
      coupontype: 1,
      usescore: 0,
      money: 0,
      disprice: 0,
      dkmoney: 0,
      couponmoney: 0,
      paymoney: 0,
      mdkey: 0,
      couponvisible: false,
      couponkey: 0
    };
  },

  onLoad: function (opt) {
	 
		this.opt = app.getopts(opt);
		 console.log('bid',this.opt)
		this.bid = this.opt.id?this.opt.id:0;
		console.log('1213',this.bid)
		this.getdata();
		uni.setNavigationBarTitle({
			title:app.lang("pay bill"),
			success() {
				console.log(app.lang("pay bill"))
			}
		})
  },
	onPullDownRefresh: function () {
		this.getdata();
	},
  methods: {
		lang: function(k) {
			return app.lang(k);
		},
		getdata: function () {
			var that = this; //获取产品信息
			
			that.loading = true;
			app.get('ApiMaidan/maidan', {bid:that.bid}, function (res) {
				that.loading = false;
				if (res.status == 0) {
					app.alert(res.msg, function () {
						app.goback();
					});
					return;
				}
				var userinfo = res.userinfo;
				var couponList = res.couponList;
				var mdlist = res.mdlist;
				that.wxpayst = res.wxpayst;
				that.alipay = res.alipay;
				that.couponList = res.couponList;
				that.mdlist = res.mdlist;
				that.moneypay = res.moneypay;
				that.name = res.name;
				that.userinfo = res.userinfo;
				that.loaded();

				if (mdlist.length > 0) {
					app.getLocation(function (res) {
						var latitude = res.latitude;
						var longitude = res.longitude;
						var speed = res.speed;
						var accuracy = res.accuracy;

						for (var i in mdlist) {
							mdlist[i].juli = that.GetDistance(latitude, longitude, mdlist[i].latitude, mdlist[i].longitude);
						}

						mdlist = mdlist.sort(that.compare('juli'));
						console.log(mdlist);
						that.mdlist = mdlist;
					});
				}
			});
		},
    modalinput: function () {
      this.$refs.dialogInput.open()
    },
    //选择门店
    selectmd: function (e) {
      var that = this;
      var itemlist = [];
      var mdlist = this.mdlist;
      for (var i = 0; i < mdlist.length; i++) {
        itemlist.push(mdlist[i].name + (mdlist[i].juli ? that.lang('distance')+':' + mdlist[i].juli + 'km' : ''));
      }
      uni.showActionSheet({
        itemList: itemlist,
        success: function (res) {
					if(res.tapIndex >= 0){
						that.mdkey = res.tapIndex;
					}
        }
      });
    },
    //积分抵扣
    scoredk: function (e) {
      var usescore = e.detail.value[0];
      if (!usescore) usescore = 0;
      this.usescore = usescore;
      this.calculatePrice();
    },
    inputMoney: function (e) {
      console.log(e);
      var money = e.detail.value;
      if (!money) money = 0;
      var money = parseFloat(money);
      if (money <= 0) money = 0;
      this.money = money;
      this.calculatePrice();
    },
    cancel: function () {
      this.hiddenmodalput = true;
    },
    //计算价格
    calculatePrice: function () {
      var that = this;
      var money = parseFloat(that.money); //+商品总价
      if (that.userinfo.discount > 0 && that.userinfo.discount < 10) {
        var disprice = Math.round(money * (1 - 0.1 * that.userinfo.discount) * 100) / 100; //-会员折扣
      } else {
        var disprice = 0;
      }
      if (that.usescore) {
        var dkmoney = parseFloat(that.userinfo.dkmoney); //-积分抵扣
      } else {
        var dkmoney = 0;
      }
      var couponmoney = parseFloat(that.couponmoney); //-优惠券抵扣 
      var paymoney = money - disprice - couponmoney - dkmoney; // 商品金额 - 会员折扣 - 优惠券抵扣 - 积分抵扣
      if (paymoney < 0) paymoney = 0;
      paymoney = paymoney.toFixed(2);
      that.paymoney = paymoney;
      that.disprice = disprice;
    },
    chooseCoupon: function (e) {
			var couponrid = e.rid;
      var couponkey = e.key;

      if (couponrid == this.couponrid) {
        this.couponkey = 0;
        this.couponrid = 0;
        this.coupontype = 1;
        this.couponmoney = 0;
        this.couponvisible = false;
      } else {
        var couponList = this.couponList;
        var couponmoney = couponList[couponkey]['money'];
        var coupontype = couponList[couponkey]['type'];
        if (coupontype == 4) {
          couponmoney = this.freightprice;
        }
        this.couponkey = couponkey;
        this.couponrid = couponrid;
        this.coupontype = coupontype;
        this.couponmoney = couponmoney;
        this.couponvisible = false;
      }
      this.calculatePrice();
    },
    topay: function (e) {
      var that = this;
      var money = that.money;
      var couponrid = that.couponrid;
      var usescore = that.usescore;

      if (that.mdlist.length > 0) {
        var mdid = that.mdlist[that.mdkey].id;
      } else {
        var mdid = 0;
      }
			app.post('ApiMaidan/maidan', {bid:that.bid,money: money,couponrid: couponrid,usescore: usescore,mdid: mdid}, function (res) {
				if (res.status == 0) {
					app.error(res.msg);
					return;
				}
				app.goto('/pages/pay/pay?id='+res.payorderid);
			});
    },
    showCouponList: function () {
      this.couponvisible = true;
    },
    handleClickMask: function () {
      this.couponvisible = false;
    },
    GetDistance: function (lat1, lng1, lat2, lng2) {
      var radLat1 = lat1 * Math.PI / 180.0;
      var radLat2 = lat2 * Math.PI / 180.0;
      var a = radLat1 - radLat2;
      var b = lng1 * Math.PI / 180.0 - lng2 * Math.PI / 180.0;
      var s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)));
      s = s * 6378.137; // EARTH_RADIUS;
      s = Math.round(s * 100) / 100;
      return s;
    },
    compare: function (property) {
      return function (a, b) {
        var value1 = a[property];
        var value2 = b[property];
        return value1 - value2;
      };
    }
  }
}
</script>
<style>
.info-box{width:94%;margin:0 3%;margin-top:30rpx;border-radius:16rpx;overflow:hidden;padding:0 20rpx;background: #fff;}
.info-item{ display:flex;align-items:center;width: 100%; padding: 0 3%;  border-bottom: 1px #f3f3f3 solid;}
.info-item2{ display:flex;align-items:center;width: 100%; padding: 2% 3%; }
.info-item:last-child{border:none}
.info-item .t1{ width: 200rpx; height: 120rpx; line-height: 120rpx; color: #000; }
.info-item .t2{ height: 120rpx;line-height: 120rpx; color: #000;text-align:right;flex:1;font-size:30rpx}
.info-item .t2 input{height:80rpx;line-height:80rpx;border:1px solid #f5f5f5;padding:0 5px;width:240rpx;font-size:32rpx;margin-right:10rpx}

.dkdiv{margin-top:20rpx}
.dkdiv-item{ width: 100%; padding:30rpx 3%; background: #fff;border-top:1px #ededed dashed;}
.dkdiv-item:first-child{border-top:0}
.dkdiv-item .f1{ }
.dkdiv-item .f2{ text-align:right;flex:1}
.dkdiv-item .f3{ width: 40rpx; height: 40rpx;}

.fpay-btn{ width: 90%; margin: 0 5%; height: 80rpx; line-height: 80rpx; margin-top: 40rpx; float: left; border-radius: 10rpx; color: #fff; background: #1aac19; border: none; font-size: 30rpx; }
.fpay-btn2{ width: 90%; margin: 0 5%; height: 80rpx; line-height: 80rpx; margin-top: 20rpx; float: left; border-radius: 10rpx; color: #fff; background: #e2cc05; border: none; font-size: 30rpx; }

.mendian{width:90%; line-height: 60rpx;border-radius: 10rpx;padding:30rpx 5%;height:800rpx;overflow-y: scroll;border: none;border-radius:5px; -webkit-animation-duration: .5s; animation-duration: .5s;}
.mendian label{display: flex;align-items: center;border-bottom: 1px solid #f5f5f5;padding: 20rpx 0;color:#333}
.mendian input{margin-right: 10rpx}
.submit{ text-align: center}
.mendian button{ padding: 20rpx 60rpx;border-radius:40rpx;border: 0;margin-top:20rpx;color: #fff; background: #31C88E }

.i-as{position:fixed;width:100%;box-sizing:border-box;left:0;right:0;bottom:0;background:#f7f7f8;transform:translate3d(0,100%,0);transform-origin:center;transition:all .2s ease-in-out;z-index:900;visibility:hidden}
.i-as-show{transform:translate3d(0,0,0);visibility:visible}
.i-as-mask{position:fixed;top:0;left:0;right:0;bottom:0;background:rgba(0,0,0,.7);z-index:900;transition:all .2s ease-in-out;opacity:0;visibility:hidden}
.i-as-mask-show{opacity:1;visibility:visible}
.i-as-header{background:#fff;text-align:center;position:relative;font-size:30rpx;color:#555;height:80rpx;line-height:80rpx}
.i-as-header::after{content:'';position:absolute;top:0;left:0;width:200%;height:200%;transform:scale(.5);transform-origin:0 0;pointer-events:none;box-sizing:border-box;border:0 solid #e9eaec;border-bottom-width:1px}
.i-as-cancel{margin-top:20rpx}
.i-as-cancel button{ border:0}
.i-as-cancel button::after {border: 0;} 
.i-as-content{ height:700rpx;width:710rpx;margin:20rpx;}


.op{width:96%;margin:20rpx 2%;display:flex;align-items:center;margin-top:40rpx}
.op .btn{flex:1;height:100rpx;line-height:100rpx;background:#07C160;width:90%;margin:0 10rpx;border-radius:10rpx;color: #fff;font-size:28rpx;font-weight:bold;display:flex;align-items:center;justify-content:center}
.op .btn .img{width:48rpx;height:48rpx;margin-right:20rpx}
.ts2{

	padding:20rpx; ;
}
.fontbigsize{
	padding-right: 20rpx;
	font-size: 1.6em;
}
</style>
<template>
	<view class="content">
		<view class="content-top" v-if="!addressinfo">
			<button id="zt-btn" class="mini-btn" type="default" size="mini" @click="submit()">选择收货地址</button>
		</view>
		<view class="content-address" v-else>
			<view class="address-t">
				<view>
					{{addressinfo.name}}
				</view>
				<view style="margin-left: 20rpx;">
					{{addressinfo.tel}}
				</view>
			</view>
			<view class="">
				{{addressinfo.address}}
			</view>
			<view class="changeAddress"  @click="submit()">
				切换地址
			</view>
			  
		</view>
		<view class="content-middle">
			<view class="" style="margin-top: 10rpx;">
				<image src="/static/img/search.png" style="width: 40rpx;height: 40rpx;margin-right: 10rpx;"></image>
			</view>
			<view class="" style="margin-top: 12rpx;">
				<input type="text" placeholder="搜索自提点地址" v-model="search" />
			</view>

		</view>
		<view class="content-list">
			<view class="list" v-for="(item,index) in dataList">
				<view class="list-top">
					<view class="">
						{{item.name}}
					</view>
					<view class="">
						<radio-group @change="radioChange">
							<radio :value="index" :checked="item.value" />
						</radio-group>
					</view>
				</view>
				<view class="list-middle">
					{{item.address}}
				</view>
				<view class="list-button">
					<view class="list-distance">
						<view class="" style="margin-top: 10rpx;">
							<image src="/static/img/logistics-icon.png"
								style="width: 40rpx;height: 40rpx;margin-right: 10rpx;"></image>
						</view>
						距离
					</view>
					<view class="list-distance">
						<view class="" style="margin-top: 10rpx;">
							<image src="/static/img/telephone.png"
								style="width: 40rpx;height: 40rpx;margin-right: 10rpx;"></image>
						</view>
						{{item.tel}}
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	var app = getApp();
	export default {
		data() {
			return {
				dataList: [],
				myAddress:{},
				search: "",
				datainfo:{},  //存储选中地址
				id:"",
				addressinfo:"",
			}
		},
		onLoad(e) {
			this.id = e.id;
			if(e.data){
				this.addressinfo=JSON.parse(e.data);
			}
			if(this.id){
				uni.setStorageSync('typeid',this.id)
			}
			if(uni.getStorageSync('typeid')){
				this.id=uni.getStorageSync('typeid');
			}
			this.getdata();
			this.getMyaddress()
		},
		onShow() {
			this.getMyaddress()
		},
		methods: {
			radioChange(e){
				let code = ''
				
				for(var i=0;i<this.dataList.length;i++){
					if(e.detail.value==i){
						this.dataList[i].value=true;
						this.datainfo=this.dataList[i];
						console.log(this.dataList[i]);
						
					}else{
						this.dataList[i].value=false
					}
					
				}
				let objInfo={
					id:this.id,
					code:this.datainfo.code,
					phonenum:this.datainfo.phonenum,
					postalcode:this.datainfo.postalcode,
					postaladdress:this.datainfo.postaladdress
				}
				console.log("进行跳转",code,objInfo);
				uni.redirectTo({
				    url: '/pages/business/index?id='+this.id+'&code='+this.datainfo.code+'&phonenum='+this.datainfo.phonenum+'&postalcode='+this.datainfo.postalcode+'&postaladdress='+this.datainfo.postaladdress
			    })
			},
			getdata() {
				var that = this;
				app.get('ApiIndex/mendian', {}, function(res) {
					that.dataList = res.data;
					for(var i=0;i<that.dataList.length;i++){
						that.$set(that.dataList[i],'value',false);
					}
					
				})
			},
			getMyaddress: function() {
				var that = this;
				app.get('ApiAddress/address', {
					keyword: '',
					type:undefined
				}, function(res) {
					that.loading = false;
					let dataList=res.data
					let morenAddree=''
					console.log("我的地址信息",dataList);
					if(dataList&&dataList.length>0){
						morenAddree =dataList.find(item=>item.isdefault);
					}
					if(that.addressinfo.name){

					}else{
						that.addressinfo=morenAddree
					}
					console.log("that.myAddress ",that.addressinfo );
					
				
				});
			},
			submit(){
				uni.navigateTo({
					url: '/pages/address/address?state=1'
				})
			}
		}
	}
</script>

<style>
	.list-distance {
		display: flex;
	}

	.content-middle {
		display: flex;
	}

	.content {
		background-color: #ebebeb;
		height: 100vh;
	}
	.content-address{
		background: #f6f6f6;
		line-height: 50rpx;
		width: 90%;
		margin: 0 auto;
		padding: 50rpx;
		padding-right: 50rpx;
		margin-bottom: 10rpx;
		border-radius: 10rpx;
	}
	.address-t{
		color: #737373;
		display: flex;
	}
	.content-middle {
		border-radius: 10rpx;
		padding-left: 20rpx;
		line-height: 60rpx;
		width: 90%;
		margin: 0rpx auto;
		background-color: #f6f6f6;
	}

	.content-list {
		width: 90%;
		margin: 0rpx auto;
	}

	.list {
		border-radius: 10rpx;
		line-height: 60rpx;
		padding-left: 20rpx;
		padding-right: 20rpx;
		background-color: #f6f6f6;
		margin-top: 20rpx;
	}

	.list-top {
		display: flex;
		justify-content: space-between
	}

	.list-button {
		display: flex;
		justify-content: space-between
	}

	.content-top {
		width: 90%;
		margin: 0rpx auto;
		padding-top: 100rpx;
		padding-bottom: 100rpx;
		display: flex;
		justify-content: center;
	}

	#zt-btn {
		border: 1px solid #088ffb;
		color: #088ffb;
	}
	.changeAddress{
		text-align: right;
		color: #088ffb;
}
</style>
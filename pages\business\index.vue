<template>
	<view>
		<view class="changebox" v-if="pageinfo.translate == '1'">
			<view class="language"> 语言 {{ getLange() ? getLange() : "" }} </view>
			<view class="change" @tap="tochange">
				Change language
				<image src="/static/img/changelang.png" class="imgset" />
			</view>
		</view>
		<block v-if="isdiy">
			<view :style="
          'display:flex;min-height: 100vh;flex-direction: column;background-color:' +
          pageinfo.bgcolor
        ">
				<view class="container">
					<dp :pagecontent="pagecontent" :pageinfo="pageinfo" :menuindex="menuindex"></dp>
				</view>
				<!-- 这里是我写的首页 你可以自己封装 -->
				<view class="transport" v-if="opt.bid == 33">
					<view class="transport-title"> 选择运输方式 </view>
					<view class="transport-tab">
						<view :class="index == logisticstype ? 'tab-item-select' : 'tab-item'"
							v-for="(item, index) in logisticstypeList" :key="index"
							@click="setlogisticstype(index, item)">{{ item.label }}</view>
					</view>
					<view class="transport-content" v-if="result">
						<view class="content-item"> 收件人:{{ codeName }} </view>
						<view class="content-item">
							手机号码:{{ opt.phonenum ? opt.phonenum : "" }}
						</view>
						<view class="content-item">
							邮编:{{ opt.postalcode ? opt.postalcode : "" }}
						</view>
						<view class="content-item"> 收件地址:{{ postaladdress }} </view>
					</view>
					<view class="transport-contents" v-else>
						！请先选择 运输方式和自提点
					</view>
					<view class="transport-b">
						<button id="zt-btn" class="mini-btn" type="default" size="mini" @click="selectAddress()">
							选择自提点
						</button>

						<button v-if="codeName != ''" id="copy-btn" class="mini-btn" type="default" size="mini"
							@click="copy">
							复制地址
						</button>
						<button v-if="codeName == ''" id="copy-btn" class="mini-btn" style="background-color: #aaa"
							type="default" size="mini">
							复制地址
						</button>
					</view>
				</view>
				<view class="orderlist" v-if="opt.bid == 33">
					<view class="" style="margin-top: 20rpx; margin-bottom: 30rpx">
						<view class="" style="display: flex">
							<view class="" style="margin-top: 5rpx">
								<image src="/static/img/error.png" style="width: 25rpx; height: 25rpx"></image>
							</view>
							{{ jmstr }}
						</view>
					</view>
					<view class="" style="margin-top: 20rpx; margin-bottom: 20rpx; font-size: 25rpx">
						最新订单提醒
					</view>
					<order-list ref="orderlist" :orderSt="st" :orderTab="ordertab"></order-list>
				</view>
				<view class="tips" v-if="opt.bid == 33">
					<view class="" style="margin-top: 20rpx">
						免费仓储 免费仓储180天，超期收费￥1.00/天
					</view>
					<view class="">
						温馨提示
						请您直接复制仓库地址到您的拼多多，或者淘宝界面，请注意观察是否写清楚您的代码
					</view>
				</view>
			</view>
			<view class="ggdialog" v-if="guanggaopic && hideguanggao == 0">
				<view class="main">
					<view class="close" @tap="closegg">
						<image src="/static/img/close.png" />
					</view>
					<image :src="guanggaopic" class="guanggaopic" @tap="goto" :data-url="guanggaourl" mode="widthFix" />
				</view>
			</view>
		</block>
		<block v-else>
			<view class="container nodiydata" v-if="isload">
				<swiper v-if="pics.length > 0" class="swiper" :indicator-dots="pics[1] ? true : false" :autoplay="true"
					:interval="5000" indicator-color="#dcdcdc" indicator-active-color="#fff">
					<block v-for="(item, index) in pics" :key="index">
						<swiper-item class="swiper-item">
							<image :src="item" mode="widthFix" class="image" />
						</swiper-item>
					</block>
				</swiper>
				<view class="topcontent">
					<view class="logo">
						<image class="img" :src="business.logo" />
					</view>
					<view class="title">{{ business.name }}</view>
					<view class="desc">
						<view class="f1">
							<image class="img" v-for="(item2, index2) in [0, 1, 2, 3, 4]" :key="index2" :src="
                  '/static/img/star' +
                  (business.comment_score > item2 ? '2' : '') +
                  '.png'
                " />
							<text class="txt">{{ business.comment_score }}</text>
						</view>
						<view class="f2">{{ lang("sales volume") }} {{ business.sales }}</view>
					</view>
					<view class="boxtexd">
						<view class="tel boxtel" :style="{
                background:
                  'linear-gradient(90deg,' +
                  t('color1') +
                  ' 0%, rgba(' +
                  t('color1rgb') +
                  ',0.8) 100%)',
              }">
							<view @tap="tobuy" :data-phone="business.tel" class="tel_online">{{ lang("payment") }}
							</view>
						</view>
						<view class="tel boxtel" :style="{
                background:
                  'linear-gradient(90deg,' +
                  t('color1') +
                  ' 0%, rgba(' +
                  t('color1rgb') +
                  ',0.8) 100%)',
              }">
							<view @tap="tomailist" class="tel_online">{{
                lang("record")
              }}</view>
						</view>
					</view>
					<view class="address" @tap="phone" :data-phone="business.tel">
						<image class="f1" src="/static/img/tel1.png" />
						<view class="f2">{{ business.tel }}</view>
						<!-- 	<image class="f3" src="/static/img/arrowright.png"/> -->
					</view>
					<view class="address" @tap="openLocation" :data-latitude="business.latitude"
						:data-longitude="business.longitude" :data-company="business.name"
						:data-address="business.address">
						<image class="f1" src="/static/img/shop_addr.png" />
						<view class="f2">{{ business.address }}</view>
						<image class="f3" src="/static/img/arrowright.png" />
					</view>
				</view>

				<view class="contentbox">
					<view class="shop_tab">
						<view v-if="showfw" :class="'cptab_text ' + (st == -1 ? 'cptab_current' : '')" @tap="changetab"
							data-st="-1">{{ lang("service in our shop")
              }}
							<view class="after" :style="{ background: t('color1') }"></view>
						</view>
						<view :class="'cptab_text ' + (st == 0 ? 'cptab_current' : '')" @tap="changetab" data-st="0">{{ lang("products of our store")
              }}
							<view class="after" :style="{ background: t('color1') }"></view>
						</view>
						<view :class="'cptab_text ' + (st == 1 ? 'cptab_current' : '')" @tap="changetab" data-st="1">
							{{ lang("store evaluation") }}({{ countcomment }})<view class="after"
								:style="{ background: t('color1') }"></view>
						</view>
						<view :class="'cptab_text ' + (st == 2 ? 'cptab_current' : '')" @tap="changetab" data-st="2">{{ lang("business details")
              }}
							<view class="after" :style="{ background: t('color1') }"></view>
						</view>
					</view>

					<view class="cp_detail" v-if="st == -1" style="padding-top: 20rpx">
						<view class="classify-ul" v-if="yuyue_clist.length > 0">
							<view class="flex" style="width: 100%; overflow-y: hidden; overflow-x: scroll">
								<view class="classify-li" :style="
                    yuyue_cid == 0
                      ? 'color:' +
                        t('color1') +
                        ';background:rgba(' +
                        t('color1rgb') +
                        ',0.2)'
                      : ''
                  " @tap="changeyuyueCTab" :data-id="0">{{ lang("all") }}</view>
								<block v-for="(item, idx2) in yuyue_clist" :key="idx2">
									<view class="classify-li" :style="
                      yuyue_cid == item.id
                        ? 'color:' +
                          t('color1') +
                          ';background:rgba(' +
                          t('color1rgb') +
                          ',0.2)'
                        : ''
                    " @tap="changeyuyueCTab" :data-id="item.id">{{ item.name }}</view>
								</block>
							</view>
						</view>

						<dp-yuyue-itemlist :data="datalist" :menuindex="menuindex"></dp-yuyue-itemlist>

						<nomore v-if="nomore"></nomore>
						<nodata v-if="nodata"></nodata>
					</view>

					<view class="cp_detail" v-if="st == 0" style="padding-top: 20rpx">
						<dp-product-itemlist :data="datalist" :menuindex="menuindex"></dp-product-itemlist>

						<nomore v-if="nomore"></nomore>
						<nodata v-if="nodata"></nodata>
					</view>

					<view class="cp_detail" v-if="st == 1">
						<view class="comment">
							<block v-if="datalist.length > 0">
								<view v-for="(item, index) in datalist" :key="index" class="item">
									<view class="f1">
										<image class="t1" :src="item.headimg" />
										<view class="t2">{{ item.nickname }}</view>
										<view class="flex1"></view>
										<view class="t3">
											<image class="img" v-for="(item2, index2) in [0, 1, 2, 3, 4]" :key="index2"
												:src="
                          '/static/img/star' +
                          (item.score > item2 ? '2' : '') +
                          '.png'
                        " />
										</view>
									</view>
									<view style="color: #777; font-size: 22rpx">{{
                    item.createtime
                  }}</view>
									<view class="f2">
										<text class="t1">{{ item.content }}</text>
										<view class="t2">
											<block v-if="item.content_pic != ''">
												<block v-for="(itemp, index) in item.content_pic" :key="index">
													<view @tap="previewImage" :data-url="itemp"
														:data-urls="item.content_pic">
														<image :src="itemp" mode="widthFix" />
													</view>
												</block>
											</block>
										</view>
									</view>
									<view class="f3" v-if="item.reply_content">
										<view class="arrow"></view>
										<view class="t1">{{ lang("reply from merchant") }}：{{
                        item.reply_content
                      }}</view>
									</view>
								</view>
							</block>
							<block v-else>
								<nodata v-show="nodata"></nodata>
							</block>
						</view>
					</view>
					<view class="cp_detail" v-if="st == 2" style="padding: 20rpx">
						<text style="font-size: 32rpx; color: #333; line-height: 2">{{
              business.content
            }}</text>
					</view>
				</view>
				<view v-if="couponcount > 0" class="covermy" @tap="goto"
					:data-url="'/pages/coupon/couponlist?bid=' + business.id">
					<text style="padding: 0 4rpx; height: 36rpx; line-height: 36rpx">{{
            lang("merchant")
          }}</text>
					<text style="padding: 0 4rpx; height: 36rpx; line-height: 36rpx">{{
            t("优惠券")
          }}</text>
				</view>
			</view>
		</block>
		<loading v-if="loading"></loading>
		<dp-tabbar :opt="opt" @getmenuindex="getmenuindex"></dp-tabbar>
		<popmsg ref="popmsg"></popmsg>
	</view>
</template>

<script>
	var app = getApp();
	export default {
		data() {
			return {
				jmstr: "简码说明：< AA自提点编码 >,  002  用户ID ,  L 陆运",
				opt: {},
				loading: false,
				isload: false,
				menuindex: -1,
				isdiy: 0,
				st: 0,
				business: [],
				countcomment: 0,
				couponcount: 0,
				pics: [],
				pagenum: 1,
				datalist: [],
				topbackhide: false,
				nomore: false,
				nodata: false,

				title: "",
				sysset: "",
				guanggaopic: "",
				guanggaourl: "",
				pageinfo: "",
				pagecontent: [],
				showfw: false,
				yuyue_clist: [],
				yuyue_cid: 0,
				logisticstypeList: [{
						value: "",
						label: "海运公斤(H)",
					},
					{
						value: "",
						label: "陆运公斤(L)",
					},
					{
						value: "",
						label: "海运立方(Y)",
					},
					{
						value: "",
						label: "陆运立方(Z)",
					},
				],
				codeName: "",
				postaladdressarr: [],
				postaladdress: "",
				logisticstype: -1,
				result: false,
			};
		},

		onLoad: function(opt) {
			console.log('参数：-——————————>', opt);
			this.opt = app.getopts(opt);
			this.opt.bid = this.opt.id;
			this.st = this.opt.st || 0;
			console.log("接受的参数", this.opt);
			if (opt.code) {
				this.result = true;
				var arr = opt.code.split(",");
				for (var i = 0; i < arr.length; i++) {
					this.logisticstypeList[i].value = arr[i];
				}
			}
			if (opt.postaladdress) {
				this.postaladdressarr = opt.postaladdress.split(",");
			}
			this.getdata();
			uni.setNavigationBarTitle({
				title: app.lang("business details"),
				success() {
					console.log(app.lang("business details"));
				},
			});
		},
		onShow() {
			console.log("商家进入页面onShow");
			this.getdata();
		},
		onPullDownRefresh: function() {
			this.getdata();
		},
		onReachBottom: function() {
			if (this.isdiy == 0) {
				if (!this.nodata && !this.nomore) {
					this.pagenum = this.pagenum + 1;
					this.getDataList(true);
				}
			}
		},
		onShareAppMessage: function() {
			return this._sharewx({
				title: this.business.name
			});
		},
		onPageScroll: function(e) {
			if (this.isdiy == 0) {
				var that = this;
				var scrollY = e.scrollTop;
				if (scrollY > 200 && !that.topbackhide) {
					that.topbackhide = true;
				}
				if (scrollY < 150 && that.topbackhide) {
					that.topbackhide = false;
				}
			}
		},
		methods: {
			previewImage() {
				console.log('xxxx');
			},
			getLange() {
				let language = uni.getStorageSync("mylang");
				console.log("language------------->", language);
				if (language == "zh_cn") {
					return "中文";
				} else if (language == "zh_tw") {
					return "中文（繁体）";
				} else if (language == "en") {
					return "English";
				} else if (language == "vnm") {
					return "Tiếng Việt";
				} else if (language == "tha") {
					return "ภาษาไทย";
				} else if (language == "in") {
					return "भारत";
				} else if (language == "my") {
					return "Malay";
				}
			},
			tochange() {
				app.goto("/pages/index/changelang?type=3");
			},
			copy() {
				console.log("copy");
				let str =
					"收件人:" +
					this.codeName +
					",手机号码:" +
					this.opt.phonenum +
					",邮编:" +
					this.opt.postalcode +
					",收件地址:" +
					this.postaladdress;
				console.log(str);

				uni.setClipboardData({
					data: str, //要被复制的内容
					success: () => {
						//复制成功的回调函数
						uni.showToast({
							//提示
							title: "复制成功",
						});

						app.post(
							"/ApiIndex/save_choose", {
								code: this.codeName,
							},
							function(res) {
								console.log(res);
							}
						);
					},
				});
			},
			//选择运输方式
			setlogisticstype(index, val) {
				this.codeName = val.value;
				// console.log(val.value);

				if (this.codeName) {
					this.logisticstype = index;
					this.postaladdress = this.postaladdressarr[index];
					app.post("ApiBusiness/index", {
						code: this.codeName
					}, function(res) {
						if (res.status == 1) {
							// app.alert(res.msg);
						}
					});
				} else {
					app.alert("请选择自提点！");
				}
			},
			setDefaultAddress() {},
			//选择自提仓库
			selectAddress() {
				uni.redirectTo({
					url: "/pages/order/warehouse?id=" + this.opt.id,
				});
			},
			lang: function(k) {
				return app.lang(k);
			},
			getdata: function() {
				app.post("/ApiIndex/save_choose", {}, function(res) {
					console.log("data", res);
				});

				var that = this;
				var id = that.opt.id || 0;
				that.loading = true;
				app.get("ApiBusiness/index", {
					id: id
				}, function(res) {
					that.loading = false;
					that.isdiy = res.isdiy;
					that.business = res.business;
					that.countcomment = res.countcomment;
					that.couponcount = res.couponcount;
					that.pics = res.pics;
					that.guanggaopic = res.guanggaopic;
					that.guanggaourl = res.guanggaourl;
					that.pageinfo = res.pageinfo;
					that.pagecontent = res.pagecontent;
					for (var i = 0; i < that.pagecontent.length; i++) {
						for (var j = 0; j < that.pagecontent[i].data.length; j++) {
							if (that.pagecontent[i].data[j].id === "F0000000000005") {
								// that.pagecontent[i].data[j].hrefurl='pages/forecast/forecast';
							} else {
								//   that.pagecontent[i].data[j].hrefurl='pages/order/index';
							}
						}
					}
					that.sysset = res.sysset;
					that.showfw = res.showfw || false;
					if (that.showfw) {
						that.st = -1;
						that.yuyue_clist = res.yuyue_clist;
					}

					that.loaded({
						title: that.business.name,
						pic: that.business.logo
					});
					console.log('res.isdiy', res.isdiy);
					if (res.isdiy == 0) {
						that.isload = 1;
						uni.setNavigationBarTitle({
							title: that.business.name,
						});
						that.getDataList();
					} else {
						if (res.status == 2) {
							//付费查看
							app.goto(
								"/pages/pay/pay?fromPage=index&id=" +
								res.payorderid +
								"&pageid=" +
								that.res.id,
								"redirect"
							);
							return;
						}
						if (res.status == 1) {
							var pagecontent = res.pagecontent;
							that.isdiy = 1;

							that.title = res.pageinfo.title;
							that.sysset = res.sysset;
							that.guanggaopic = res.guanggaopic;

							that.guanggaourl = res.guanggaourl;
							that.pageinfo = res.pageinfo;
							//console.log('that.pageinfo', that.pageinfo);
							that.pagecontent = res.pagecontent;
							//console.log('that.pagecontent', that.pagecontent);
							uni.setNavigationBarTitle({
								title: res.pageinfo.title,
							});
						} else {
							app.alert(res.msg);
						}
					}
				});
			},
			tomailist: function() {
				app.goto("pages/maidan/maidanlog");
			},
			tobuy: function() {
				app.goto("pages/maidan/pay?id=" + this.opt.bid);
			},
			changetab: function(e) {
				var st = e.currentTarget.dataset.st;
				this.pagenum = 1;
				this.st = st;
				this.datalist = [];
				uni.pageScrollTo({
					scrollTop: 0,
					duration: 0,
				});
				this.getDataList();
			},
			getDataList: function(loadmore) {
				if (!loadmore) {
					this.pagenum = 1;
					this.datalist = [];
				}
				var that = this;
				var pagenum = that.pagenum;
				var st = that.st;
				that.loading = true;
				that.nodata = false;
				that.nomore = false;
				app.post(
					"ApiBusiness/getdatalist", {
						id: that.business.id,
						st: st,
						pagenum: pagenum,
						yuyue_cid: that.yuyue_cid,
					},
					function(res) {
						// console.log('付款数据：',data[0].payorderid);

						that.loading = false;
						uni.stopPullDownRefresh();
						var data = res.data;
						if (pagenum == 1) {
							that.datalist = data;
							if (data.length == 0) {
								that.nodata = true;
							}
						} else {
							if (data.length == 0) {
								that.nomore = true;
							} else {
								var datalist = that.datalist;
								var newdata = datalist.concat(data);
								that.datalist = newdata;
							}
						}
					}
				);
			},
			openLocation: function(e) {
				//console.log(e)
				var latitude = parseFloat(e.currentTarget.dataset.latitude);
				var longitude = parseFloat(e.currentTarget.dataset.longitude);
				var address = e.currentTarget.dataset.address;
				uni.openLocation({
					latitude: latitude,
					longitude: longitude,
					name: address,
					scale: 13,
				});
			},
			phone: function(e) {
				var phone = e.currentTarget.dataset.phone;
				uni.makePhoneCall({
					phoneNumber: phone,
					fail: function() {},
				});
			},
			//改变子分类
			changeyuyueCTab: function(e) {
				var that = this;
				var id = e.currentTarget.dataset.id;
				this.nodata = false;
				this.yuyue_cid = id;
				this.pagenum = 1;
				this.datalist = [];
				this.nomore = false;
				this.getDataList();
			},
		},
	};
</script>

<!-- //这里我写的 -->
<style>
	.transport {
		width: 90%;
		margin: 0 auto;
		padding: 20rpx;
		font-size: 25rpx;
		background-color: #d1e0e0;
	}

	.transport-contents {
		text-align: center;
		line-height: 200rpx;
	}

	.transport-tab {
		display: flex;
		justify-content: space-between;
		margin-top: 20rpx;
		margin-bottom: 20rpx;
	}

	.tab-item {
		border: 1px solid #757575;
		padding: 7rpx;
		border-radius: 5rpx;
		/* line-height: 50rpx; */
	}

	.tab-item-select {
		padding: 7rpx;
		font-size: 25rpx;
		background: #088ffb;
		border-radius: 10rpx;
		color: white;
		border: 1px solid #088ffb;
	}

	.content-item {
		line-height: 40rpx;
	}

	.transport-b {
		margin-top: 10rpx;
		display: flex;
		justify-content: center;
	}

	#zt-btn {
		border: 1px solid #088ffb;
		width: 50%;
		margin-right: 20rpx;
		color: #088ffb;
	}

	#copy-btn {
		background: #088ffb;
		width: 50%;
		color: #ececec;
	}

	.orderlist {
		width: 90%;
		margin: 0 auto;
		background-color: #ffffff;
	}

	.tips {
		font-size: 25rpx;
		border-top: 1px solid #d4d4d4;
		width: 90%;
		margin: 0 auto;
		line-height: 50rpx;
	}
</style>

<style>
	.container {
		position: relative;
	}

	.nodiydata {
		display: flex;
		flex-direction: column;
	}

	.nodiydata .swiper {
		width: 100%;
		height: 400rpx;
		position: relative;
		z-index: 1;
	}

	.nodiydata .swiper .image {
		width: 100%;
		height: 400rpx;
		overflow: hidden;
	}

	.nodiydata .topcontent {
		width: 94%;
		margin-left: 3%;
		padding: 24rpx;
		border-bottom: 1px solid #eee;
		margin-bottom: 20rpx;
		background: #fff;
		margin-top: -120rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		border-radius: 16rpx;
		position: relative;
		z-index: 2;
	}

	.nodiydata .topcontent .logo {
		width: 160rpx;
		height: 160rpx;
		margin-top: -104rpx;
		border: 2px solid rgba(255, 255, 255, 0.5);
		border-radius: 50%;
	}

	.nodiydata .topcontent .logo .img {
		width: 100%;
		height: 100%;
		border-radius: 50%;
	}

	.nodiydata .topcontent .title {
		color: #222222;
		font-size: 36rpx;
		font-weight: bold;
		margin-top: 12rpx;
	}

	.nodiydata .topcontent .desc {
		display: flex;
		align-items: center;
	}

	.nodiydata .topcontent .desc .f1 {
		margin: 20rpx 0;
		font-size: 24rpx;
		color: #fc5648;
		display: flex;
		align-items: center;
	}

	.nodiydata .topcontent .desc .f1 .img {
		width: 24rpx;
		height: 24rpx;
		margin-right: 10rpx;
	}

	.nodiydata .topcontent .desc .f2 {
		margin: 10rpx 0;
		padding-left: 60rpx;
		font-size: 24rpx;
		color: #999;
	}

	.nodiydata .topcontent .tel {
		font-size: 28rpx;
		color: #fff;
		padding: 16rpx 40rpx;
		border-radius: 60rpx;
		font-weight: normal;
		min-width: 230rpx;
		text-align: center;
	}

	.nodiydata .topcontent .tel .img {
		width: 28rpx;
		height: 28rpx;
		vertical-align: middle;
		margin-right: 10rpx;
	}

	.nodiydata .topcontent .address {
		width: 100%;
		display: flex;
		align-items: center;
		padding-top: 20rpx;
	}

	.nodiydata .topcontent .address .f1 {
		width: 28rpx;
		height: 28rpx;
		margin-right: 8rpx;
	}

	.nodiydata .topcontent .address .f2 {
		flex: 1;
		color: #999999;
		font-size: 26rpx;
	}

	.nodiydata .topcontent .address .f3 {
		display: inline-block;
		width: 26rpx;
		height: 26rpx;
	}

	.nodiydata .contentbox {
		width: 94%;
		margin-left: 3%;
		background: #fff;
		border-radius: 16rpx;
		margin-bottom: 32rpx;
		overflow: hidden;
	}

	.nodiydata .shop_tab {
		display: flex;
		width: 100%;
		height: 90rpx;
		border-bottom: 1px solid #eee;
	}

	.nodiydata .shop_tab .cptab_text {
		flex: 1;
		text-align: center;
		color: #646566;
		height: 90rpx;
		line-height: 90rpx;
		position: relative;
	}

	.nodiydata .shop_tab .cptab_current {
		color: #323233;
	}

	.nodiydata .shop_tab .after {
		display: none;
		position: absolute;
		left: 50%;
		margin-left: -16rpx;
		bottom: 10rpx;
		height: 3px;
		border-radius: 1.5px;
		width: 32rpx;
	}

	.nodiydata .shop_tab .cptab_current .after {
		display: block;
	}

	.nodiydata .cp_detail {
		min-height: 500rpx;
	}

	.nodiydata .comment .item {
		background-color: #fff;
		padding: 10rpx 20rpx;
		display: flex;
		flex-direction: column;
	}

	.nodiydata .comment .item .f1 {
		display: flex;
		width: 100%;
		align-items: center;
		padding: 10rpx 0;
	}

	.nodiydata .comment .item .f1 .t1 {
		width: 70rpx;
		height: 70rpx;
		border-radius: 50%;
	}

	.nodiydata .comment .item .f1 .t2 {
		padding-left: 10rpx;
		color: #333;
		font-weight: bold;
		font-size: 30rpx;
	}

	.nodiydata .comment .item .f1 .t3 {
		text-align: right;
	}

	.nodiydata .comment .item .f1 .t3 .img {
		width: 24rpx;
		height: 24rpx;
		margin-left: 10rpx;
	}

	.nodiydata .comment .item .score {
		font-size: 24rpx;
		color: #f99716;
	}

	.nodiydata .comment .item .score image {
		width: 140rpx;
		height: 50rpx;
		vertical-align: middle;
		margin-bottom: 6rpx;
		margin-right: 6rpx;
	}

	.nodiydata .comment .item .f2 {
		display: flex;
		flex-direction: column;
		width: 100%;
		padding: 10rpx 0;
	}

	.nodiydata .comment .item .f2 .t1 {
		color: #333;
		font-size: 28rpx;
	}

	.nodiydata .comment .item .f2 .t2 {
		display: flex;
		width: 100%;
	}

	.nodiydata .comment .item .f2 .t2 image {
		width: 100rpx;
		height: 100rpx;
		margin: 10rpx;
	}

	.nodiydata .comment .item .f2 .t3 {
		color: #aaa;
		font-size: 24rpx;
	}

	.nodiydata .comment .item .f2 .t3 {
		color: #aaa;
		font-size: 24rpx;
	}

	.nodiydata .comment .item .f3 {
		width: 100%;
		padding: 10rpx 0;
		position: relative;
	}

	.nodiydata .comment .item .f3 .arrow {
		width: 16rpx;
		height: 16rpx;
		background: #eee;
		transform: rotate(45deg);
		position: absolute;
		top: 0rpx;
		left: 36rpx;
	}

	.nodiydata .comment .item .f3 .t1 {
		width: 100%;
		border-radius: 10rpx;
		padding: 10rpx;
		font-size: 22rpx;
		color: #888;
		background: #eee;
	}

	.nodiydata .nomore-footer-tips {
		background: #fff !important;
	}

	.nodiydata .covermy {
		position: fixed;
		z-index: 99999;
		cursor: pointer;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		overflow: hidden;
		z-index: 9999;
		top: 81vh;
		left: 82vw;
		color: #fff;
		background-color: rgba(92, 107, 129, 0.6);
		width: 110rpx;
		height: 110rpx;
		font-size: 26rpx;
		border-radius: 50%;
	}

	.classify-ul {
		width: 100%;
		height: 70rpx;
		padding: 0 10rpx;
	}

	.classify-li {
		flex-shrink: 0;
		display: flex;
		background: #f5f6f8;
		border-radius: 22rpx;
		color: #6c737f;
		font-size: 20rpx;
		text-align: center;
		height: 44rpx;
		line-height: 44rpx;
		padding: 0 28rpx;
		margin: 12rpx 10rpx 12rpx 0;
	}

	.boxtel {
		float: left !important;
		margin-right: 15rpx;
	}

	.mini-btn-g {
		background-color: #aaa;
	}

	.changebox {
		/* position: absolute; */
		display: flex;
		justify-content: space-between;
		right: 30rpx;
		/* top:8rpx; */
		text-align: right;
		padding: 15px;
		color: #8a8a8a;
	}

	.imgset {
		width: 15px;
		height: 15px;
		vertical-align: -0.3em;
		margin-left: 0.1em;
	}
</style>
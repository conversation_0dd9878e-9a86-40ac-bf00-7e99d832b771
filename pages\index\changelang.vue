<template>
	<view>
		<view class="info-item" @tap="changelang('zh_cn')" data-url="setnickname">
			<view class="t1">中文</view>
			<view class="t2">CNY</view>
			<image class="t3" src="/static/img/arrowright.png" />
		</view>
		<view class="info-item" @tap="changelang('zh_tw')" data-url="setnickname">
			<view class="t1">中文（繁体）</view>
			<view class="t2">TWD</view>
			<image class="t3" src="/static/img/arrowright.png" />
		</view>
		<view class="info-item" @tap="changelang('en')" data-url="setnickname">
			<view class="t1">English</view>
			<view class="t2">USD</view>
			<image class="t3" src="/static/img/arrowright.png" />
		</view>
		<view class="info-item" @tap="changelang('vnm')" data-url="setnickname">
			<view class="t1">Tiếng Việt</view>
			<view class="t2">VND</view>
			<image class="t3" src="/static/img/arrowright.png" />
		</view>
		<view class="info-item" @tap="changelang('tha')" data-url="setnickname">
			<view class="t1">ภาษาไทย</view>
			<view class="t2">THB</view>
			<image class="t3" src="/static/img/arrowright.png" />
		</view>
		<view class="info-item" @tap="changelang('in')" data-url="setnickname">
			<view class="t1">भारत</view>
			<view class="t2">INR</view>
			<image class="t3" src="/static/img/arrowright.png" />
		</view>
		<view class="info-item" @tap="changelang('my')" data-url="setnickname">
			<view class="t1">Malay</view>
			<view class="t2">MYR</view>
			<image class="t3" src="/static/img/arrowright.png" />
		</view>
	</view>
</template>

<script>
	var app = getApp();
	export default {
		data() {
			return {
				opt: {},
			}
		},
		onLoad: function(opt) {
			this.opt = app.getopts(opt);
			console.log(opt)
		},
		methods: {
			lang: function(k) {
				return app.lang(k);
			},
			changelang(lang) {
				console.log(111)
				app.changelangs(lang, this.opt.type)
			},
		}
	}
</script>

<style>
	.content {
		width: 94%;
		margin: 20rpx 3%;
		background: #fff;
		border-radius: 5px;
		padding: 0 20rpx;
	}

	.info-item {
		display: flex;
		align-items: center;
		width: 100%;
		background: #fff;
		padding: 0 3%;
		border-bottom: 1px #f3f3f3 solid;
		height: 96rpx;
		line-height: 96rpx;
	}

	.info-item:last-child {
		border: none;
	}

	.info-item .t1 {
		width: 300rpx;
		color: #8b8b8b;
		font-weight: bold;
		height: 96rpx;
		line-height: 96rpx;
	}

	.info-item .t2 {
		color: #8b8b8b;
		text-align: right;
		flex: 1;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 1;
		overflow: hidden;
	}

	.info-item .t3 {
		width: 26rpx;
		height: 26rpx;
		margin-left: 20rpx;
	}
</style>

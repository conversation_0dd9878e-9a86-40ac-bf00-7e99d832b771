<template>
  <view class="container">
    <form report-submit="true" @submit="subconfirm" style="width:100%">
      <div class="title">{{lang('please enter the exchange yards to exchange')}}</div>
      <div class="inputdiv"><input id="dhcode" type="text" name="dhcode" value="" placeholder-style="color:#666;"
          :placeholder="lang('please enter the exchange yards to exchange')"></input></div>

      <button class="btn" form-type="submit">{{lang('immediately change"')}}</button>
      <view class="f0" @tap="goto" data-url="/pages/lipin/dhlog"><text>{{lang('check the change record')}}</text></view>
    </form>
    <loading v-if="loading"></loading>
    <dp-tabbar :opt="opt"></dp-tabbar>
    <popmsg ref="popmsg"></popmsg>
  </view>
</template>

<script>
var app = getApp();

export default {
  data () {
    return {
      opt: {},
      loading: false,
      isload: false,
      menuindex: -1,

      userinfo: [],
      money: '',
      moneyduan: 0
    };
  },

  onLoad: function (opt) {
    this.opt = app.getopts(opt);
    this.getdata();
	uni.setNavigationBarTitle({
		title:app.lang("exchange center"),
		success() {
			console.log(app.lang("exchange center"))
		}
	})
  },
  onPullDownRefresh: function () {
    this.getdata();
  },
  onLoad: function (opt) {
    var that = this;
  },
  methods: {
	  lang: function(k) {
	  	return app.lang(k);
	  },
    getdata: function () {

    },
    subconfirm: function (e) {
      var that = this;
      var dhcode = e.detail.value.dhcode;
      that.loading = true;
      app.post('ApiLipin/index', { dhcode: dhcode }, function (res) {
        that.loading = false;
        if (res.status == 0) {
          app.error(res.msg);
          return;
        }
        if (res.status == 1) {
          app.alert(res.msg, function () {
            app.goto('/myshop/my/usercenter');
          });
        }
        if (res.status == 2) {
          app.success(res.msg);
          setTimeout(function () {
            app.goto('/pages/lipin/prodh?dhcode=' + dhcode);
          }, 1000);
        }
        if (res.status == 3) {
          app.alert(res.msg, function () {
            app.goto('/pages/coupon/mycoupon');
          });
        }
        that.loaded();
      });
    }
  }
}
</script>
<style>
.container {
  display: flex;
  flex-direction: column;
}
.container .title {
  display: flex;
  justify-content: center;
  width: 100%;
  color: #555;
  font-size: 40rpx;
  text-align: center;
  height: 100rpx;
  line-height: 100rpx;
  margin-top: 60rpx;
}
.container .inputdiv {
  display: flex;
  width: 90%;
  margin: 0 auto;
  margin-top: 40rpx;
  margin-bottom: 40rpx;
}
.container .inputdiv input {
  background: #fff;
  width: 100%;
  height: 120rpx;
  line-height: 120rpx;
  padding: 0 40rpx;
  font-size: 40rpx;
  border: 1px solid #f5f5f5;
  border-radius: 20rpx;
}
.container .btn {
  height: 88rpx;
  line-height: 88rpx;
  background: #fc4343;
  width: 90%;
  margin: 0 auto;
  border-radius: 8rpx;
  margin-top: 60rpx;
  color: #fff;
  font-size: 36rpx;
}
.container .f0 {
  width: 100%;
  margin-top: 40rpx;
  height: 60rpx;
  line-height: 60rpx;
  color: #fc4343;
  font-size: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
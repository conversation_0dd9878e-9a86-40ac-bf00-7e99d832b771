<template>
  <view>
    <view class="address">
      <view class="address-left">
        仓库地址
      </view>
      <view class="address-right">
        <picker @change="bindPickerChange" :value="index" :range="address" range-key="address">
          <view class="uni-input">
            {{address[index].address}}
            <image style="width: 15px; height: 15px; margin-left: 10px" src="/static/img/arrowdown.png"></image>
          </view>
        </picker>
      </view>
    </view>

    <view>
      <view class="title">运输方式</view>
      <view>
        <radio-group @change="transportChange" style="display: flex; margin-left: 20px">
          <label v-for="(item, index) in transportList" :key="index"
                 :class="index === transportCurrent? 'radio-checked radio-btn' : 'radio-unchecked radio-btn'">
            <view class="radio-hidden">
              <radio :value="item.value" :checked="index === transportCurrent" />
            </view>
            <view>{{item.name}}</view>
          </label>
        </radio-group>
      </view>
    </view>

    <view>
      <view class="title">物品属性</view>
      <view>
        <radio-group @change="goodsChange" style="display: flex; margin-left: 20px">
          <label v-for="(item, index) in goodsList" :key="index"
                 :class="index === goodsCurrent? 'radio-checked radio-btn' : 'radio-unchecked radio-btn'">
            <view class="radio-hidden">
              <radio :value="item.value" :checked="index === goodsCurrent" />
            </view>
            <view>{{item.name}}</view>
          </label>
        </radio-group>
      </view>
    </view>

    <view>
      <view class="title">重量（单位KG）</view>
      <view>
        <input class="weight-input" @input="weightInput" type="digit" placeholder="输入重量" />
      </view>
    </view>

    <view>
      <view class="title">包裹尺寸（选填）</view>
      <view style="display: flex; margin-top: 10px; margin-left: 20px">
        <input class="size-input" @input="lInput" type="digit" placeholder="长：CM" />
        <input class="size-input" @input="wInput" type="digit" placeholder="宽：CM" />
        <input class="size-input" @input="hInput" type="digit" placeholder="高：CM" />
      </view>
    </view>

    <view>
      <view class="title">预估费用:<text style="font-weight: bold; font-size: 32px; margin-left: 5px">{{price}}元</text></view>
    </view>

    <view style="margin: 30px 20px 0 20px">
      <button class="query-btn" @click="getPrice">立即查询</button>
    </view>

  </view>
</template>

<script>
var app = getApp();
export default {
  data() {
    return {
      title: 'picker',
      address: [],
      index: 0,
      transportList: [
        { name: '海运公斤', value: '海运公斤'},
        { name: '陆运公斤', value: '陆运公斤'},
        { name: '海运立方', value: '海运立方'},
        { name: '陆运立方', value: '陆运立方'}
      ],
      transportCurrent: 0,
      goodsList: [
        { name: '普货', value: '普货'},
        { name: '敏感货', value: '敏感货'},
        { name: '酒', value: '酒'},
        { name: '特货', value: '特货'},
        { name: 'YAN', value: 'YAN'},
      ],
      goodsCurrent: 0,
      transport: "海运公斤",
      goods: "普货",
      weight: null,
      l: null,
      w: null,
      h: null,
      price: 0
    };
  },

  onLoad() {
    this.getAddress();
  },

  methods: {
    getPrice: function (){
      var that = this;
      let param = {
        weight: this.weight,
        length: this.l,
        width: this.w,
        height: this.h,
        id: this.address[this.index].id,
        transport: this.transport,
        goodstype: this.goods
      }
      app.post('ApiWuliu/estimate', param, function(res) {
        if (res.status === 1){
          that.price = res.total;
        }else {
          that.price = 0;
        }

      })
    },

    getAddress: function (){
      var that = this;
      app.get('ApiIndex/mendian', {}, function(res) {
        for(var i=0;i<res.data.length;i++){
          that.address.push({
            id: res.data[i].id,
            address: res.data[i].address
          })
        }

      })
    },

    bindPickerChange: function(e) {
      this.index = e.detail.value
      this.getPrice();
    },

    transportChange: function (e) {
      for (let i = 0; i < this.transportList.length; i++) {
        if (this.transportList[i].value === e.detail.value) {
          this.transportCurrent = i;
          break;
        }
      }
      this.transport = e.detail.value;
      this.getPrice();
    },

    goodsChange: function (e) {
      for (let i = 0; i < this.goodsList.length; i++) {
        if (this.goodsList[i].value === e.detail.value) {
          this.goodsCurrent = i;
          break;
        }
      }
      this.goods = e.detail.value;
      this.getPrice();
    },

    weightInput: function (e){
      this.weight = e.target.value
      this.getPrice();
    },

    lInput: function (e){
      this.l = e.target.value
      this.getPrice();
    },

    wInput: function (e){
      this.w = e.target.value
      this.getPrice();
    },

    hInput: function (e){
      this.h = e.target.value
      this.getPrice();
    },
  },
};
</script>

<style lang="scss">
.address{
  margin-top: 20px;
  display: flex;
  justify-content: space-between;
}
.address-left{
  padding-left: 20px;
  width: 30%;
  height: 50px;
}
.address-right{
  text-align: right;
  padding-right: 20px;
  width: 70%;
  height: 50px;
}

.title{
  margin-top: 10px;
  margin-left: 20px;
}

.radio-hidden{
  display: none;
}

.radio-btn{
  display: flex;
  background: #f1f1f1;
  font-size: 30rpx;
  color: black;
  width: 80px;
  height: 40px;
  margin-top: 10px;
  margin-right: 10px;
  align-items: center;
  justify-content: center;
  border: 1px solid #eee;
}

.radio-checked{
  background: dodgerblue;
  color: white;
}

.radio-unchecked{
  background: #f1f1f1;
  color: black;
}

.weight-input{
  margin-left: 20px;
  margin-top: 10px;
  width: 150px;
  height: 50px;
  border: 1px solid silver;
  text-align: center;
}

.size-input{
  display: flex;
  width: 100px;
  height: 50px;
  border: 1px solid silver;
  text-align: center;
  margin-right: 10px;
}

.query-btn{
  color:#ffffff;
  background-color:dodgerblue;
  border-color:dodgerblue;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10px
}
</style>

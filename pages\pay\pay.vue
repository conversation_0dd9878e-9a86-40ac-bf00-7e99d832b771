<template>
	<view class="container">
		<block v-if="isload">
			<view class="top">
				<view class="f1">{{lang('to pay the amount')}}</view>
				<view class="f2" v-if="payorder.score==0"><text class="t1">{{payorder.currency}}</text><text
						class="t2">{{payorder.money}}</text></view>
				<view class="f2" v-else-if="payorder.money>0 && payorder.score>0"><text class="t1"></text><text
						class="t2">{{payorder.money}}</text><text style="font-size:28rpx"> +
						{{payorder.score}}{{t('积分')}}</text></view>
				<view class="f2" v-else><text class="t3">{{payorder.score}}{{t('积分')}}</text></view>
				<view class="f3" @tap="goto" :data-url="detailurl" v-if="detailurl!=''">
					{{lang("the order details")}}<text class="iconfont iconjiantou"></text>
				</view>
			</view>
			<view class="paytype">
				<view class="f1">{{lang('choice of payment')}}：</view>
				<block v-if="payorder.money==0 && payorder.score>0">
					<view class="f2">
						<view class="item" v-if="moneypay==1" @tap.stop="changeradio" data-typeid="1">
							<view class="t1 flex">
								<image class="img" src="/static/img/pay-money.png" />
								<view class="flex-col"><text>{{t('积分')}}{{lang("pay")}}</text><text
										style="font-size:22rpx;font-weight:normal">{{lang("the remaining")}}{{t('积分')}}<text
											style="color:#FC5729">{{userinfo.score}}</text></text></view>
							</view>
							<view class="radio" :style="typeid=='1' ? 'background:'+t('color1')+';border:0' : ''">
								<image class="radio-img" src="/static/img/checkd.png" />
							</view>
						</view>
						<view class="item" v-if="moneypay==1" @tap.stop="changeradio" data-typeid="13">
							<view class="t1">
								<image class="img" src="/static/img/pay-money.png" />Online Payment
							</view>
							<view class="radio" :style="typeid=='13' ? 'background:'+t('color1')+';border:0' : ''">
								<image class="radio-img" src="/static/img/checkd.png" />
							</view>
						</view>
					</view>
				</block>
				<block v-else>
					<view class="f2">
						<view class="item" v-if="wxpay==1 && (wxpay_type==0 || wxpay_type==1 || wxpay_type==3)"
							@tap.stop="changeradio" data-typeid="2">
							<view class="t1">
								<image class="img" src="/static/img/withdraw-weixin.png" />{{lang("wechat pay")}}
							</view>
							<view class="radio" :style="typeid=='2' ? 'background:'+t('color1')+';border:0' : ''">
								<image class="radio-img" src="/static/img/checkd.png" />
							</view>
						</view>
						<view class="item" v-if="wxpay==1 && wxpay_type==2" @tap.stop="changeradio" data-typeid="22">
							<view class="t1">
								<image class="img" src="/static/img/withdraw-weixin.png" />{{lang("wechat pay")}}
							</view>
							<view class="radio" :style="typeid=='22' ? 'background:'+t('color1')+';border:0' : ''">
								<image class="radio-img" src="/static/img/checkd.png" />
							</view>
						</view>

						<view class="item" v-if="alipay==2" @tap.stop="changeradio" data-typeid="23">
							<view class="t1">
								<image class="img" src="/static/img/withdraw-alipay.png" />
								{{lang("pay treasure to pay")}}
							</view>
							<view class="radio" :style="typeid=='23' ? 'background:'+t('color1')+';border:0' : ''">
								<image class="radio-img" src="/static/img/checkd.png" />
							</view>
						</view>

						<!-- 	<view class="item" @tap.stop="changeradio" data-typeid="14">
							<view class="t1">
								<image class="img" src="/static/img/pay-money.png" />Ksher Payment
							</view>
							<view class="radio" :style="typeid=='14' ? 'background:'+t('color1')+';border:0' : ''">
								<image class="radio-img" src="/static/img/checkd.png" />
							</view>
						</view> -->
						<!-- 
						<view class="item" @tap.stop="changeradio" data-typeid="13">
							<view class="t1">
								<image class="img" src="/static/img/pay-money.png" />Online Payment
							</view>
							<view class="radio" :style="typeid=='13' ? 'background:'+t('color1')+';border:0' : ''">
								<image class="radio-img" src="/static/img/checkd.png" />
							</view>
						</view> -->

						<view class="item" v-if="alipay==1" @tap.stop="changeradio" data-typeid="3">
							<view class="t1">
								<image class="img" src="/static/img/withdraw-alipay.png" />
								{{lang("pay treasure to pay")}}
							</view>
							<view class="radio" :style="typeid=='3' ? 'background:'+t('color1')+';border:0' : ''">
								<image class="radio-img" src="/static/img/checkd.png" />
							</view>
						</view>

						<block v-if="more_alipay==1">
							<view class="item" v-if="alipay2==1" @tap.stop="changeradio" data-typeid="31">
								<view class="t1">
									<image class="img" src="/static/img/withdraw-alipay.png" />
									{{lang("pay treasure to pay")}}2
								</view>
								<view class="radio" :style="typeid=='31' ? 'background:'+t('color1')+';border:0' : ''">
									<image class="radio-img" src="/static/img/checkd.png" />
								</view>
							</view>
							<view class="item" v-if="alipay3==1" @tap.stop="changeradio" data-typeid="32">
								<view class="t1">
									<image class="img" src="/static/img/withdraw-alipay.png" />
									{{lang("pay treasure to pay")}}3
								</view>
								<view class="radio" :style="typeid=='32' ? 'background:'+t('color1')+';border:0' : ''">
									<image class="radio-img" src="/static/img/checkd.png" />
								</view>
							</view>
						</block>

						<view class="item" v-if="baidupay==1" @tap.stop="changeradio" data-typeid="11">
							<view class="t1">
								<image class="img" src="/static/img/pay-money.png" />{{lang("online payment")}}
							</view>
							<view class="radio" :style="typeid=='11' ? 'background:'+t('color1')+';border:0' : ''">
								<image class="radio-img" src="/static/img/checkd.png" />
							</view>
						</view>
						<view class="item" v-if="toutiaopay==1" @tap.stop="changeradio" data-typeid="12">
							<view class="t1">
								<image class="img" src="/static/img/pay-money.png" />{{lang("online payment")}}
							</view>
							<view class="radio" :style="typeid=='12' ? 'background:'+t('color1')+';border:0' : ''">
								<image class="radio-img" src="/static/img/checkd.png" />
							</view>
						</view>
						<view class="item" v-if="moneypay==1" @tap.stop="changeradio" data-typeid="1">
							<view class="t1 flex">
								<image class="img" src="/static/img/pay-money.png" />
								<view class="flex-col"><text>{{t('余额')}}{{lang("pay")}}</text><text
										style="font-size:22rpx;font-weight:normal">{{lang("available balance")}}<text
											style="color:#FC5729">{{userinfo.money}}</text></text></view>
							</view>
							<view class="radio" :style="typeid=='1' ? 'background:'+t('color1')+';border:0' : ''">
								<image class="radio-img" src="/static/img/checkd.png" />
							</view>
						</view>
					</view>
				</block>
			</view>
			<button class="btn" @tap="topay" :style="{background:t('color1')}"
				v-if="typeid != '0'">{{lang("immediate payment")}}</button>
			<button class="btn" @tap="topay2" v-if="cancod==1"
				style="background:rgba(126,113,246,0.5);">{{codtxt}}</button>
			<button class="btn" @tap="topayTransfer" v-if="pay_transfer==1" :style="{background:t('color2')}"
				:data-text="lang('transfer the remittance')">{{lang("transfer the remittance")}}</button>

			<uni-popup id="dialogInput" ref="dialogInput" type="dialog">
				<uni-popup-dialog mode="input" :title="lang('pay the password')" value=""
					:placeholder="lang('enter the password for payment')" @confirm="getpwd"></uni-popup-dialog>
			</uni-popup>

			<block v-if="give_coupon_show">
				<view class="give-coupon flex-x-center flex-y-center">
					<view class='coupon-block'>
						<image :src="pre_url+'/static/img/coupon-top.png'" style="width:630rpx;height:330rpx;"></image>
						<view @tap="give_coupon_close" :data-url="give_coupon_close_url"
							class="coupon-del flex-x-center flex-y-center">
							<image src="/static/img/coupon-del.png"></image>
						</view>
						<view class="flex-x-center">
							<view class="coupon-info">
								<view class="flex-x-center coupon-get">
									{{lang('the cumulative gain')}}{{give_coupon_list.length}}{{t('优惠券')}}
								</view>
								<view style="background:#f5f5f5;padding:10rpx 0">
									<block v-for="(item,index) in give_coupon_list" :key="item.id">
										<block v-if="index < 3">
											<view class="coupon-coupon">
												<view :class="item.type==1?'pt_img1':'pt_img2'"></view>
												<view class="pt_left" :class="item.type==1?'':'bg2'">
													<view class="f1" v-if="item.type==1"><text class="t0"></text><text
															class="t1">{{item.money}}</text></view>
													<view class="f1" v-if="item.type==2">{{lang('gift certificate')}}
													</view>
													<view class="f1" v-if="item.type==3"><text
															class="t1">{{item.limit_count}}</text><text
															class="t2"></text></view>
													<view class="f1" v-if="item.type==4">{{lang("for the freight")}}
													</view>
													<view class="f2" v-if="item.type==1 || item.type==4">
														<text
															v-if="item.minprice>0">{{lang('full')}}{{item.minprice}}{{lang('yuan available')}}</text>
														<text v-else>{{lang("there is no threshold")}}</text>
													</view>
												</view>
												<view class="pt_right">
													<view class="f1">
														<view class="t1">{{item.name}}</view>
														<view class="t2" v-if="item.type==1">{{lang("vouchers")}}</view>
														<view class="t2" v-if="item.type==2">
															{{lang('gift certificate')}}
														</view>
														<view class="t2" v-if="item.type==3">{{lang("would stamp")}}
														</view>
														<view class="t2" v-if="item.type==4">{{lang("freight rebate")}}
														</view>
														<!-- <view class="t4" v-if="item.bid>0">适用商家：{{item.bname}}</view> -->
														<!-- <view class="t3">有效期至 {{item.yxqdate}}</view> -->
													</view>
												</view>
											</view>
										</block>
									</block>
								</view>
								<view @tap="goto" data-url="/pages/coupon/mycoupon" class="flex-x-center coupon-btn">
									前往查看</view>
							</view>
						</view>
					</view>
				</view>
			</block>
			<uni-popup id="dialogOpenWeapp" ref="dialogOpenWeapp" type="dialog" :maskClick="false">
				<view style="background:#fff;padding:50rpx;position:relative;border-radius:20rpx">
					<view
						style="height:80px;line-height:80px;width:200px;margin:0 auto;font-size: 18px;text-align:center;font-weight:bold;text-align:center;color:#333">
						{{lang("congratulations on your payment successful")}}
					</view>
					<!-- #ifdef H5 -->
					<wx-open-launch-weapp :username="payorder.payafter_username" :path="payorder.payafter_path">
						<script type="text/wxtag-template">
							<div style="background:#FD4A46;height:50px;line-height: 50px;width:200px;margin:0 auto;border-radius:5px;margin-top:15px;color: #fff;font-size: 15px;font-weight:bold;text-align:center">{{payorder.payafterbtntext}}</div>
					</script>
					</wx-open-launch-weapp>
					<!-- #endif -->
					<view
						style="height:50px;line-height: 50px;width:200px;margin:0 auto;border-radius:5px;color:#66f;font-size: 14px;text-align:center"
						@tap="goto" :data-url="detailurl">{{lang("to view the order details")}}</view>
				</view>
			</uni-popup>


			<uni-popup id="dialogPayconfirm" ref="dialogPayconfirm" type="dialog" :maskClick="false">
				<uni-popup-dialog type="info" :title="lang('payment confirmation')"
					:content="lang('Whether payment has been completed')" @confirm="PayconfirmFun"></uni-popup-dialog>
			</uni-popup>

		</block>
		<loading v-if="loading"></loading>
		<dp-tabbar :opt="opt"></dp-tabbar>
		<popmsg ref="popmsg"></popmsg>
	</view>
</template>

<script>
	var app = getApp();


	export default {
		data() {
			return {
				opt: {},
				loading: false,
				isload: false,
				menuindex: -1,
				pre_url: app.globalData.pre_url,

				detailurl: '',
				tourl: '',
				typeid: '0',
				wxpay: 0,
				wxpay_type: 0,
				alipay: 0,
				baidupay: 0,
				toutiaopay: 0,
				moneypay: 0,
				cancod: 0,
				pay_transfer: 0,
				codtxt: '',
				give_coupon_list: [],
				userinfo: [],
				paypwd: '',
				hiddenmodalput: true,
				payorder: {},
				tmplids: [],
				give_coupon_show: false,
				give_coupon_close_url: "",
				more_alipay: 0,
				alipay2: 0,
				alipay3: 0,
			};
		},

		onLoad: function(opt) {
			this.opt = app.getopts(opt);
			if (this.opt.tourl) this.tourl = decodeURIComponent(this.opt.tourl);
			this.getdata();
		},
		onPullDownRefresh: function() {
			this.getdata();
		},
		methods: {
			lang: function(k) {
				return app.lang(k);
			},
			getdata: function() {
				var that = this;
				that.loading = true;
				var thisurl = '';
				if (app.globalData.platform == 'mp' || app.globalData.platform == 'h5') {
					thisurl = location.href;
				}
				app.post('ApiPay/pay', {
					orderid: that.opt.id,
					thisurl: thisurl,
					tourl: that.tourl,
					scene: app.globalData.scene
				}, function(res) {

					that.loading = false;
					if (res.status == 0) {
						app.error(res.msg);
						return;
					}

					that.wxpay = res.wxpay;
					that.wxpay_type = res.wxpay_type;
					that.alipay = res.alipay;
					that.baidupay = res.baidupay;
					that.toutiaopay = res.toutiaopay;
					that.cancod = res.cancod;
					that.codtxt = res.codtxt;
					that.moneypay = res.moneypay;
					that.pay_transfer = res.pay_transfer;
					that.pay_transfer_info = res.pay_transfer_info;
					that.payorder = res.payorder;
					that.userinfo = res.userinfo;
					that.tmplids = res.tmplids;
					that.give_coupon_list = res.give_coupon_list;
					that.detailurl = res.detailurl;
					that.tourl = res.tourl;

					that.more_alipay = res.more_alipay;
					that.alipay2 = res.alipay2;
					that.alipay3 = res.alipay3;

					if (that.wxpay) {
						if (that.wxpay_type == 2) {
							that.typeid = 22;
						} else {
							that.typeid = 2;
						}
					} else if (that.moneypay) {
						that.typeid = 1;
					} else if (that.alipay) {
						that.typeid = 3;
						if (that.alipay == 2) {
							that.typeid = 23;
						}
					} else if (that.more_alipay) {
						if (that.alipay2) {
							that.typeid = 31;
						}
						if (that.alipay3) {
							that.typeid = 32;
						}
					} else if (that.baidupay) {
						that.typeid = 11;
					} else if (that.toutiaopay) {
						that.typeid = 12;
					}
					if (that.payorder.money == 0 && that.payorder.score > 0) {
						that.typeid = 1;
					}
					that.loaded();
				});
			},
			getpwd: function(done, val) {
				this.paypwd = val;
				this.topay({
					currentTarget: {
						dataset: {
							typeid: 1
						}
					}
				});
			},
			changeradio: function(e) {
				var that = this;
				var typeid = e.currentTarget.dataset.typeid;
				that.typeid = typeid;
				console.log(typeid)
			},
			topay: function(e) {
				var that = this;
				var typeid = that.typeid;

				var orderid = this.payorder.id;
				if (typeid == 1) { //余额支付
					if (that.userinfo.haspwd && that.paypwd == '') {
						that.$refs.dialogInput.open();
						return;
					}
					app.confirm(that.lang('identify with') + that.t('余额') + that.lang('to pay?'), function() {
						app.showLoading(that.lang('in the submission'));
						app.post('ApiPay/pay', {
							op: 'submit',
							orderid: orderid,
							typeid: typeid,
							paypwd: that.paypwd
						}, function(res) {
							app.showLoading(false);
							if (res.status == 0) {
								app.error(res.msg);
								return;
							}
							if (res.status == 2) {
								app.success(res.msg);
								that.subscribeMessage(function() {
									setTimeout(function() {
										if (that.give_coupon_list && that
											.give_coupon_list.length > 0) {
											that.give_coupon_show = true;
											that.give_coupon_close_url = that.tourl;
										} else {
											that.gotourl(that.tourl, 'reLaunch');
										}
									}, 1000);
								});
								return;
							}
						});
					});
				} else if (typeid == 2) { //微信支付
					console.log(app)
					app.showLoading(that.lang('in the submission'));
					app.post('ApiPay/pay', {
						op: 'submit',
						orderid: orderid,
						typeid: typeid
					}, function(res) {
						app.showLoading(false);
						if (res.status == 0) {
							app.error(res.msg);
							return;
						}
						if (res.status == 2) {
							//无需付款
							app.success(res.msg);
							that.subscribeMessage(function() {
								setTimeout(function() {
									if (that.give_coupon_list && that.give_coupon_list.length >
										0) {
										that.give_coupon_show = true;
										that.give_coupon_close_url = that.tourl;
									} else {
										that.gotourl(that.tourl, 'reLaunch');
									}
								}, 1000);
							});
							return;
						}
						var opt = res.data;
						if (app.globalData.platform == 'wx') {
							if (that.payorder.type == 'shop') {
								console.log('requestOrderPayment');
								wx.requestOrderPayment({
									'timeStamp': opt.timeStamp,
									'nonceStr': opt.nonceStr,
									'package': opt.package,
									'signType': 'MD5',
									'paySign': opt.paySign,
									'orderInfo': opt.orderInfo,
									'success': function(res2) {
										app.success(that.lang('payment is done'));
										that.subscribeMessage(function() {
											setTimeout(function() {
												if (that.give_coupon_list && that
													.give_coupon_list.length > 0) {
													that.give_coupon_show = true;
													that.give_coupon_close_url =
														that.tourl;
												} else {
													that.gotourl(that.tourl,
														'reLaunch');
												}
											}, 1000);
										});
									},
									'fail': function(res2) {
										//app.alert(JSON.stringify(res2))
									}
								});
							} else {
								uni.requestPayment({
									'provider': 'wxpay',
									'timeStamp': opt.timeStamp,
									'nonceStr': opt.nonceStr,
									'package': opt.package,
									'signType': 'MD5',
									'paySign': opt.paySign,
									'success': function(res2) {
										app.success(that.lang('payment is done'));
										that.subscribeMessage(function() {
											setTimeout(function() {
												if (that.give_coupon_list && that
													.give_coupon_list.length > 0) {
													that.give_coupon_show = true;
													that.give_coupon_close_url =
														that.tourl;
												} else {
													that.gotourl(that.tourl,
														'reLaunch');
												}
											}, 1000);
										});
									},
									'fail': function(res2) {
										//app.alert(JSON.stringify(res2))
									}
								});
							}
						} else if (app.globalData.platform == 'mp') {
							// #ifdef H5
							function jsApiCall() {
								WeixinJSBridge.invoke('getBrandWCPayRequest', opt, function(res) {
									if (res.err_msg == "get_brand_wcpay_request:ok") {
										app.success(that.lang('payment is done'));
										that.subscribeMessage(function() {
											setTimeout(function() {
												if (that.give_coupon_list && that
													.give_coupon_list.length > 0) {
													that.give_coupon_show = true;
													that.give_coupon_close_url = that
														.tourl;
												} else {
													that.gotourl(that.tourl,
														'reLaunch');
												}
											}, 1000);
										});
									} else {

									}
								});
							}
							if (typeof WeixinJSBridge == "undefined") {
								if (document.addEventListener) {
									document.addEventListener('WeixinJSBridgeReady', jsApiCall, false);
								} else if (document.attachEvent) {
									document.attachEvent('WeixinJSBridgeReady', jsApiCall);
									document.attachEvent('onWeixinJSBridgeReady', jsApiCall);
								}
							} else {
								jsApiCall();
							}
							// #endif
							/*
							var jweixin = require('jweixin-module');
							jweixin.chooseWXPay({
								timestamp: opt.timeStamp, // 支付签名时间戳，注意微信jssdk中的所有使用timestamp字段均为小写。但最新版的支付后台生成签名使用的timeStamp字段名需大写其中的S字符
								nonceStr: opt.nonceStr, // 支付签名随机串，不长于 32 位
								package: opt.package, // 统一支付接口返回的prepay_id参数值，提交格式如：prepay_id=\*\*\*）
								signType: opt.signType, // 签名方式，默认为'SHA1'，使用新版支付需传入'MD5'
								paySign: opt.paySign, // 支付签名
								success: function (res2) {
									// 支付成功后的回调函数
									app.success('付款完成');
									that.subscribeMessage(function () {
										setTimeout(function () {
											if (that.give_coupon_list && that.give_coupon_list.length > 0) {
												that.give_coupon_show = true;
												that.give_coupon_close_url = that.tourl;
											} else {
												that.gotourl(that.tourl,'reLaunch');
											}
										}, 1000);
									});
								}
							});
							*/
						} else if (app.globalData.platform == 'h5') {
							location.href = opt.wx_url + '&redirect_url=' + encodeURIComponent(location.href
								.split('#')[0] + '#' + that.tourl);
						} else if (app.globalData.platform == 'app') {
							console.log(opt)
							uni.requestPayment({
								'provider': 'wxpay',
								'orderInfo': opt,
								'success': function(res2) {
									app.success(that.lang('payment is done'));
									that.subscribeMessage(function() {
										setTimeout(function() {
											if (that.give_coupon_list && that
												.give_coupon_list.length > 0) {
												that.give_coupon_show = true;
												that.give_coupon_close_url = that
													.tourl;
											} else {
												that.gotourl(that.tourl,
													'reLaunch');
											}
										}, 1000);
									});
								},
								'fail': function(res2) {
									console.log(res2)
									//app.alert(JSON.stringify(res2))
								}
							});
						} else if (app.globalData.platform == 'qq') {
							qq.requestWxPayment({
								url: opt.wx_url,
								referer: opt.referer,
								success(res) {
									that.subscribeMessage(function() {
										setTimeout(function() {
											if (that.give_coupon_list && that
												.give_coupon_list.length > 0) {
												that.give_coupon_show = true;
												that.give_coupon_close_url = that
													.tourl;
											} else {
												that.gotourl(that.tourl, 'reLaunch');
											}
										}, 1000);
									});
								},
								fail(res) {}
							})
						}
					})
				} else if (typeid == 3 || typeid == 31 || typeid == 32) { //支付宝支付
					app.showLoading(that.lang('in the submission'));
					app.post('ApiPay/pay', {
						op: 'submit',
						orderid: orderid,
						typeid: typeid
					}, function(res) {
						console.log(res)
						app.showLoading(false);
						if (res.status == 0) {
							app.error(res.msg);
							return;
						}
						if (res.status == 2) {
							//无需付款
							app.success(res.msg);
							that.subscribeMessage(function() {
								setTimeout(function() {
									if (that.give_coupon_list && that.give_coupon_list.length >
										0) {
										that.give_coupon_show = true;
										that.give_coupon_close_url = that.tourl;
									} else {
										that.gotourl(that.tourl, 'reLaunch');
									}
								}, 1000);
							});
							return;
						}
						var opt = res.data;
						if (app.globalData.platform == 'alipay') {
							uni.requestPayment({
								'provider': 'alipay',
								'orderInfo': opt.trade_no,
								'success': function(res2) {
									console.log(res2)
									if (res2.resultCode == '6001') {
										return;
									}
									app.success(that.lang('payment is done'));
									that.subscribeMessage(function() {
										setTimeout(function() {
											if (that.give_coupon_list && that
												.give_coupon_list.length > 0) {
												that.give_coupon_show = true;
												that.give_coupon_close_url = that
													.tourl;
											} else {
												that.gotourl(that.tourl,
													'reLaunch');
											}
										}, 1000);
									});
								},
								'fail': function(res2) {
									//app.alert(JSON.stringify(res2))
								}
							});
						} else if (app.globalData.platform == 'mp' || app.globalData.platform == 'h5') {
							document.body.innerHTML = res.data;
							document.forms['alipaysubmit'].submit();
						} else if (app.globalData.platform == 'app') {
							console.log('------------alipay----------')
							console.log(opt)
							console.log('------------alipay end----------')
							uni.requestPayment({
								'provider': 'alipay',
								'orderInfo': opt,
								'success': function(res2) {
									console.log('------------success----------')
									console.log(res2)
									app.success(that.lang('payment is done'));
									that.subscribeMessage(function() {
										setTimeout(function() {
											if (that.give_coupon_list && that
												.give_coupon_list.length > 0) {
												that.give_coupon_show = true;
												that.give_coupon_close_url = that
													.tourl;
											} else {
												that.gotourl(that.tourl,
													'reLaunch');
											}
										}, 1000);
									});
								},
								'fail': function(res2) {
									console.log(res2)
									//app.alert(JSON.stringify(res2))
								}
							});
						}
					})
				} else if (typeid == '11') {
					app.showLoading(that.lang('in the submission'));
					app.post('ApiPay/pay', {
						op: 'submit',
						orderid: orderid,
						typeid: typeid
					}, function(res) {
						app.showLoading(false);
						swan.requestPolymerPayment({
							'orderInfo': res.orderInfo,
							'success': function(res2) {
								app.success(that.lang('payment is done'));
								that.subscribeMessage(function() {
									setTimeout(function() {
										if (that.give_coupon_list && that
											.give_coupon_list.length > 0) {
											that.give_coupon_show = true;
											that.give_coupon_close_url = that
												.tourl;
										} else {
											that.gotourl(that.tourl, 'reLaunch');
										}
									}, 1000);
								});
							},
							'fail': function(res2) {
								if (res2.errCode != 2) {
									app.alert(JSON.stringify(res2))
								}
							}
						});
					});
				} else if (typeid == '12') {
					app.showLoading(that.lang('in the submission'));
					app.post('ApiPay/pay', {
						op: 'submit',
						orderid: orderid,
						typeid: typeid
					}, function(res) {
						app.showLoading(false);
						console.log(res.orderInfo);
						tt.pay({
							'service': 5,
							'orderInfo': res.orderInfo,
							'success': function(res2) {
								if (res2.code === 0) {
									app.success(that.lang('payment is done'));
									that.subscribeMessage(function() {
										setTimeout(function() {
											if (that.give_coupon_list && that
												.give_coupon_list.length > 0) {
												that.give_coupon_show = true;
												that.give_coupon_close_url = that
													.tourl;
											} else {
												that.gotourl(that.tourl,
													'reLaunch');
											}
										}, 1000);
									});
								}
							},
							'fail': function(res2) {
								app.alert(JSON.stringify(res2))
							}
						});
					});
				} else if (typeid == '22') {
					if (app.globalData.platform == 'wx') {
						wx.login({
							success: function(res) {
								if (res.code) {
									app.showLoading(that.lang('in the submission'));
									app.post('ApiPay/getYunMpauthParams', {
										jscode: res.code
									}, function(res) {
										app.showLoading(false);
										app.post('https://showmoney.cn/scanpay/fixed/mpauth', res
											.params,
											function(res2) {
												console.log(res2.sessionKey);
												app.post('ApiPay/getYunUnifiedParams', {
													orderid: orderid,
													sessionKey: res2.sessionKey
												}, function(res3) {
													app.post(
														'https://showmoney.cn/scanpay/unified',
														res3.params,
														function(res4) {
															if (res4.respcd ==
																'09') {
																wx.requestPayment({
																	timeStamp: res4
																		.timeStamp,
																	nonceStr: res4
																		.nonceStr,
																	package: res4
																		.package,
																	signType: res4
																		.mpSignType,
																	paySign: res4
																		.mpSign,
																	success: function success(
																		result
																	) {
																		app.success(
																			that
																			.lang(
																				'payment is done'
																			)
																		);
																		that.subscribeMessage(
																			function() {
																				setTimeout
																					(function() {
																							if (that
																								.give_coupon_list &&
																								that
																								.give_coupon_list
																								.length >
																								0
																							) {
																								that.give_coupon_show =
																									true;
																								that.give_coupon_close_url =
																									that
																									.tourl;
																							} else {
																								that.gotourl(
																									that
																									.tourl,
																									'reLaunch'
																								);
																							}
																						},
																						1000
																					);
																			}
																		);
																	},
																	fail: function(
																		res5
																	) {
																		//app.alert(JSON.stringify(res5))
																	}
																});
															} else {
																app.alert(res4
																	.errorDetail
																);
															}
														})
												})
											})
									})
								} else {
									console.log('登录失败！' + res.errMsg)
								}
							}
						});
					} else {
						var url = app.globalData.baseurl + 'ApiPay/pay' + '&aid=' + app.globalData.aid + '&platform=' +
							app.globalData.platform + '&session_id=' + app.globalData.session_id;
						url += '&op=submit&orderid=' + orderid + '&typeid=22';
						location.href = url;
					}
				} else if (typeid == '23') {
					//var url = app.globalData.baseurl + 'ApiPay/pay'+'&aid=' + app.globalData.aid + '&platform=' + app.globalData.platform + '&session_id=' + app.globalData.session_id;
					//url += '&op=submit&orderid='+orderid+'&typeid=23';
					//location.href = url;
					setTimeout(function() {
						that.$refs.dialogPayconfirm.open();
					}, 1000);

					app.goto('/pages/index/webView2?orderid=' + orderid + '&typeid=23' + '&aid=' + app.globalData.aid +
						'&platform=' + app.globalData.platform + '&session_id=' + app.globalData.session_id);
					return;
					app.showLoading(that.lang('in the submission'));
					app.post('ApiPay/pay', {
						op: 'submit',
						orderid: orderid,
						typeid: 23
					}, function(res) {
						app.showLoading(false);
						console.log(res)
						app.goto('url::' + res.url);
					});
				} else if (typeid == '13') {
					//var url = app.globalData.baseurl + 'ApiPay/pay'+'&aid=' + app.globalData.aid + '&platform=' + app.globalData.platform + '&session_id=' + app.globalData.session_id;
					//url += '&op=submit&orderid='+orderid+'&typeid=23';
					//location.href = url;

					// app.goto('/pages/index/webView2?orderid='+orderid+'&typeid=13');
					// return ;
					app.showLoading(that.lang('in the submission'));
					app.post('ApiPay/pay', {
						op: 'submit',
						orderid: orderid,
						typeid: 13
					}, function(res) {
						app.showLoading(false);
						console.log(res)
						app.goto('url::' + res.url);
					});
				}
			},
			topay2: function() {
				var that = this;
				var orderid = this.payorder.id;
				app.confirm(that.lang('confirm') + that.codtxt + '?', function() {
					app.showLoading(that.lang('in the submission'));
					app.post('ApiPay/pay', {
						op: 'submit',
						orderid: orderid,
						typeid: 4
					}, function(res) {
						app.showLoading(false);
						if (res.status == 0) {
							app.error(res.msg);
							return;
						}
						if (res.status == 2) {
							//无需付款
							app.success(res.msg);
							that.subscribeMessage(function() {
								setTimeout(function() {
									that.gotourl(that.tourl, 'reLaunch');
								}, 1000);
							});
							return;
						}
					});
				})
			},
			topayTransfer: function(e) {
				var that = this;
				var orderid = this.payorder.id;
				app.confirm(that.lang('confirm') + e.currentTarget.dataset.text + '?', function() {
					app.showLoading(that.lang('in the submission'));
					app.post('ApiPay/pay', {
						op: 'submit',
						orderid: orderid,
						typeid: 5
					}, function(res) {
						app.showLoading(false);
						if (res.status == 0) {
							app.error(res.msg);
							return;
						}
						if (res.status == 2) {
							//无需付款
							app.success(res.msg);
							that.subscribeMessage(function() {

							});
							setTimeout(function() {
								that.gotourl('transfer?id=' + orderid, 'reLaunch');
							}, 1000);
							return;
						}
					});
				})
			},
			give_coupon_close: function(e) {
				var that = this;
				var tourl = e.currentTarget.dataset.url;
				this.give_coupon_show = false;
				that.gotourl(tourl, 'reLaunch');
			},
			gotourl: function(tourl, opentype) {
				var that = this;
				if (app.globalData.platform == 'mp' || app.globalData.platform == 'h5') {
					if (tourl.indexOf('miniProgram::') === 0) {
						//其他小程序
						tourl = tourl.slice(13);
						var tourlArr = tourl.split('|');
						console.log(tourlArr)
						that.showOpenWeapp();
						return;
					}
				}
				app.goto(tourl, opentype);
			},
			showOpenWeapp: function() {
				this.$refs.dialogOpenWeapp.open();
			},
			closeOpenWeapp: function() {
				this.$refs.dialogOpenWeapp.close();
			},
			PayconfirmFun: function() {
				this.gotourl(this.tourl, 'reLaunch');
			}
		}
	}
</script>
<style>
	.top {
		width: 100%;
		display: flex;
		flex-direction: column;
		align-items: center;
		padding-top: 60rpx
	}

	.top .f1 {
		height: 60rpx;
		line-height: 60rpx;
		color: #939393;
		font-size: 24rpx;
	}

	.top .f2 {
		color: #101010;
		font-weight: bold;
		font-size: 72rpx;
		height: 120rpx;
		line-height: 120rpx
	}

	.top .f2 .t1 {
		font-size: 44rpx
	}

	.top .f2 .t3 {
		font-size: 50rpx
	}

	.top .f3 {
		color: #FC5729;
		font-size: 26rpx;
		height: 70rpx;
		line-height: 70rpx
	}

	.paytype {
		width: 94%;
		margin: 20rpx 3% 80rpx 3%;
		border-radius: 10rpx;
		display: flex;
		flex-direction: column;
		margin-top: 20rpx;
		background: #fff
	}

	.paytype .f1 {
		height: 100rpx;
		line-height: 100rpx;
		padding: 0 30rpx;
		color: #333333;
		font-weight: bold
	}

	.paytype .f2 {
		padding: 0 30rpx
	}

	.paytype .f2 .item {
		border-bottom: 1px solid #f5f5f5;
		height: 100rpx;
		display: flex;
		align-items: center
	}

	.paytype .f2 .item:last-child {
		border-bottom: 0
	}

	.paytype .f2 .item .t1 {
		flex: 1;
		display: flex;
		align-items: center;
		color: #222222;
		font-size: 30rpx;
		font-weight: bold
	}

	.paytype .f2 .item .t1 .img {
		width: 44rpx;
		height: 44rpx;
		margin-right: 40rpx
	}

	.paytype .f2 .item .radio {
		flex-shrink: 0;
		width: 36rpx;
		height: 36rpx;
		background: #FFFFFF;
		border: 3rpx solid #BFBFBF;
		border-radius: 50%;
		margin-right: 10rpx
	}

	.paytype .f2 .item .radio .radio-img {
		width: 100%;
		height: 100%
	}

	.btn {
		height: 100rpx;
		line-height: 100rpx;
		width: 90%;
		margin: 0 auto;
		border-radius: 10rpx;
		margin-top: 30rpx;
		color: #fff;
		font-size: 30rpx;
		font-weight: bold
	}

	.op {
		width: 94%;
		margin: 20rpx 3%;
		display: flex;
		align-items: center;
		margin-top: 40rpx
	}

	.op .btn {
		flex: 1;
		height: 100rpx;
		line-height: 100rpx;
		background: #07C160;
		width: 90%;
		margin: 0 10rpx;
		border-radius: 10rpx;
		color: #fff;
		font-size: 28rpx;
		font-weight: bold;
		display: flex;
		align-items: center;
		justify-content: center
	}

	.op .btn .img {
		width: 48rpx;
		height: 48rpx;
		margin-right: 20rpx
	}

	.give-coupon {
		width: 100%;
		height: 100%;
		position: absolute;
		left: 0;
		top: 0;
		z-index: 2000;
		background-color: rgba(0, 0, 0, 0.5);
	}

	.give-coupon .coupon-block {
		margin-top: -140rpx;
		position: relative;
	}

	.give-coupon .coupon-info {
		width: 559rpx;
		border-radius: 0 0 30rpx 30rpx;
		background-color: #fff;
		margin-top: -20rpx;
		padding: 20rpx 0 20rpx 0;
	}

	.give-coupon .coupon-one {
		width: 100%;
		height: 164rpx;
		margin-bottom: 10rpx;
	}

	.give-coupon .coupon-bg {
		width: 514rpx;
		height: 164rpx;
		border-radius: 10rpx;
		box-shadow: 2rpx 2rpx 30rpx #ddd;
		padding: 14rpx;
	}

	.give-coupon .coupon-bg-1 {
		width: 100%;
		height: 100%;
		border: 2rpx #ff4544 dashed;
		border-radius: 10rpx;
		padding: 0 20rpx;
	}

	.give-coupon .coupon-del {
		position: absolute;
		right: 34rpx;
		top: 224rpx;
		width: 90rpx;
		height: 90rpx;
	}

	.give-coupon .coupon-del image {
		width: 30rpx;
		height: 30rpx;
	}

	.give-coupon .coupon-text {
		color: #707070;
		margin-top: 24rpx;
		margin-bottom: 34rpx;
		font-size: 9pt;
	}

	.give-coupon .coupon-text::before {
		content: ' ';
		margin-right: 32rpx;
		width: 50rpx;
		height: 1rpx;
		background-color: #707070;
		overflow: hidden;
		margin-top: 21rpx;
	}

	.give-coupon .coupon-text::after {
		content: ' ';
		margin-left: 32rpx;
		width: 50rpx;
		height: 1rpx;
		background-color: #707070;
		overflow: hidden;
		margin-top: 21rpx;
	}

	.give-coupon .coupon-btn {
		position: relative;
		margin: 0 auto;
		width: 340rpx;
		height: 70rpx;
		line-height: 70rpx;
		background: linear-gradient(90deg, #F9475F, #EF155B);
		color: #fff;
		border-radius: 40rpx;
		margin-top: 20rpx
	}

	.give-coupon .coupon-btn image {
		width: 374rpx;
		height: 96rpx;
	}

	.give-coupon .coupon-get {
		margin-top: 4rpx;
		margin-bottom: 20rpx;
		color: #ff4544;
		font-size: 13pt;
	}

	.give-coupon .coupon-coupon {
		width: 100%;
		display: flex;
		padding: 0 20rpx;
		margin: 10rpx 0;
	}

	.give-coupon .coupon-coupon .pt_img1 {
		background: url(data:image/svg+xml;base64,PHN2ZyBpZD0i5Zu+5bGCXzEiIGRhdGEtbmFtZT0i5Zu+5bGCIDEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgdmlld0JveD0iMCAwIDI3IDEzMCI+PGRlZnM+PHN0eWxlPi5jbHMtMXtmaWxsOiM3M2FmNjA7fTwvc3R5bGU+PC9kZWZzPjx0aXRsZT7nu7/oibJfMjwvdGl0bGU+PHBhdGggY2xhc3M9ImNscy0xIiBkPSJNMzkuNSw0Ny43NlY2MS42MmE1LDUsMCwwLDEsMCw5LjgydjMuMzhhNSw1LDAsMCwxLDAsOS44MlY4OGE1LDUsMCwwLDEsMCw5LjgydjMuMzlhNSw1LDAsMCwxLDAsOS44MnYzLjM5YTUsNSwwLDAsMSwwLDkuODJ2My4zOWE1LDUsMCwwLDEsMCw5LjgydjMuMzlhNSw1LDAsMCwxLDAsOS44MnYzLjM4YTUsNSwwLDAsMSwwLDkuODJ2MTMuODdoMjd2LTEzMFoiIHRyYW5zZm9ybT0idHJhbnNsYXRlKC0zOS41IC00Ny43NikiLz48L3N2Zz4=);
		background-size: 147%;
		height: 140rpx;
		border-bottom-left-radius: 16rpx;
		border-top-left-radius: 16rpx;
		width: 4%
	}

	.give-coupon .coupon-coupon .pt_img2 {
		background: url(data:image/svg+xml;base64,PHN2ZyBpZD0i5Zu+5bGCXzEiIGRhdGEtbmFtZT0i5Zu+5bGCIDEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgdmlld0JveD0iMCAwIDI3IDEzMCI+PGRlZnM+PHN0eWxlPi5jbHMtMXtmaWxsOiM2ZmM1ZmE7fTwvc3R5bGU+PC9kZWZzPjx0aXRsZT7ok53oibJfMTwvdGl0bGU+PHBhdGggY2xhc3M9ImNscy0xIiBkPSJNMCwwVjEzLjg2YTUsNSwwLDAsMSw0LjEsNC45MUE1LDUsMCwwLDEsMCwyMy42OHYzLjM5QTUsNSwwLDAsMSw0LjEsMzIsNSw1LDAsMCwxLDAsMzYuODl2My4zOWE1LDUsMCwwLDEsNC4xLDQuOTFBNSw1LDAsMCwxLDAsNTAuMXYzLjM5QTUsNSwwLDAsMSw0LjEsNTguNCw1LDUsMCwwLDEsMCw2My4zMXYzLjM4QTUsNSwwLDAsMSw0LjEsNzEuNiw1LDUsMCwwLDEsMCw3Ni41MVY3OS45YTUsNSwwLDAsMSw0LjEsNC45MUE1LDUsMCwwLDEsMCw4OS43MnYzLjM5QTUsNSwwLDAsMSw0LjEsOTgsNSw1LDAsMCwxLDAsMTAyLjkzdjMuMzlhNSw1LDAsMCwxLDQuMSw0LjkxQTUsNSwwLDAsMSwwLDExNi4xNFYxMzBIMjdWMFoiLz48L3N2Zz4=);
		background-size: 147%;
		height: 140rpx;
		border-bottom-left-radius: 16rpx;
		border-top-left-radius: 16rpx;
		width: 4%
	}

	.give-coupon .coupon-coupon .pt_left {
		background: #73af60;
		height: 140rpx;
		color: #FFF;
		padding-bottom: 20rpx;
		padding-right: 10rpx;
		width: 30%;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center
	}

	.give-coupon .coupon-coupon .pt_left.bg2 {
		background: #6fc5fa
	}

	.give-coupon .coupon-coupon .pt_left .f1 {
		font-size: 34rpx;
		text-align: center
	}

	.give-coupon .coupon-coupon .pt_left .t0 {
		padding-right: 10rpx;
	}

	.give-coupon .coupon-coupon .pt_left .t1 {
		font-size: 46rpx;
	}

	.give-coupon .coupon-coupon .pt_left .t2 {
		padding-left: 10rpx
	}

	.give-coupon .coupon-coupon .pt_left .f2 {
		font-size: 24rpx;
		text-align: center;
		overflow: hidden
	}

	.give-coupon .coupon-coupon .pt_right {
		background: #fff;
		width: 66%;
		display: flex;
		height: 140rpx;
		text-align: left;
		padding: 20rpx 30rpx;
		border-top-right-radius: 16rpx;
		border-bottom-right-radius: 16rpx;
		position: relative
	}

	.give-coupon .coupon-coupon .pt_right .f1 {
		flex-grow: 1;
		flex-shrink: 1;
	}

	.give-coupon .coupon-coupon .pt_right .f1 .t1 {
		font-size: 36rpx;
		color: #2c3e50;
		height: 40rpx;
		line-height: 40rpx;
		overflow: hidden;
	}

	.give-coupon .coupon-coupon .pt_right .f1 .t2 {
		height: 60rpx;
		line-height: 60rpx;
		font-size: 24rpx;
		color: #727272;
	}

	.give-coupon .coupon-coupon .pt_right .f1 .t2_1 {
		height: 40rpx;
		line-height: 40rpx
	}

	.give-coupon .coupon-coupon .pt_right .f1 .t3 {
		font-size: 24rpx;
		color: #2c3e50
	}

	.give-coupon .coupon-coupon .pt_right .f1 .t4 {
		font-size: 24rpx;
		color: #555555
	}

	.give-coupon .coupon-coupon .pt_right .btn {
		position: absolute;
		right: 30rpx;
		top: 50%;
		margin-top: -25rpx;
		border-radius: 25rpx;
		width: 140rpx;
		height: 50rpx;
		line-height: 50rpx;
		background: #07c160;
		color: #fff;
	}
</style>
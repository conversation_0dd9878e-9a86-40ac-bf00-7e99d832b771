import Vue from 'vue';
import App from './App';

//Vue.prototype.goto = function(e){
//	$.goto(e.currentTarget.dataset.url)
//};

import { setClipboardData, getClipboardData } from 'components/u-clipboard/js_sdk'
Vue.config.productionTip = false;
Vue.mixin({
	onShareAppMessage:function(){
		return this._sharewx();
	},
	onShareTimeline:function(){
		var sharewxdata = this._sharewx();
		var query = (sharewxdata.path).split('?')[1];
		return {
			title: sharewxdata.title,
			imageUrl: sharewxdata.imageUrl,
			query: query
		}
	},
	methods: {
		goto:function(e){
			getApp().goto(e.currentTarget.dataset.url,e.currentTarget.dataset.opentype)
		},
		goback:function(){
			getApp().goback();
		},
		getmenuindex:function(menuindex){
			this.menuindex = menuindex
		},
		loaded:function(obj){
			if(obj && obj.title && !obj.desc) obj.desc = obj.title
			var that = this;
			uni.stopPullDownRefresh();
			var app = getApp();
			if(app.globalData.isinit == false){
				app.get('ApiIndex/linked',{},function(){
					that.isload = true;
					that._sharemp(obj);
				});
			}else{
				this.isload = true;
				this._sharemp(obj);
			}
		},
		getdata:function(){
			var that = this;
			getApp().get('ApiIndex/linked',{},function(){
				that.loaded();
			});
		},
		getplatform:function(){
			return getApp().globalData.platform;
		},
		_sharemp:function(obj){
			//#ifdef H5
			var app = getApp();
			if(app.globalData.platform != 'mp') return;
			if(!obj) obj = {};
			if(!obj.link) obj.link = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#'+ this.sharepath();
			var currenturl = '/' + (this.route ? this.route : this.__route__); //当前页面url
			if(this.opt && this.opt.id){
				currenturl+='?id='+this.opt.id
			}else if(this.opt && this.opt.cid){
				currenturl+='?cid='+this.opt.cid
			}else if(this.opt && this.opt.gid){
				currenturl+='?gid='+this.opt.gid
			}else if(this.opt && this.opt.bid){
				currenturl+='?bid='+this.opt.bid
			}
			var sharelist = app.globalData.initdata.sharelist;
			if(sharelist){
				for(var i=0;i<sharelist.length;i++){
					if(sharelist[i]['indexurl'] == currenturl){
						obj.title = sharelist[i].title;
						obj.desc = sharelist[i].desc;
						obj.pic = sharelist[i].pic;
						if(sharelist[i].url){
							var sharelink = sharelist[i].url;
							if(sharelink.indexOf('/') === 0){
								sharelink = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#'+ sharelink;
							}
							if(app.globalData.mid>0){
								 sharelink += (sharelink.indexOf('?') === -1 ? '?' : '&') + 'pid='+app.globalData.mid;
							}
							obj.link = sharelink;
						}
					}
				}
			}
			//app.alert('分享信息' + JSON.stringify(obj));
			var jweixin = require('jweixin-module');
			jweixin.ready(function () {   //需在用户可能点击分享按钮前就先调用
				jweixin.onMenuShareAppMessage({ 
					title: obj.title || app.globalData.initdata.name,
					desc: obj.desc || app.globalData.initdata.desc,
					link: obj.link || '',
					imgUrl: obj.pic || app.globalData.initdata.logo,
					success: function () {
						obj.callback && obj.callback();
					},
				})
				jweixin.onMenuShareTimeline({ 
					title: obj.title || app.globalData.initdata.name,
					desc: obj.desc || app.globalData.initdata.desc,
					link: obj.link || '',
					imgUrl: obj.pic || app.globalData.initdata.logo,
					success: function () {
						obj.callback && obj.callback();
					},
				})
			});
			//#endif
		},
		_sharewx:function(obj){
			if(!obj) obj = {};
			var app = getApp();
			var pages = getCurrentPages(); //获取加载的页面
			var currentPage = pages[pages.length - 1]; //获取当前页面的对象
			var currenturl = '/' + (currentPage.route ? currentPage.route : currentPage.__route__); //当前页面url 

			var opt = this.opt;
			if(this.opt && this.opt.id){
				currenturl+='?id='+this.opt.id
			}else if(this.opt && this.opt.cid){
				currenturl+='?cid='+this.opt.cid
			}else if(this.opt && this.opt.gid){
				currenturl+='?gid='+this.opt.gid
			}else if(this.opt && this.opt.bid){
				currenturl+='?bid='+this.opt.bid
			}
			var sharelist = app.globalData.initdata.sharelist;
			if(sharelist){
				for(var i=0;i<sharelist.length;i++){
					if(sharelist[i]['indexurl'] == currenturl){
						obj.title = sharelist[i].title;
						obj.desc = sharelist[i].desc;
						obj.pic = sharelist[i].pic;
						obj.link = sharelist[i].url;
					}
				}
			}
			var sharepath = this.sharepath();
			if(obj.title){
				var title = obj.title
			}else{
				var title = app.globalData.initdata.name;
			}
			if(obj.pic){
				var imgUrl = obj.pic
			}else{
				var imgUrl = '';
			}
			console.log(sharepath);
			typeof obj.callback == 'function' && obj.callback();
			return {
				title: title,
				path: sharepath, 
				imageUrl: imgUrl
			}
		},
		sharepath:function(){
			var app = getApp();
			var opt = this.opt;
			var currentpath = '/' + (this.route ? this.route : this.__route__); //当前页面url
			var scene = [];
			for(var i in opt){
				if(i != 'pid'){
					scene.push(i+'_'+opt[i]);
				}
			}
			console.log(app.globalData.mid);
			if(app.globalData.mid){
				scene.push('pid_'+app.globalData.mid);
			}
			var scenes = scene.join('-');
			if(scenes){
				currentpath = currentpath + "?scene="+scenes + '&t='+parseInt((new Date().getTime())/1000);
			}
			return currentpath;
		},
		t:function(text){
			if(text=='color1'){
				return getApp().globalData.initdata.color1;
			}else if(text=='color2'){
				return getApp().globalData.initdata.color2;
			}else if(text=='color1rgb'){
				var color1rgb = getApp().globalData.initdata.color1rgb;
				return color1rgb['red']+','+color1rgb['green']+','+color1rgb['blue'];
			}else if(text=='color2rgb'){
				var color2rgb = getApp().globalData.initdata.color2rgb;
				return color2rgb['red']+','+color2rgb['green']+','+color2rgb['blue'];
			}else{
				return getApp().globalData.initdata.textset[text] || text;
			}
		},
		inArray: function (search, array) {
			for (var i in array) {
				if (array[i] == search) {
					return true;
				}
			}
			return false;
		},
		isNull:function(num){
			return (num == undefined || num == "undefined" || num == null || num == "");
		},
		isEmpty:function(list){
			return (!list || list.length === 0 || (list.length === 1 && (!list[(0)] || list[(0)].length === 0)))
		},
		date:function(time,format){
			if(format == undefined || format == "undefined" || format == null || format == "") format = 'Y-m-d H:i:s';
			var date = new Date();
			if(time != '' || time > 0) {
				date = new Date(time * 1000);
			}
			
			var Y = date.getFullYear();
			var m = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) ;
			var d = date.getDate() < 10 ? '0' + date.getDate() : date.getDate();
			var H = date.getHours() < 10 ? '0' + date.getHours() : date.getHours();
			var i = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes();
			var s = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds();
			format = format.replace('Y',Y);
			format = format.replace('m',m);
			format = format.replace('d',d);
			format = format.replace('H',H);
			format = format.replace('i',i);
			format = format.replace('s',s);
			return format;
		},
		getDistance: function (lat1, lng1, lat2, lng2) {
			if(!lat1 || !lng1 || !lat2 || !lng2) return '';
			var rad1 = lat1 * Math.PI / 180.0;
			var rad2 = lat2 * Math.PI / 180.0;
			var a = rad1 - rad2;
			var b = lng1 * Math.PI / 180.0 - lng2 * Math.PI / 180.0;
			var r = 6378137;
			var juli = r * 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(rad1) * Math.cos(rad2) * Math.pow(Math.sin(b / 2), 2)));
			juli = juli/1000
			juli = juli.toFixed(2);
			return juli;
		},
		previewImage: function (e) {
			var imgurl = e.currentTarget.dataset.url
			var imgurls = e.currentTarget.dataset.urls
			if (!imgurls) imgurls = imgurl;
			if (typeof (imgurls) == 'string') imgurls = imgurls.split(',');
			uni.previewImage({
				current: imgurl, 
				urls: imgurls 
			})
		},
		copy: function(e) {
			var res = setClipboardData(e.currentTarget.dataset.text);
			getApp().error('复制成功')
		},
		subscribeMessage:function(callback){
			var app = getApp();
			// #ifdef MP-WEIXIN
			var that = this;
			var tmplids = that.tmplids;
			if(tmplids && tmplids.length > 0){
				uni.requestSubscribeMessage({
					tmplIds: tmplids,
					success:function(res) {
						for(var i in tmplids){
							if(res[tmplids[i]] == 'accept'){
								app.post('ApiIndex/subscribemessage',{tmplid:tmplids[i]},function(){})
							}
						}
						console.log(res)
						typeof callback == "function" && callback();
					},
					fail:function(res){
						console.log(res)
						typeof callback == "function" && callback();
					}
				})
			}else{
				typeof callback == "function" && callback();
			}
			// #endif
			// #ifndef MP-WEIXIN
			typeof callback == "function" && callback();
			// #endif
		},
	}
});

App.mpType = 'app';

const app = new Vue({
    ...App
});

app.$mount();

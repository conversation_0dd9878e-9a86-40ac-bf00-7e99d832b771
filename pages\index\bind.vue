<template>
<view>
	<loading v-if="loading"></loading>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
			opt:{},
			loading:false,
      isload: false,
			menuindex:-1,

      url: ''
    };
  },
  onLoad: function (opt) {
    this.opt = app.getopts(opt);
		this.getdata();
		uni.setNavigationBarTitle({
			title:app.lang("bind administrator"),
			success() {
				console.log(app.lang("bind administrator"))
			}
		})
  },
  methods: {
		getdata: function () {
			var that = this;
			that.loading = true;
			app.post('ApiIndex/bind',{id:that.opt.id,token:that.opt.token},function(res){
				that.loading = false;
				if(res.status == 1){
					app.alert(res.msg,function(){
						app.goto('/admin/index/index');
					});
				}else{
					app.alert(res.msg);
				}
			});
		}
	}
};
</script>

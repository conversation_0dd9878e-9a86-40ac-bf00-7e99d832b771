<template>
	<view class="page" v-if="isload">
		<swiper class="swiper" :circular="circular" :vertical="true" @change="onSwiperChange">
			<swiper-item v-for="item in videoList" :key="item.id">
				<video class="video" :id="item.id" :ref="item.id" :src="item.src" :controls="false" :loop="true" :show-center-play-btn="false" @tap="playpause" @play="onplay"></video>
			</swiper-item>
		</swiper>
		<view class="goback" @tap="goback" :style="{top:gobacktopHeight+'px'}"><image class="goback-img" src="/static/img/goback.png"/></view>
		
		<view v-if="!isplayst" class="playbox" @tap.stop="playClick"><image src="/static/img/shortvideo_playnum.png" class="playbox-img"/></view>
		
		<view class="logo" @tap="gotourl" :data-url="videoDataList[_videoDataIndex].bid==0?'/pages/index/index':'/pages/business/index?id='+videoDataList[_videoDataIndex].bid"><image :src="videoDataList[_videoDataIndex].logo" class="logo-img"/></view>
		<view class="viewnum"><image src="/static/img/shortvideo_view.png" class="viewnum-img"/><text class="viewnum-txt">{{videoDataList[_videoDataIndex].view_num}}</text></view>
		<view class="likenum" @tap="dozan"><image :src="videoDataList[_videoDataIndex].iszan ? '/static/img/shortvideo_like2.png':'/static/img/shortvideo_like.png'" class="likenum-img"/><text class="likenum-txt">{{videoDataList[_videoDataIndex].zan_num}}</text></view>
		
		<view class="commentnum" @tap="commentClick" v-if="videoDataList[_videoDataIndex].comment==1"><image src="/static/img/shortvideo_comment.png" class="commentnum-img"/><text class="commentnum-txt">{{videoDataList[_videoDataIndex].commentnum}}</text></view>
		<view class="sharenum" @tap="shareClick"><image src="/static/img/shortvideo_share.png" class="sharenum-img"/><!-- <text class="sharenum-txt">{{videoDataList[_videoDataIndex].share_num}}</text> --></view>

		<view class="linkurl" @tap="gotourl" :data-url="videoDataList[_videoDataIndex].linkurl" v-if="videoDataList[_videoDataIndex].linkurl">
			<text class="linkurl-txt">{{videoDataList[_videoDataIndex].linkname}}</text>
			<image src="/static/img/shortvideo_arrowright.png" class="linkurl-img"/>
		</view>
		
		<!-- <view class="bottomshadow" :style="menuindex > -1 ? 'bottom:110rpx':''"></view> -->

		<view class="cart" :style="menuindex > -1 ? 'bottom:150rpx':''" v-if="videoDataList[_videoDataIndex].pronum > 0" @tap="proboxClick"><image src="/static/img/shortvideo_cart.png" class="cart-img"/><text class="cart-txt">{{videoDataList[_videoDataIndex].pronum}}</text></view>

		<view class="prodialog" :style="menuindex > -1 ? 'bottom:230rpx':''" v-if="!videoDataList[_videoDataIndex].linkurl && prodialogshow && videoDataList[_videoDataIndex].proid > 0" @tap="gotourl" :data-url="'/myshop/shop/product?id='+videoDataList[_videoDataIndex].proid">
			<image src="/static/img/shortvideo_probg.png" class="prodialog-bgimg"/>
			<view class="prodialog-content">
				<image :src="videoDataList[_videoDataIndex].propic" class="prodialog-content-img"/>
				<view class="prodialog-content-info">
					<text class="prodialog-content-name">{{videoDataList[_videoDataIndex].proname}}</text>
					<text class="prodialog-content-sales">{{videoDataList[_videoDataIndex].prosales}}人购买</text>
					<text class="prodialog-content-price">${{videoDataList[_videoDataIndex].prosell_price}}</text>
				</view>
			</view>
			<image src="/static/img/close.png" class="prodialog-close" @tap.stop="prodialogClose"/>
		</view>

		
		<view class="title" :style="menuindex > -1 ? 'bottom:270rpx':''"><text class="title-txt">{{videoDataList[_videoDataIndex].name}}</text></view>
		<view class="description" :style="menuindex > -1 ? 'bottom:170rpx':''"><text class="description-txt">{{videoDataList[_videoDataIndex].description}}</text></view>

		<loading v-if="loading"></loading>
		<dp-tabbar :opt="opt" @getmenuindex="getmenuindex"></dp-tabbar>
		<popmsg ref="popmsg"></popmsg>

		<view class="popupshare__content" v-if="sharetypevisible" :style="menuindex > -1 ? 'bottom:110rpx':''">
			<image src="/static/img/close.png" class="popupshare__close2" @tap.stop="handleClickMask"/>
			<view class="sharetypecontent">
				<view class="sharetypecontent-f1" @tap="shareapp" v-if="platform == 'app'">
					<image class="sharetypecontent-f1-img" src="/static/img/weixin.png"/>
					<text class="sharetypecontent-f1-t1">分享给好友</text>
				</view>
				<view class="sharetypecontent-f1" @tap="sharemp" v-else-if="platform == 'mp'">
					<image class="sharetypecontent-f1-img" src="/static/img/weixin.png"/>
					<text class="sharetypecontent-f1-t1">分享给好友</text>
				</view>
				<view class="sharetypecontent-f1" @tap="sharemp" v-else-if="platform == 'h5'">
					<image class="sharetypecontent-f1-img" src="/static/img/weixin.png"/>
					<text class="sharetypecontent-f1-t1">分享给好友</text>
				</view>
				<button class="sharetypecontent-f1" open-type="share" v-else>
					<image class="sharetypecontent-f1-img" src="/static/img/weixin.png"/>
					<text class="sharetypecontent-f1-t1">分享给好友</text>
				</button>
				<view class="sharetypecontent-f2" @tap="showPoster">
					<image class="sharetypecontent-f2-img" src="/static/img/sharepic.png"/>
					<text class="sharetypecontent-f2-t1">生成分享图片</text>
				</view>
			</view>
		</view>
		<view class="posterDialog" v-if="showposter">
			<view class="posterDialog-main">
				<view class="posterDialog-close" @tap="posterDialogClose"><image class="posterDialog-img" src="/static/img/close.png"/></view>
				<view class="posterDialog-content">
					<image class="posterDialog-content-img" :src="posterpic" mode="widthFix" @tap="previewImage" :data-url="posterpic"></image>
				</view>
			</view>
		</view>

		<!--评论-->
		<view class="plbox" v-if="showcomment" :style="menuindex > -1 ? 'bottom:110rpx':''">
			<view class="plbox_title"><text class="plbox_title-t1">评论</text><text class="plbox_title-t2">({{videoDataList[_videoDataIndex].commentnum}})</text></view>
			<image src="/static/img/close.png" class="plbox-close" @tap.stop="commentClick"/>
			<scroll-view class="plbox_content" scroll-y="true" @scrolltolower="getmorecomment">
				<block v-for="(item, idx) in commentlist" :key="item.id">
				<view class="plbox_content-item">
					<view class="plbox_content-item-f1 flex0"><image :src="item.headimg" class="plbox_content-item-f1-img"></image></view>
					<view class="plbox_content-item-f2 flex-col">
						<text class="plbox_content-item-f2-t1">{{item.nickname}}</text>
						<view class="plbox_content-item-f2-t2 plbox_content-plcontent">
							<block v-for="con in item.content">
								<text v-if="con.type=='text'" class="plbox_content-plcontent-text">{{con.content}}</text>
								<image v-if="con.type=='image'" :src="con.content" class="plbox_content-plcontent-image"></image>
							</block>
						</view>
						<block v-if="item.replylist.length>0">
						<view class="plbox_content-relist">
							<block v-for="(hfitem, index) in item.replylist" :key="index">
							<view class="plbox_content-relist-item2">
								<view class="flex-row">
									<image :src="hfitem.headimg" class="plbox_content-relist-headimg"/>
									<text class="plbox_content-relist-nickname">{{hfitem.nickname}}</text>
								</view>
								<view class="plbox_content-relist-f2 plbox_content-plcontent">
									<block v-for="con in hfitem.content">
										<text v-if="con.type=='text'" class="plbox_content-plcontent-text">{{con.content}}</text>
										<image v-if="con.type=='image'" :src="con.content" class="plbox_content-plcontent-image"></image>
									</block>
								</view>
							</view>
							 </block>
						</view>
						</block>
						<view class="plbox_content-item-f2-t3">
							<text class="plbox_content-item-f2-t3-x1">{{item.createtime}}</text>
							<view class="plbox_content-item-f2-t3-x2"><text class="plbox_content-item-f2-phuifu" @tap="replyClick" :data-id="item.id" :data-nickname="item.nickname">回复</text></view>
							<view class="plbox_content-item-f2-pzan" @tap="pzan" :data-id="item.id" :data-index="idx"><image :src="'/static/img/zan-' + (item.iszan==1?'2':'1') + '.png'" class="plbox_content-item-f2-pzan-img"/><text class="plbox_content-item-f2-pzan-txt">{{item.zan}}</text></view>
						</view>
					</view>
				</view>
				</block>
				<view v-if="nomore"><text class="comment-nomore">没有更多数据了</text></view>
				<view v-if="nodata"><text class="comment-nodata">暂无评论!</text></view>
			</scroll-view>
			<loading v-if="loading"></loading>
			<view style="height:160rpx"></view>
			<view class="plbox-replyshadow" v-if="hfid!=0" @tap.stop="replyshadowClick"></view>
			<view class="pinglun">
				<image @tap="toggleFaceBox" class="pinglun-faceicon" src="/static/img/face-icon.png"></image>
				<input @confirm="sendMessage" @focus="onInputFocus" @input="messageChange" class="pinglun-input" confirmHold="true" confirmType="send" cursorSpacing="20" type="text" :value="message" :placeholder="messageholder"/>
				<view class="pinglun-buybtn" @tap="sendcomment"><text class="pinglun-buybtn-txt">发表</text></view>
			</view>
			<wxface v-if="faceshow" @selectface="selectface"></wxface>
		</view>

		<!--商品-->
		<view class="probox" v-if="showproduct" :style="menuindex > -1 ? 'bottom:110rpx':''">
			<view class="probox_title"><text class="probox_title-t1">全部商品</text><text class="plbox_title-t2">({{videoDataList[_videoDataIndex].pronum}})</text></view>
			<image src="/static/img/close.png" class="plbox-close" @tap.stop="proboxClick"/>
			<scroll-view class="probox_content" scroll-y="true">
				<block v-for="(item, idx) in prolist" :key="item.id">
					<view class="probox_content-item" @tap="gotourl" :data-url="'/myshop/shop/product?id='+item.id">
						<image :src="item.pic" class="probox_content-item-img"/>
						<view class="probox_content-item-info">
							<text class="probox_content-item-name">{{item.name}}</text>
							<text class="probox_content-item-sales">{{item.sales}}人购买</text>
							<text class="probox_content-item-price">${{item.sell_price}}</text>
						</view>
						<view class="probox_content-item-btn"><text class="probox_content-item-btn-txt">立即抢购</text></view>
					</view>
				</block>
			</scroll-view>
		</view>
	</view>
</template>
<script>
var app = getApp();
export default {
	data() {
		return {
			opt:{},
			loading:false,
			isload: false,
			menuindex:-1,
			nomore: false,
			nodata:false,
			platform:app.globalData.platform,
			gobacktopHeight:40,
			
			playst:true,
			circular: true,
			videoList: [{id: "video0",src: "",img: ""},{id: "video1",src: "",img: ""},{id: "video2",src: "",img: ""}],
			_videoDataIndex:0,
			videoDataList: [],
			prodialogshow:true,
			sharetypevisible: false,
			showposter: false,
			posterpic: "",
			showcomment:false,
			commentlist:[],
			pagenum:1,
      message: "",
      trimMessage: "",
      faceshow: false,
			hfid:0,
			messageholder:'发表评论',
			showproduct:false,
			prolist:[],
			isplay:false,
			isplayst:true,
		}
	},
	onLoad: function (opt) {
		this.opt = app.getopts(opt);
	},
	onReady() {
		this.init();
		this.getdata();
		var sysinfo = uni.getSystemInfoSync();
		console.log(sysinfo);
		if(sysinfo && sysinfo.statusBarHeight){
			this.gobacktopHeight = sysinfo.statusBarHeight + 4;
		}
		// #ifdef H5
		this.gobacktopHeight = 20;
		// #endif
	},
	onShareAppMessage:function(){
		return this._sharewx({title:this.videoDataList[this._videoDataIndex].name,pic:this.videoDataList[this._videoDataIndex].coverimg});
	},
	onShareTimeline:function(){
		var sharewxdata = this._sharewx({title:this.videoDataList[this._videoDataIndex].name,pic:this.videoDataList[this._videoDataIndex].coverimg});
		var query = (sharewxdata.path).split('?')[1];
		return {
			title: sharewxdata.title,
			imageUrl: sharewxdata.imageUrl,
			query: query
		}
	},
	methods: {
		init() {
			this._videoIndex = 0;
			this._videoContextList = [];
			for (var i = 0; i < this.videoList.length; i++) {
				this._videoContextList.push(uni.createVideoContext('video' + i, this));
			}
			this._videoDataIndex = 0;
		},
		getdata(e) {
			var that = this;
			that.nomore = false;
			that.loading = true;
      app.post('ApiShortvideo/detail', {id:that.opt.id,cid:that.opt.cid}, function (res) {
				that.loading = false;
				that.videoDataList = res.videoList;
				that.isload = true;
				// #ifndef APP-PLUS
				that.loaded({title:that.videoDataList[that._videoDataIndex].name,desc:that.videoDataList[that._videoDataIndex].description,pic:that.videoDataList[that._videoDataIndex].coverimg});
				// #endif
				setTimeout(() => {
					that.updateVideo(true);
				}, 200)
			});
		},
		onSwiperChange(e) {
			let currentIndex = e.detail.current;
			if (currentIndex === this._videoIndex) {
				return;
			}
			let isNext = false;
			if (currentIndex === 0 && this._videoIndex === this.videoList.length - 1) {
				isNext = true;
			} else if (currentIndex === this.videoList.length - 1 && this._videoIndex === 0) {
				isNext = false;
			} else if (currentIndex > this._videoIndex) {
				isNext = true;
			}

			if (isNext) {
				this._videoDataIndex++;
			} else {
				this._videoDataIndex--;
			}

			if (this._videoDataIndex < 0) {
				this._videoDataIndex = this.videoDataList.length - 1;
			} else if (this._videoDataIndex >= this.videoDataList.length) {
				this._videoDataIndex = 0;
			}

			this.circular = (this._videoDataIndex != 0);

			if (this._videoIndex >= 0) {
				this._videoContextList[this._videoIndex].pause();
				this._videoContextList[this._videoIndex].seek(0);
			}

			this._videoIndex = currentIndex;
			setTimeout(() => {
				this.updateVideo(isNext);
			}, 200);
			
			this.showcomment = false;
			this.prodialogshow = true;
			app.post('ApiShortvideo/updateviewnum',{id:this.videoDataList[this._videoDataIndex].id},function(){});
		},
		getNextIndex(isNext) {
			let index = this._videoIndex + (isNext ? 1 : -1);
			if (index < 0) {
				return this.videoList.length - 1;
			} else if (index >= this.videoList.length) {
				return 0;
			}
			return index;
		},
		getNextDataIndex(isNext) {
			let index = this._videoDataIndex + (isNext ? 1 : -1);
			if (index < 0) {
				return this.videoDataList.length - 1;
			} else if (index >= this.videoDataList.length) {
				return 0;
			}
			return index;
		},
		onplay:function(){
			this.isplay = true;
			this.isplayst = true;
		},
		playClick:function(){
			this._videoContextList[this._videoIndex].play();
		},
		playpause:function(){
			if(this.playst){
				this._videoContextList[this._videoIndex].pause();
			}else{
				this._videoContextList[this._videoIndex].play();
			}
			this.playst = !this.playst;
		},
		updateVideo(isNext) {
			this.$set(this.videoList[this._videoIndex], 'src', this.videoDataList[this._videoDataIndex].src);
			this.$set(this.videoList[this.getNextIndex(isNext)], 'src', this.videoDataList[this.getNextDataIndex(isNext)].src);
			
			this.isplay = false;
			this.isplayst = true;
			setTimeout(() => {
				this._videoContextList[this._videoIndex].play();
			}, 200);
			setTimeout(() => {
				if(this.isplay == false) this.isplayst = false;
			},400)
			console.log("v:" + this._videoIndex + " d:" + this._videoDataIndex + "; next v:" + this.getNextIndex(
				isNext) + " next d:" + this.getNextDataIndex(isNext));
		},
		prodialogClose:function(){
			this.prodialogshow = false
		},
		dozan:function(){
			var that = this
			app.post('ApiShortvideo/zan',{id:this.videoDataList[this._videoDataIndex].id},function(res){
				that.videoDataList[that._videoDataIndex].iszan = res.iszan;
				that.videoDataList[that._videoDataIndex].zan_num = res.zan_num;
			});
		},
		proboxClick:function(){
			var that = this;
			this.showproduct = !this.showproduct;
			if(this.showproduct){
				this.getprolist();
			}
		},
		getprolist:function(loadmore){
			var that = this;
			if(!loadmore){
				this.pagenum = 1;
				this.prolist = [];
			}
			that.nodata = false;
			that.nomore = false;
			app.post('ApiShortvideo/getprolist',{id:this.videoDataList[this._videoDataIndex].id,pagenum:this.pagenum},function(res){
				var data = res.data;
				if (that.pagenum == 1) {
					that.prolist = data;
          if (data.length == 0) {
            that.nodata = true;
          }
				}else{
					if (data.length == 0) {
							that.nomore = true;
					} else {
						var prolist = that.prolist;
						var newdata = prolist.concat(data);
						that.prolist = newdata;
					}
				}
			});
		},
		gotourl:function(e){
			getApp().goto(e.currentTarget.dataset.url,e.currentTarget.dataset.opentype)
		},
		getmoreprolist: function () {
			this.pagenum = this.pagenum + 1;
			if(!this.nomore && !this.nodata){
				this.getprolist(true);
			}
		},
		commentClick:function(){
			var that = this;
			this.showcomment = !this.showcomment;
			if(this.showcomment){
				this.getcommentlist();
			}
		},
		getcommentlist:function(loadmore){
			var that = this;
			if(!loadmore){
				this.pagenum = 1;
				this.commentlist = [];
			}
			that.nodata = false;
			that.nomore = false;
			app.post('ApiShortvideo/getcommentlist',{id:this.videoDataList[this._videoDataIndex].id,pagenum:this.pagenum},function(res){
				var data = res.data;
				if (that.pagenum == 1) {
					that.commentlist = data;
          if (data.length == 0) {
            that.nodata = true;
          }
				}else{
					if (data.length == 0) {
							that.nomore = true;
					} else {
						var commentlist = that.commentlist;
						var newdata = commentlist.concat(data);
						that.commentlist = newdata;
					}
				}
			});
		},
		getmorecomment: function () {
			this.pagenum = this.pagenum + 1;
			if(!this.nomore && !this.nodata){
				this.getcommentlist(true);
			}
		},
		replyClick:function(e){
			var id = e.currentTarget.dataset.id
			this.message = '';
			this.hfid = e.currentTarget.dataset.id;
			this.messageholder = '回复 '+e.currentTarget.dataset.nickname+'：';
		},
		replyshadowClick:function(){
			this.hfid = 0;
			this.message = '';
			this.messageholder = '发表评论';
		},
		sendcomment:function(){
			var that = this;
			var message = this.message;
			var hfid = this.hfid;
			app.showLoading('提交中');
			app.post('ApiShortvideo/subpinglun', {id:this.videoDataList[this._videoDataIndex].id,hfid:hfid,content:message}, function (res) {
				app.showLoading(false);
				if (res.status == 0) {
					app.error(res.msg);
				} else {
					app.error(res.msg);
					that.videoDataList[that._videoDataIndex].commentnum = res.commentnum;
					that.getcommentlist();
					that.message = '';
					that.hfid = 0;
					that.messageholder = '发表评论';
					that.faceshow = false;
				}
			});

		},
    pzan: function (e) {
      var that = this;
      var id = e.currentTarget.dataset.id;
      var index = e.currentTarget.dataset.index;
      var commentlist = that.commentlist;
      app.post("ApiShortvideo/pzan", {id: id}, function (res) {
        if (res.type == 0) {
          //取消点赞
          var iszan = 0;
        } else {
          var iszan = 1;
        }
        commentlist[index].iszan = iszan;
        commentlist[index].zan = res.zancount;
        that.commentlist = commentlist;
      });
    },
		shareClick: function () {
			this.sharetypevisible = !this.sharetypevisible;
		},
		handleClickMask: function () {
			this.sharetypevisible = false
		},
		showPoster: function () {
			var that = this;
			that.showposter = true;
			that.sharetypevisible = false;
			app.showLoading('生成海报中');
			app.post('ApiShortvideo/getposter', {id:this.videoDataList[this._videoDataIndex].id}, function (data) {
				app.showLoading(false);
				if (data.status == 0) {
					app.alert(data.msg);
				} else {
					that.posterpic = data.poster;
				}
			});
		},
		posterDialogClose: function () {
			this.showposter = false;
			console.log('11')
		},
    toggleFaceBox: function () {
      this.faceshow = !this.faceshow
    },
    onInputFocus: function (e) {
      this.faceshow = false
    },
    messageChange: function (e) {
      this.message = e.detail.value;
      this.trimMessage = e.detail.value.trim();
    },
    transformMsgHtml: function (msgtype, content) {
      if (msgtype == 'miniprogrampage') {
        var contentdata = JSON.parse(content);
        content = '<div style="font-size:16px;font-weight:bold;height:25px;line-height:25px">' + contentdata.Title + '</div><img src="' + contentdata.ThumbUrl + '" width="200"/>';
      }
      if (msgtype == 'image') {
        content = '<img src="' + content + '" width="200"/>';
      }
      return content;
    },
    selectface: function (face) {
      this.message = "" + this.message + face;
			this.trimMessage = this.message.trim();
    },
		goback:function(){
			getApp().goback();
		},
		sharemp:function(){
			app.error('点击右上角发送给好友或分享到朋友圈');
			this.sharetypevisible = false
		},
		shareapp:function(){
			var that = this;
			var videoinfo = that.videoDataList[that._videoDataIndex]
			uni.showActionSheet({
        itemList: ['发送给微信好友', '分享到微信朋友圈'],
        success: function (res){
					if(res.tapIndex >= 0){
						var scene = 'WXSceneSession';
						if (res.tapIndex == 1) {
							scene = 'WXSenceTimeline';
						}
						var sharedata = {};
						sharedata.provider = 'weixin';
						sharedata.type = 0;
						sharedata.scene = scene;
						sharedata.title = videoinfo.name;
						sharedata.summary = videoinfo.description;
						sharedata.href = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#/activity/shortvideo/detail?scene=id_'+videoinfo.id+'-pid_' + app.globalData.mid;
						sharedata.imageUrl = videoinfo.coverimg;
						var sharelist = app.globalData.initdata.sharelist;
						if(sharelist){
							for(var i=0;i<sharelist.length;i++){
								if(sharelist[i]['indexurl'] == '/activity/shortvideo/detail'){
									sharedata.title = sharelist[i].title;
									sharedata.summary = sharelist[i].desc;
									sharedata.imageUrl = sharelist[i].pic;
									if(sharelist[i].url){
										var sharelink = sharelist[i].url;
										if(sharelink.indexOf('/') === 0){
											sharelink = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#'+ sharelink;
										}
										if(app.globalData.mid>0){
											 sharelink += (sharelink.indexOf('?') === -1 ? '?' : '&') + 'pid='+app.globalData.mid;
										}
										sharedata.href = sharelink;
									}
								}
							}
						}
						uni.share(sharedata);
					}
        }
      });
		},
	}
}
</script>

<style>
	/* #ifndef APP-PLUS */
	page {width: 100%;height: 100%;display: flex;overflow:hidden;position:relative}
	/* #endif */
	.page {flex: 1;/* width: 750rpx; */}
	.swiper {flex: 1;}
	.swiper-item {flex: 1;}
	.video {flex: 1;/* #ifndef APP-PLUS */width: 100%;/* #endif */}

	.flex-row{display:flex;flex-direction:row}
	
	.goback{position:absolute;z-index:4;top:40px;left:15px;width:30px;height:30px;display:flex;flex-direction:column;align-items:center}
	.goback-img{width:30px;height:30px}

	.playbox{position:absolute;width:750rpx;height: 100%;flex:1;background:rgba(0,0,0,0.5);z-index:3}
	.playbox-img{position:absolute;top:50%;left:50%;width:60rpx;height:60rpx;margin-left:-30rpx;margin-top:-30rpx}

	.logo{position:absolute;z-index:4;top:260rpx;right:18rpx;width:90rpx;height:90rpx;z-index:4;border-radius:50%;overflow:hidden;border:4rpx solid #FFFFFF;background: linear-gradient(180deg, #FF2775 0%, #FF2F4B 100%);}
	.logo-img{width:86rpx;height:86rpx;border-radius:50%;}
	.viewnum{position:absolute;z-index:4;top:400rpx;right:26rpx;display:flex;flex-direction:column;align-items:center}
	.viewnum-img{width:72rpx;height:72rpx}
	.viewnum-txt{font-size:24rpx;color:#fff;text-shadow: 0px 6px 12px rgba(0, 0, 0, 0.12);}
	.likenum{position:absolute;z-index:4;top:530rpx;right:26rpx;display:flex;flex-direction:column;align-items:center}
	.likenum-img{width:72rpx;height:72rpx}
	.likenum-txt{font-size:24rpx;color:#fff;text-shadow: 0px 6px 12px rgba(0, 0, 0, 0.12);}
	.commentnum{position:absolute;z-index:4;top:660rpx;right:26rpx;display:flex;flex-direction:column;align-items:center}
	.commentnum-img{width:72rpx;height:72rpx}
	.commentnum-txt{font-size:24rpx;color:#fff;text-shadow: 0px 6px 12px rgba(0, 0, 0, 0.12);}
	.sharenum{position:absolute;z-index:4;top:790rpx;right:26rpx;display:flex;flex-direction:column;align-items:center}
	.sharenum-img{width:72rpx;height:72rpx}
	.sharenum-txt{font-size:24rpx;color:#fff;text-shadow: 0px 6px 12px rgba(0, 0, 0, 0.12);}

	.linkurl{position:absolute;z-index:4;bottom:160rpx;right:0;display:flex;flex-direction:row;align-items:center;background:rgba(100,100,100,0.5);padding:10rpx 0 10rpx 20rpx;border-radius:30rpx 0 0 30rpx}
	.linkurl-img{width:36rpx;height:36rpx}
	.linkurl-txt{font-size:26rpx;color:#fff;}
	
	.bottomshadow{position:absolute;z-index:4;bottom:0;left:0;width:100%;height:400rpx;background: linear-gradient(0deg, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0) 100%);border-radius: 10px;}
	.cart{position:absolute;z-index:5;bottom:40rpx;right:50rpx;width:88rpx;height:88rpx;display:relative}
	.cart-img{width:88rpx;height:88rpx}
	.cart-txt{position:absolute;z-index:5;bottom:6rpx;color:#fff;width:88rpx;text-align:center;font-size:28rpx;font-weight:bold}

	.title{position:absolute;z-index:3;bottom:160rpx;left:30rpx;width:500rpx;overflow:hidden;}
	.title-txt{font-size:36rpx;color:#fff;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}
	.description{position:absolute;z-index:3;bottom:60rpx;left:30rpx;width:500rpx;overflow:hidden;}
	.description-txt{font-size:28rpx;height:88rpx;line-height:44rpx;color:#fff;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}
	
	.prodialog{width:480rpx;height:190rpx;position:absolute;bottom:120rpx;right:10rpx;z-index:10;display:flex}
	.prodialog-bgimg{width:480rpx;height:190rpx;}
	.prodialog-content{width:470rpx;display:flex;flex-direction:row;flex:1;padding:20rpx;position:relative;position:absolute;bottom:24rpx;right:10rpx}
	.prodialog-content-img{width:128rpx;height:128rpx;border-radius:10rpx;flex-shrink:0}
	.prodialog-content-info{flex:1;display:flex;flex-direction:column;padding-left:20rpx}
	.prodialog-content-name{color:#212121;font-size:28rpx;height:40rpx;line-height:40rpx;font-weight:bold;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}
	.prodialog-content-sales{color:#999999;font-size:24rpx;height:40rpx;line-height:40rpx}
	.prodialog-content-price{color:#FD4A46;font-size:28rpx;font-weight:bold;height:50rpx;line-height:50rpx}

	.probox{width:750rpx;height:700rpx;padding:20rpx 20rpx;background:#fff;z-index:6;position:relative}
	.probox-replyshadow{position:absolute;z-index:7;background:rgba(0,0,0,0.4);width:750rpx;flex:1;left:0;right:0;top:0;bottom:0;}
	.probox-close{position:absolute;top:10rpx;right:10rpx;z-index:7;width:50rpx;height:50rpx;padding:10rpx;}
	.probox_title{height:60rpx;line-height:60rpx;margin-bottom:20rpx;display:flex;flex-direction:row}
	.probox_title-t1{font-size:28rpx;color:#000;font-weight:bold}
	.probox_title-t2{font-size:28rpx;color:#000;font-weight:bold}
	.probox_content{height:580rpx;overflow:scroll;display:flex;}
	.probox_content-item{display:flex;flex-direction:row;flex:1;padding:20rpx;position:relative}
	.probox_content-item-img{width:188rpx;height:188rpx;border-radius:10rpx;flex-shrink:0}
	.probox_content-item-info{flex:1;display:flex;flex-direction:column;padding-left:20rpx}
	.probox_content-item-name{color:#212121;font-size:28rpx;height:80rpx;line-height:40rpx;font-weight:bold;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}
	.probox_content-item-sales{color:#999999;font-size:24rpx;height:50rpx;line-height:50rpx}
	.probox_content-item-price{color:#FD4A46;font-size:36rpx;font-weight:bold;height:50rpx;line-height:50rpx}
	.probox_content-item-btn{position:absolute;top:140rpx;right:20rpx;background: linear-gradient(-90deg, #FD4A46 0%, rgba(253, 74, 70, 0.76) 100%);border-radius:26px;width:150rpx;height:54rpx;display:flex;flex-direction:row;align-items:center;justify-content:center}
	.probox_content-item-btn-txt{color:#FFFFFF;font-size:24rpx;font-weight:bold;text-align:center}

	.popupshare__content{position: absolute;bottom: 0;left: 0;right: 0;z-index:999;width:750rpx;height:280rpx;padding:20rpx 0;background:#fff;display:flex;}
	.popupshare__close2{position:absolute;top:10rpx;right:10rpx;z-index:1000;width:50rpx;height:50rpx;padding:10rpx;}
	.sharetypecontent{flex:1;width:710rpx;height:250rpx;margin:0 20rpx;display:flex;flex-direction:row;padding:50rpx;}
	.sharetypecontent-f1{flex:1;display:flex;flex-direction:column;align-items:center;background:#fff;font-size:24rpx;border:0}
	.sharetypecontent button::after{border:0}
	.sharetypecontent-f1-img{width:90rpx;height:90rpx;padding:0;margin:0}
	.sharetypecontent-f1-t1{font-size:24rpx;color:#111111;height:60rpx;line-height:60rpx}
	.sharetypecontent-f2{flex:1;display:flex;flex-direction:column;align-items:center}
	.sharetypecontent-f2-img{width:90rpx;height:90rpx;}
	.sharetypecontent-f2-t1{font-size:24rpx;color:#111111;height:60rpx;line-height:60rpx}

	.posterDialog{ position:absolute;z-index:99;width:750rpx;height:1200rpx;top:100rpx;left:0}
	.posterDialog-main{ width:670rpx;height:1100rpx;margin:60rpx 40rpx 30rpx 40rpx;background:#fff;position:relative;border-radius:20rpx;display:flex}
	.posterDialog-close{ position:absolute;padding:20rpx;top:0;right:0;z-index:10;}
	.posterDialog-img{ width:40rpx;height:40rpx;}
	.posterDialog-content{flex:1;padding:70rpx 20rpx 30rpx 20rpx;color:#333;font-size:30rpx;text-align:center;display:flex}
	.posterDialog-content-img{flex:1;}
	.prodialog-close{width:36rpx;height:36rpx;padding:10rpx;position:absolute;top:6rpx;right:14rpx;z-index:9}

	
	.pinglun{width:750rpx;padding:0 20rpx;margin:0 auto;display:flex;flex-direction:row;align-items:center;position:absolute;bottom:0;left:0;right:0;height:100rpx;background:#fff;z-index:8;border-top:1px solid #f7f7f7}
	.pinglun-faceicon {	width:46rpx;height:46rpx;	margin-left: 10rpx;margin-right:18rpx}
	.pinglun-input{flex:1;color:#000;font-size:32rpx;padding:0;line-height:100rpx}
	.pinglun-buybtn{margin-left:0.08rpx;background:#FD4A46;height:60rpx;line-height:60rpx;padding:0 30rpx;border-radius:6rpx}
	.pinglun-buybtn-txt{color:#fff;font-size:28rpx;height:60rpx;line-height:60rpx;}


	.plbox{width:750rpx;height:700rpx;padding:20rpx 20rpx;background:#fff;z-index:6;position:relative}
	.plbox-replyshadow{position:absolute;z-index:7;background:rgba(0,0,0,0.4);width:750rpx;flex:1;left:0;right:0;top:0;bottom:0;}
	.plbox-close{position:absolute;top:10rpx;right:10rpx;z-index:7;width:50rpx;height:50rpx;padding:10rpx;}
	.plbox_title{height:60rpx;line-height:60rpx;margin-bottom:20rpx;display:flex;flex-direction:row}
	.plbox_title-t1{font-size:28rpx;color:#000;font-weight:bold}
	.plbox_title-t2{font-size:28rpx;color:#000;font-weight:bold}
	.plbox_content{height:500rpx;overflow:scroll;display:flex;}
	.plbox_content-plcontent{display:flex;flex-direction:row;vertical-align: middle;color:#111}
	.plbox_content-plcontent-text{ color:#111;font-size:28rpx}
	.plbox_content-plcontent-image{ width:44rpx;height:44rpx;vertical-align: inherit;}
	.plbox_content-item{flex:1;margin-bottom:20rpx;display:flex;flex-direction:row}
	.plbox_content-item-f1{width:80rpx;}
	.plbox_content-item-f1-img{width:60rpx;height:60rpx;border-radius:50%}
	.plbox_content-item-f2{flex:1}
	.plbox_content-item-f2-t1{font-size:28rpx}
	.plbox_content-item-f2-t2{color:#000;margin:10rpx 0;line-height:60rpx;}
	.plbox_content-item-f2-t3{display:flex;flex-direction:row;flex:1}
	.plbox_content-item-f2-t3-x1{color:#999;font-size:26rpx;}
	.plbox_content-item-f2-t3-x2{flex:1;display:flex;flex-direction:row;}
	.plbox_content-item-f2-pzan{display:flex;flex-direction:row}
	.plbox_content-item-f2-pzan-img{width:32rpx;height:32rpx;margin-right:10rpx}
	.plbox_content-item-f2-pzan-txt{font-size:28rpx;color:#666}
	.plbox_content-item-f2-phuifu{margin-left:6px;color:#507DAF;font-size:26rpx}
	.plbox_content-relist{flex:1;background:#f5f5f5;padding:20rpx 20rpx 10rpx 20rpx;margin-bottom:20rpx}
	.plbox_content-relist-item2{font-size:24rpx;margin-bottom:10rpx}
	.plbox_content-relist-headimg{width:30rpx;height:30rpx;border-radius:15rpx;margin-right:8rpx}
	.plbox_content-relist-nickname{color:#333;font-size:24rpx}
	.plbox_content-relist-f2{margin-top:10rpx}

	.comment-nodata{color:#666;font-size:26rpx}
	.comment-nomore{flex:1;color:#999;height:60rpx;line-height:60rpx;font-size:26rpx;text-align:center}

	@supports(bottom: env(safe-area-inset-bottom)){
		.dp-tabbar-bot{padding-bottom:0 !important;}
		.dp-tabbar-bar{padding-bottom:0 !important;}
	}
</style>

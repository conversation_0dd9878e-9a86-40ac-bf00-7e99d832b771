<template>
	<view class="container">
		<swiper class="swiper" circular :indicator-dots="indicatorDots" :autoplay="autoplay" :interval="interval"
			:duration="duration">
			<swiper-item v-for="(item, index) in bannderList" :key="index">
				<image :src="item.url" mode="aspectFill" style="width: 100%;height: 100%;"></image>
			</swiper-item>
		</swiper>
		<view class="menu-list flex">
			<view class="flex1 flex-column y-center" v-for="(item, index) in menuList" :key="index" @tap="onMenuClick(item)">
				<image class="image" :src="item.image" mode="aspectFill"></image>
				<view>{{lang(item.name)}}</view>
			</view>
		</view>
		<view class="notice y-center">
			<image src="https://wuliu.ohomer.cn/upload/2/20231026/1841054217.png" style="width: 40rpx;height: 40rpx;"></image>
			<view class="flex1">
				<swiper :vertical="true" style="height: 50rpx;">
					<swiper-item v-for="(item, index) in noticeList" :key="index" class="y-center">
						<view class="line1">{{item.content}}</view>
					</swiper-item>
				</swiper>
			</view>
			<view>查看更多</view>
		</view>
		<view class="address-block">
			<view class="x-between">
				<view>{{lang('My default address')}}</view>
				<view class="y-center">
					<view class="button1 xy-center">{{lang('Replacement of warehouses')}}</view>
					<view class="button xy-center">{{lang('Copy Address')}}</view>
				</view>
			</view>
			<view>
				<text>{{lang('addressee')}}：</text>
				<text>(800941) </text>
			</view>
			<view>
				<text>{{lang('mobile telephone number')}}：</text>
				<text>17575030077</text>
			</view>
			<view>
				<text>{{lang('zip code')}}：</text>
				<text>510450</text>
			</view>
			<view>
				<text>{{lang('delivery address')}}：</text>
				<text>广东广州仓库800941白云区白云湖唐阁二社八 零零九四一新基大街22号(800941) </text>
			</view>
		</view>
		<view class="order-block">
			<view>{{lang('Latest Order Alerts')}}</view>
			<view v-for="(item, index) in orderList" :key="index" class="order-item">
				<view class="_header x-between">
					<view class="y-center">
						<image src="../../static/img/my.png" style="width: 48rpx;height: 48rpx;margin-right: 10rpx;"></image>
						<view>{{item.title}}</view>
					</view>
					<view class="color1">{{item.status}}</view>
				</view>
				<view class="_content">
					<view class="y-center">
						<image class="image" :src="item.image" mode="aspectFill"></image>
						<view class="">
							<view>{{item.name}}</view>
							<view class="info" style="margin-top: 10rpx;">{{item.info}}</view>
						</view>
					</view>
					<view style="margin-top: 20rpx;">{{lang('cost')}}：<span class="color1">¥{{item.price}}</span></view>
					<view class="x-between" style="margin-top: 10rpx;">
						<view></view>
						<view class="y-center">
							<view class="button1 xy-center">{{lang('particulars')}}</view>
							<view class="button1 xy-center">{{lang('Close Order')}}</view>
							<view class="button xy-center">{{lang('Go and pay')}}</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	var app = getApp();
	export default {
		data() {
			return {
				indicatorDots: true,
				autoplay: true,
				interval: 2000,
				duration: 500,
				bannderList: [{
					url: 'https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fimage109.360doc.com%2FDownloadImg%2F2023%2F11%2F1102%2F275164660_5_20231111021737523&refer=http%3A%2F%2Fimage109.360doc.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1723034414&t=ecaddefa9b01abdd8213bd39fbc8fe00'
				}],
				menuList: [{
						name: 'shipment address',
						image: 'https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fimage109.360doc.com%2FDownloadImg%2F2023%2F11%2F1102%2F275164660_5_20231111021737523&refer=http%3A%2F%2Fimage109.360doc.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1723034414&t=ecaddefa9b01abdd8213bd39fbc8fe00',
						url: ''
					},
					{
						name: 'Order management',
						image: 'https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fimage109.360doc.com%2FDownloadImg%2F2023%2F11%2F1102%2F275164660_5_20231111021737523&refer=http%3A%2F%2Fimage109.360doc.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1723034414&t=ecaddefa9b01abdd8213bd39fbc8fe00',
						url: '/pages/logistics/order'
					},
					{
						name: 'Record Search',
						image: 'https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fimage109.360doc.com%2FDownloadImg%2F2023%2F11%2F1102%2F275164660_5_20231111021737523&refer=http%3A%2F%2Fimage109.360doc.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1723034414&t=ecaddefa9b01abdd8213bd39fbc8fe00',
						url: '/pages/logistics/record'
					},
					{
						name: 'Freight estimates',
						image: 'https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fimage109.360doc.com%2FDownloadImg%2F2023%2F11%2F1102%2F275164660_5_20231111021737523&refer=http%3A%2F%2Fimage109.360doc.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1723034414&t=ecaddefa9b01abdd8213bd39fbc8fe00',
						url: '/pages/logistics/carriage'
					}
				],
				noticeList: [{
					content: '通知公告运费规则，提货方式....'
				}],
				orderList: [{
					title: 'E速物流',
					name: 'E速物流，泰国专运',
					info: '陆运 （普货） ',
					status: '待支付',
					price: '320.00',
					image: 'https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fimage109.360doc.com%2FDownloadImg%2F2023%2F11%2F1102%2F275164660_5_20231111021737523&refer=http%3A%2F%2Fimage109.360doc.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1723034414&t=ecaddefa9b01abdd8213bd39fbc8fe00'
				}, {
					title: 'E速物流',
					name: 'E速物流，泰国专运',
					info: '陆运 （普货） ',
					status: '待支付',
					price: '320.00',
					image: 'https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fimage109.360doc.com%2FDownloadImg%2F2023%2F11%2F1102%2F275164660_5_20231111021737523&refer=http%3A%2F%2Fimage109.360doc.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1723034414&t=ecaddefa9b01abdd8213bd39fbc8fe00'
				}]
			}
		},
		methods: {
			lang: function(k) {
				return app.lang(k);
			},
			onMenuClick(data) {
				uni.navigateTo({
					url: data.url
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		color: #3D3D3D;
		font-size: 28rpx;
	}

	.menu-list {
		margin-top: 20rpx;
		padding: 20rpx;
		color: rgb(102, 102, 102);

		.image {
			width: 90rpx;
			height: 90rpx;
			margin-bottom: 20rpx;
		}
	}

	.notice {
		padding: 20rpx;
	}

	.address-block {
		width: 702rpx;
		height: 316rpx;
		background: rgba(210, 228, 224, 0.5);
		border-radius: 12rpx;
		margin: 0 auto;
		padding: 20rpx;
		box-sizing: border-box;

		.button {
			width: 148rpx;
			height: 52rpx;
			border-radius: 8rpx;
			background: #4187FF;
			color: #FFFFFF;
			margin-left: 20rpx;
		}

		.button1 {
			width: 148rpx;
			height: 52rpx;
			border-radius: 8rpx;
			// background: #FFFFFF;
			color: #4187FF;
			border: 2rpx solid #4187FF;
			margin-left: 20rpx;
		}
	}

	.order-block {
		padding: 20rpx;

		.order-item {
			margin-top: 20rpx;
			background: #eaeaea;

			._header {
				padding: 20rpx;
				border-bottom: 1rpx solid #D8D8D8;
			}

			._content {
				padding: 20rpx;
			}

			.color1 {
				color: #FD4A46;
			}

			.info {
				color: #999999;
			}

			.image {
				width: 120rpx;
				height: 120rpx;
				border-radius: 10rpx;
				margin-right: 20rpx;
			}

			.button {
				width: 148rpx;
				height: 52rpx;
				border-radius: 8rpx;
				background: #FF3333;
				color: #FFFFFF;
				margin-left: 20rpx;
			}

			.button1 {
				width: 148rpx;
				height: 52rpx;
				border-radius: 8rpx;
				// background: #FFFFFF;
				color: #3D3D3D;
				border: 2rpx solid #3D3D3D;
				margin-left: 20rpx;
			}
		}
	}

	.flex {
		display: flex;
	}

	.flex1 {
		flex: 1;
	}

	.flex-column {
		display: flex;
		flex-direction: column;
	}

	.x-center {
		display: flex;
		justify-content: center;
	}

	.x-between {
		display: flex;
		justify-content: space-between;
	}

	.y-center {
		display: flex;
		align-items: center;
	}

	.xy-center {
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.line1 {
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
	}
</style>
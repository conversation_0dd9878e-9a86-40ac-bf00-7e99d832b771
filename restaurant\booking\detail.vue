<template>
	<view class="container">
		<block v-if="isload">
			<view class="head-bg">
				<view class="title" v-if="detail.check_status == 0 && detail.status == 0">预定成功，请支付</view>
				<view class="title" v-if="detail.check_status == 0 && detail.status > 0">预定成功，等待审核</view>
				<view class="title" v-if="detail.check_status == 1 && detail.status > 0">预定成功</view>
				<view class="title" v-if="detail.check_status == 1 && detail.status == 0">预定成功，请支付</view>
				<view class="title" v-if="detail.check_status == -1 && detail.status > 0">预定失败，商家驳回</view>
				<view class="text-center mt20" v-if="detail.check_status != -1">请在预定时间20分钟内到店。</view>
				<view class="text-center mt20" v-if="detail.check_status == -1 && detail.status == 0">请重新预定。</view>
			</view>
			<view class="card-view">
				<view class="card-wrap">
					<view class="card-title">{{business.name}}</view>
					<view class="mt20">{{business.address}}</view>
				</view>
				<view class="card-wrap">
					<view class="card-title">预定信息</view>
					<view class="info-item mt">
						<view class="t1">预定人</view>
						<view class="t2">{{detail.linkman}}</view>
					</view>
					<view class="info-item">
						<view class="t1">手机号</view>
						<view class="t2">{{detail.tel}}</view>
					</view>
					<view class="info-item">
						<view class="t1">预定时间</view>
						<view class="t2">{{detail.booking_time}}</view>
					</view>
					<view class="info-item">
						<view class="t1">用餐人数</view>
						<view class="t2">{{detail.seat}}</view>
					</view>
					<view class="info-item">
						<view class="t1">预定桌台</view>
						<view class="t2">{{detail.tableName}}</view>
					</view>
					<view class="info-item info-textarea">
						<view class="t1">备注信息</view>
						<view class="t2">{{detail.message}}</view>
					</view>
				</view>
			</view>
			
			<view class="btn-view button-sp-area">
				<button type="default" class="btn-default" @tap="cancel">取消</button>
				<button type="primary" v-if="detail.status == 0" @click="goto" :data-url="'/pages/pay/pay?id=' + detail.payorderid">去支付</button>
			</view>
		</block>
		<loading v-if="loading"></loading>
		<dp-tabbar :opt="opt"></dp-tabbar>
	</view>
</template>

<script>
var app = getApp();
	export default {
		data() {
			return {
				opt:{},
				loading:false,
				isload: false,
				
				detail:{},
				business:{}
			}
		},
		onLoad: function (opt) {
			this.opt = app.getopts(opt);
			this.getdata();
		},
		onPullDownRefresh: function () {
			this.getdata();
		},
		methods: {
			getdata: function () {
				var that = this;
				that.loading = true;
				app.get('ApiRestaurantBooking/detail', {id:that.opt.id}, function (res) {
					that.loading = false;
					if(res.status == 0){
						app.alert(res.msg,function(){
							app.goback();
						});return;
					}
					that.detail = res.data;
					that.business = res.business;
					
					that.loaded();
					//
				});
			},
			cancel:function () {
				var that = this;
				that.loading = true;
				app.get('ApiRestaurantBooking/del', {id:that.opt.id}, function (res) {
					that.loading = false;
					if(res.status == 0){
						app.alert(res.msg,function(){
							app.goback();
						});return;
					}
					if(res.status == 1){
						app.alert(res.msg,function(){
							app.goback();
						});return;
					}
					//
				});
			},
		}
	}
</script>

<style>
	.text-center { text-align: center;}
	.head-bg {width: 100%;height: 400rpx; background: linear-gradient(120deg, #FF7D15 0%, #FC5729 100%); color: #fff;padding-top:100rpx;}
	.head-bg .title { text-align: center; }
	
	.card-wrap { background-color: #FFFFFF; border-radius: 10rpx;padding: 30rpx; margin: 30rpx auto 0; width: 94%;}
	.card-wrap:first-child{ margin-top: -100rpx; }
	.card-wrap .card-title {font-size: 34rpx; color: #333; font-weight: bold;}
	
.info-item{ display:flex;align-items:center;width: 100%; background: #fff;padding:0 3%;  border-bottom: 1px #f3f3f3 solid;height:96rpx;line-height:96rpx}
.info-item:last-child{border:none}
.info-item .t1{ width: 200rpx;color: #8B8B8B;font-weight:bold;height:96rpx;line-height:96rpx}
.info-item .t2{ color:#444444;text-align:right;flex:1;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;overflow:hidden;}
.info-item .t3{ width: 26rpx;height:26rpx;margin-left:20rpx}
.info-textarea { height: auto; line-height: 40rpx;}
.info-textarea .t2{display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp: unset;overflow: scroll;}

.btn-view { display: flex;justify-content: space-between; margin: 30rpx 0;}
.btn-view button{ width: 45%; border-radius: 10rpx;}
.btn-default {background-color: #FFFFFF;}
</style>

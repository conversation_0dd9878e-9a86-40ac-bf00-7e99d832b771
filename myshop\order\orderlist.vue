<template>
  <view class="container">
    <block v-if="isload">
      <dd-tab :itemdata="[lang('all'),lang('for the payment'),lang('to send the goods'),lang('for the goods'),lang('has been completed')]" :itemst="['all','0','1','2','3']" :st="st" :isfixed="true"
        @changetab="changetab"></dd-tab>
      <view class="order-content">
        <block v-for="(item, index) in datalist" :key="index">
          <view class="order-box" @tap="goto" :data-url="'detail?id=' + item.id">
            <view class="head">
              <view class="f1" v-if="item.bid!=0" @tap.stop="goto" :data-url="'/pages/business/index?id=' + item.bid">
                <image src="/static/img/ico-shop.png"></image> {{item.binfo.name}}
              </view>
              <view class="f1" v-else>
                <image :src="item.binfo.logo" class="logo-row"></image> {{item.binfo.name}}
              </view>
              <view class="flex1"></view>
              <text v-if="item.status==0" class="st0">{{lang('for the payment')}}</text>
              <text v-if="item.status==1 && item.freight_type!=1" class="st1">{{lang('to send the goods')}}</text>
              <text v-if="item.status==1 && item.freight_type==1" class="st1">{{lang('to pick up the goods')}}</text>
              <text v-if="item.status==2" class="st2">{{lang('for the goods')}}</text>
              <text v-if="item.status==3" class="st3">{{lang('has been completed')}}</text>
              <text v-if="item.status==4" class="st4">{{lang('closed')}}</text>
            </view>

            <block v-for="(item2, idx) in item.prolist" :key="idx">
              <view class="content" :style="idx+1==item.procount?'border-bottom:none':''">
                <view @tap.stop="goto" :data-url="'/myshop/shop/product?id=' + item2.proid">
                  <image :src="item2.pic"></image>
                </view>
                <view class="detail">
                  <text class="t1">{{item2.name}}</text>
                  <text class="t2">{{item2.ggname}}</text>
                  <view class="t3">
                    <text class="x1 flex1">{{item2.sell_price}}</text>
                    <text class="x2">×{{item2.num}}</text>
                  </view>
                </view>
              </view>
            </block>
            <view class="bottom">
              <text>{{lang('total number of')}}{{item.procount}} {{lang('eal payment')}}:{{item.totalprice}} <span
                  v-if="item.balance_price > 0 && item.balance_pay_status == 0"
                  style="display: block; float: right;">{{lang('balance payment')}}：{{item.balance_price}}</span></text>
              <text v-if="item.refund_status==1" style="color:red;padding-left:6rpx">{{lang('a refund of')}}{{item.refund_money}}</text>
              <text v-if="item.refund_status==2" style="color:red;padding-left:6rpx">{{lang('have a refund')}}{{item.refund_money}}</text>
              <text v-if="item.refund_status==3" style="color:red;padding-left:6rpx">{{lang('refund application has been rejected')}}</text>

            </view>
            <view class="bottom" v-if="item.tips!=''">
              <text style="color:red">{{item.tips}}</text>
            </view>
            <view class="op">
              <block v-if="([1,2,3]).includes(item.status) && item.invoice">
                <view class="btn2" @tap.stop="goto" :data-url="'invoice?type=shop&orderid=' + item.id">{{lang('invoice')}}</view>
              </block>
                  
              <block >
                <view v-if="item.bid == 33" @tap.stop="goto" :data-url="'/pages/order/orderdetail?id=' + item.id" class="btn2">{{lang('details')}}</view>
                <view v-else @tap.stop="goto" :data-url="'detail?id=' + item.id" class="btn2">{{lang('details')}}</view>
              </block>
                
              <block v-if="item.status==0">
                <view class="btn2" @tap.stop="toclose" :data-id="item.id">{{lang('close the order')}}</view>
                <view class="btn1" v-if="item.paytypeid!=5" :style="{background:t('color1')}" @tap.stop="goto"
                  :data-url="'/pages/pay/pay?id=' + item.payorderid">{{lang('go to the payment')}}</view>
                <view class="btn1" v-if="item.paytypeid==5" :style="{background:t('color1')}" @tap.stop="goto"
                  :data-url="'/pages/pay/transfer?id=' + item.payorderid">{{lang('proof of payment')}}</view>
              </block>
              <block v-if="item.status==1">
                <block v-if="item.paytypeid!='4'">
                  <view class="btn2" @tap.stop="goto"
                    :data-url="'refundSelect?orderid=' + item.id + '&price=' + item.totalprice"
                    v-if="canrefund==1 && item.refundnum < item.procount">{{lang('a refund')}}</view>
                </block>
                <block v-else>
                  <view class="btn2">{{codtxt}}</view>
                </block>
              </block>
              <block v-if="item.status==2">
                <block v-if="item.paytypeid!='4'">
                  <view class="btn2" @tap.stop="goto"
                    :data-url="'refundSelect?orderid=' + item.id + '&price=' + item.totalprice"
                    v-if="canrefund==1 && item.refundnum < item.procount">{{lang('a refund')}}</view>
                </block>
                <block v-else>
                  <view class="btn2">{{codtxt}}</view>
                </block>
                <block v-if="item.freight_type!=3 && item.freight_type!=4">
                  <view class="btn2" v-if="item.express_type =='express_wx'" @tap.stop="logistics"
                    :data-express_type="item.express_type" :data-express_com="item.express_com"
                    :data-express_no="item.express_no" :data-express_content="item.express_content">{{lang('order tracking')}}</view>
                  <view class="btn2" v-else @tap.stop="logistics" :data-express_type="item.express_type"
                    :data-express_com="item.express_com" :data-express_no="item.express_no"
                    :data-express_content="item.express_content">{{lang('check the logistics')}}</view>
                </block>

                <view v-if="item.balance_pay_status == 0 && item.balance_price > 0" class="btn1"
                  :style="{background:t('color1')}" @tap.stop="goto"
                  :data-url="'/pages/pay/pay?id=' + item.balance_pay_orderid">{{lang('pay the balance payment')}}</view>
                <view v-if="item.paytypeid!='4' && (item.balance_pay_status==1 || item.balance_price==0)" class="btn1"
                  :style="{background:t('color1')}" @tap.stop="orderCollect" :data-id="item.id">{{lang('confirm the goods')}}</view>
              </block>
              <block v-if="(item.status==1 || item.status==2) && item.freight_type==1">
                <view class="btn2" @tap.stop="showhxqr" :data-hexiao_qr="item.hexiao_qr">{{lang('the verification code')}}</view>
              </block>
              <view v-if="item.refundCount" class="btn2" @tap.stop="goto" :data-url="'refundlist?orderid='+ item.id">
                {{lang('to see a refund')}}</view>
              <block v-if="item.status==3 || item.status==4">
                <view class="btn2" @tap.stop="todel" :data-id="item.id">{{lang('delete the order')}}</view>
              </block>
              <block v-if="item.bid>0 && item.status==3">
                <view v-if="item.iscommentdp==0" class="btn1" :style="{background:t('color1')}" @tap.stop="goto"
                  :data-url="'/myshop/order/commentdp?orderid=' + item.id">{{lang('evaluation of the store')}}</view>
              </block>
            </view>
          </view>
        </block>
      </view>
      <nomore v-if="nomore"></nomore>
      <nodata v-if="nodata"></nodata>

      <uni-popup id="dialogHxqr" ref="dialogHxqr" type="dialog">
        <view class="hxqrbox">
          <image :src="hexiao_qr" @tap="previewImage" :data-url="hexiao_qr" class="img" />
          <view class="txt">{{lang('please show me the verification code to cancel after verification to cancel after verification')}}</view>
          <view class="close" @tap="closeHxqr">
            <image src="/static/img/close2.png" style="width:100%;height:100%" />
          </view>
        </view>
      </uni-popup>

      <uni-popup id="dialogSelectExpress" ref="dialogSelectExpress" type="dialog">
        <view style="background:#fff;padding:20rpx 30rpx;border-radius:10rpx;width:600rpx" v-if="express_content">
          <view class="sendexpress-item" v-for="(item, index) in express_content" :key="index" @tap="goto"
            :data-url="'/myshop/order/logistics?express_com=' + item.express_com + '&express_no=' + item.express_no"
            style="display: flex;border-bottom: 1px solid #f5f5f5;padding:20rpx 0;">
            <view class="flex1">{{item.express_com}} - {{item.express_no}}</view>
            <image src="/static/img/arrowright.png" style="width:30rpx;height:30rpx" />
          </view>
        </view>
      </uni-popup>

    </block>
    <loading v-if="loading"></loading>
    <dp-tabbar :opt="opt"></dp-tabbar>
    <popmsg ref="popmsg"></popmsg>
  </view>
</template>

<script>
var app = getApp();

export default {
  data () {
    return {
      opt: {},
      loading: false,
      isload: false,
      menuindex: -1,

      st: 'all',
      datalist: [],
      pagenum: 1,
      nomore: false,
      nodata: false,
      codtxt: "",
      canrefund: 1,
      express_content: '',
      selectExpressShow: false,
      hexiao_qr: '',
    };
  },

  onLoad: function (opt) {
    this.opt = app.getopts(opt);
    if (this.opt && this.opt.st) {
      this.st = this.opt.st;
    }
    this.getdata();
	uni.setNavigationBarTitle({
		title:app.lang("SGMAll"),
		success() {
			console.log(app.lang("SGMAll"))
		}
	})
  },
  onPullDownRefresh: function () {
    this.getdata();
  },
  onReachBottom: function () {
    if (!this.nodata && !this.nomore) {
      this.pagenum = this.pagenum + 1;
      this.getdata(true);
    }
  },
  methods: {
	  lang: function(k) {
	  	return app.lang(k);
	  }, 
    getdata: function (loadmore) {
      if (!loadmore) {
        this.pagenum = 1;
        this.datalist = [];
      }
      var that = this;
      var pagenum = that.pagenum;
      var st = that.st;
      that.nodata = false;
      that.nomore = false;
      that.loading = true;
      app.post('ApiOrder/orderlist', { st: st, pagenum: pagenum }, function (res) {
        that.loading = false;
        var data = res.datalist;
        if (pagenum == 1) {
          that.codtxt = res.codtxt;
          that.canrefund = res.canrefund;
          that.datalist = data;
          if (data.length == 0) {
            that.nodata = true;
          }
          that.loaded();
        } else {
          if (data.length == 0) {
            that.nomore = true;
          } else {
            var datalist = that.datalist;
            var newdata = datalist.concat(data);
            that.datalist = newdata;
          }
        }
      });
    },
    changetab: function (st) {
      this.st = st;
      uni.pageScrollTo({
        scrollTop: 0,
        duration: 0
      });
      this.getdata();
    },
    toclose: function (e) {
      var that = this;
      var orderid = e.currentTarget.dataset.id;
      app.confirm(that.lang('sure you want to shut down the order'), function () {
        app.showLoading(that.lang('in the submission'));
        app.post('ApiOrder/closeOrder', { orderid: orderid }, function (data) {
          app.showLoading(false);
          app.success(data.msg);
          setTimeout(function () {
            that.getdata();
          }, 1000);
        });
      });
    },
    todel: function (e) {
      var that = this;
      var orderid = e.currentTarget.dataset.id;
      app.confirm(that.lang('sure you want to delete the order'), function () {
        app.showLoading(that.lang('delete the'));
        app.post('ApiOrder/delOrder', { orderid: orderid }, function (data) {
          app.showLoading(false);
          app.success(data.msg);
          setTimeout(function () {
            that.getdata();
          }, 1000);
        });
      });
    },
    orderCollect: function (e) {
      var that = this;
      var orderid = e.currentTarget.dataset.id;
      app.confirm(that.lang('sure you want to the goods'), function () {
        app.showLoading(that.lang('in the submission'));
        app.post('ApiOrder/orderCollect', { orderid: orderid }, function (data) {
          app.showLoading(false);
          app.success(data.msg);
          setTimeout(function () {
            that.getdata();
          }, 1000);
        });
      });
    },
    logistics: function (e) {
      var express_com = e.currentTarget.dataset.express_com
      var express_no = e.currentTarget.dataset.express_no
      var express_content = e.currentTarget.dataset.express_content
      var express_type = e.currentTarget.dataset.express_type
      console.log(express_content)
      if (!express_content) {
        app.goto('/myshop/order/logistics?express_com=' + express_com + '&express_no=' + express_no + '&type=' + express_type);
      } else {
        this.express_content = JSON.parse(express_content);
        console.log(express_content);
        this.$refs.dialogSelectExpress.open();
      }
    },
    hideSelectExpressDialog: function () {
      this.$refs.dialogSelectExpress.close();
    },
    showhxqr: function (e) {
      this.hexiao_qr = e.currentTarget.dataset.hexiao_qr
      this.$refs.dialogHxqr.open();
    },
    closeHxqr: function () {
      this.$refs.dialogHxqr.close();
    },
  }
};
</script>
<style>
.container {
  width: 100%;
  margin-top: 90rpx;
}
.order-content {
  display: flex;
  flex-direction: column;
}
.order-box {
  width: 94%;
  margin: 10rpx 3%;
  padding: 6rpx 3%;
  background: #fff;
  border-radius: 8px;
}
.order-box .head {
  display: flex;
  width: 100%;
  border-bottom: 1px #f4f4f4 solid;
  height: 70rpx;
  line-height: 70rpx;
  overflow: hidden;
  color: #999;
}
.order-box .head .f1 {
  display: flex;
  align-items: center;
  color: #333;
}
.order-box .head image {
  width: 34rpx;
  height: 34rpx;
  margin-right: 4px;
}
.order-box .head .st0 {
  width: 140rpx;
  color: #ff8758;
  text-align: right;
}
.order-box .head .st1 {
  width: 140rpx;
  color: #ffc702;
  text-align: right;
}
.order-box .head .st2 {
  width: 140rpx;
  color: #ff4246;
  text-align: right;
}
.order-box .head .st3 {
  width: 140rpx;
  color: #999;
  text-align: right;
}
.order-box .head .st4 {
  width: 140rpx;
  color: #bbb;
  text-align: right;
}

.order-box .content {
  display: flex;
  width: 100%;
  padding: 16rpx 0px;
  border-bottom: 1px #f4f4f4 dashed;
  position: relative;
}
.order-box .content:last-child {
  border-bottom: 0;
}
.order-box .content image {
  width: 140rpx;
  height: 140rpx;
}
.order-box .content .detail {
  display: flex;
  flex-direction: column;
  margin-left: 14rpx;
  flex: 1;
}
.order-box .content .detail .t1 {
  font-size: 26rpx;
  line-height: 36rpx;
  margin-bottom: 10rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}
.order-box .content .detail .t2 {
  height: 46rpx;
  line-height: 46rpx;
  color: #999;
  overflow: hidden;
  font-size: 26rpx;
}
.order-box .content .detail .t3 {
  display: flex;
  height: 40rpx;
  line-height: 40rpx;
  color: #ff4246;
}
.order-box .content .detail .x1 {
  flex: 1;
}
.order-box .content .detail .x2 {
  width: 100rpx;
  font-size: 32rpx;
  text-align: right;
  margin-right: 8rpx;
}

.order-box .bottom {
  width: 100%;
  padding: 10rpx 0px;
  border-top: 1px #f4f4f4 solid;
  color: #555;
}
.order-box .op {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-end;
  align-items: center;
  width: 100%;
  padding: 10rpx 0px;
  border-top: 1px #f4f4f4 solid;
  color: #555;
}

.btn1 {
  margin-left: 20rpx;
  margin-top: 10rpx;
  width: 160rpx;
  height: 60rpx;
  line-height: 60rpx;
  color: #fff;
  border-radius: 3px;
  text-align: center;
}
.btn2 {
  margin-left: 20rpx;
  margin-top: 10rpx;
  width: 160rpx;
  height: 60rpx;
  line-height: 60rpx;
  color: #333;
  background: #fff;
  border: 1px solid #cdcdcd;
  border-radius: 3px;
  text-align: center;
}

.hxqrbox {
  background: #fff;
  padding: 50rpx;
  position: relative;
  border-radius: 20rpx;
}
.hxqrbox .img {
  width: 400rpx;
  height: 400rpx;
}
.hxqrbox .txt {
  color: #666;
  margin-top: 20rpx;
  font-size: 26rpx;
  text-align: center;
}
.hxqrbox .close {
  width: 50rpx;
  height: 50rpx;
  position: absolute;
  bottom: -100rpx;
  left: 50%;
  margin-left: -25rpx;
  border: 1px solid rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  padding: 8rpx;
}
</style>
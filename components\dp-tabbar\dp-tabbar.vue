<template>
  <view class="dp-tabbar" v-if="currentIndex>-1">
    <view class="dp-tabbar-bot"></view>
    <view class="dp-tabbar-bar" :style="{backgroundColor:menudata.backgroundColor}">
      <view @click="goto" :data-url="item.pagePath" :data-index="index"
        :data-opentype="index!=0&&getplatform()=='baidu'?'':opentype" class="dp-tabbar-item"
        v-for="(item,index) in menudata.list" :key="item.id">
        <view class="dp-tabbar-image-box">
          <image v-if="currentIndex===index" class="dp-tabbar-icon" :src="item.selectedIconPath"></image>
          <image v-else class="dp-tabbar-icon" :src="item.iconPath"></image>
        </view>
        <view class="dp-tabbar-text" :style="{color:item.color}">{{item.text}}</view>
      </view>
    </view>
  </view>
</template>
<script>
  var app = getApp();
  export default {
    data() {
      return {
        menudata: {},
        currentIndex: -1,
        i: 0,
        opentype: 'reLaunch',
      }
    },
    mounted: function() {
      this.settabbar();
    },
    props: {
      opt: {}
    },
    methods: {
      settabbar: function() {
        var that = this
        if (!app.globalData.isinit && this.i < 100) {
          setTimeout(function() {
            that.i++;
            that.settabbar();
          }, 100)
        } else {
          var opentype = 'reLaunch';
          var currenturl = app._url();
          var opt = this.opt;
          if (this.opt && this.opt.id) {
            currenturl += '?id=' + this.opt.id
          } else if (this.opt && this.opt.cid) {
            currenturl += '?cid=' + this.opt.cid
          } else if (this.opt && this.opt.gid) {
            currenturl += '?gid=' + this.opt.gid
          } else if (this.opt && this.opt.bid) {
            currenturl += '?bid=' + this.opt.bid
          }
          var currentIndex = -1;
          var hastabbar = false;
          var menudata = JSON.parse(JSON.stringify(app.globalData.initdata.menudata));
          // console.log('menudata', menudata);
          var tablist = menudata['list'];
          for (var i = 0; i < tablist.length; i++) {
            if (tablist[i]['pagePath'] == currenturl) {
              currentIndex = i;
              hastabbar = true;
              menudata['list'][i].color = menudata['selectedColor']
            } else {
              menudata['list'][i].color = menudata['color']
            }
          }
          if (hastabbar == false) {
            var menu2data = JSON.parse(JSON.stringify(app.globalData.initdata.menu2data))
            if (menu2data.length > 0) {
              for (var i in menu2data) {
                if (this.opt && this.opt.bid)
                  menu2data[i].indexurl = (menu2data[i].indexurl).replace('[bid]', this.opt.bid);
                if (menu2data[i].indexurl == currenturl) {
                  hastabbar = true;
                  currentIndex = 10;
                  menudata = menu2data[i]
                  console.log(menudata);
                  console.log(this.opt)
                  for (var j in menudata.list) {
                    if (this.opt && this.opt.bid)
                      menudata.list[j].pagePath = (menudata.list[j].pagePath).replace('[bid]', this.opt.bid);
                    if (menudata.list[j].pagePath == currenturl && menudata['selectedColor']) {
                      menudata['list'][j].color = menudata['selectedColor'];
                      if (menudata['list'][j]['selectedIconPath']) {
                        menudata['list'][j].iconPath = menudata['list'][j]['selectedIconPath'];
                      }
                    } else if (menudata['color']) {
                      menudata['list'][j].color = menudata['color'];
                    }
                  }
                  opentype = '';
                  console.log(menudata);
                }
              }
            }
          }
          that.opentype = opentype
          that.currentIndex = currentIndex
          that.menudata = menudata
          //app.globalData.currentIndex = currentIndex;
          that.$emit('getmenuindex', currentIndex)
        }
      },
    }
  }
</script>
<style>
  .dp-tabbar {
    height: auto;
    position: relative;
  }

  .dp-tabbar-icon {
    width: 50rpx;
    height: 50rpx;
  }

  .dp-tabbar-bar {
    display: flex;
    flex-direction: row;
    width: 100%;
    height: 100rpx;
    position: fixed;
    bottom: 0;
    padding: 10rpx 0 0 0;
    background: #fff;
    font-size: 24rpx;
    color: #999;
    border-top: 1px solid #e5e5e5;
    z-index: 999999;
    box-sizing: content-box
  }

  .dp-tabbar-item {
    flex: 1;
    text-align: center;
    overflow: hidden;
    align-items: center
  }

  .dp-tabbar-image-box {
    height: 54rpx;
    margin-bottom: 4rpx;
  }

  .dp-tabbar-text {
    line-height: 30rpx;
    font-size: 24rpx;
  }

  .dp-tabbar-bot {
    height: 110rpx;
    width: 100%;
    box-sizing: content-box
  }

  @supports(bottom: env(safe-area-inset-bottom)) {

    .dp-tabbar-bot {
      padding-bottom: env(safe-area-inset-bottom);
    }

    .dp-tabbar-bar {
      padding-bottom: env(safe-area-inset-bottom);
    }

  }
</style>
<template>
<view class="dp-article" :style="{
	backgroundColor:params.bgcolor,margin:(params.margin_y*2.2)+'rpx '+(params.margin_x*2.2)+'rpx 0',
	padding:(params.padding_y*2.2)+'rpx '+(params.padding_x*2.2)+'rpx'
}">
	<!--单排-->
	<view v-if="params.style=='1'" class="article-item1" v-for="(item,index) in data" :key="item.id" @click="goto" :data-url="'/pages/article/detail?id='+item.artid">
		<view class="article-pic" v-if="params.showpic==1">
			<image class="image" :src="item.pic" mode="widthFix"/>
		</view>
		<view class="article-info">
			<view class="p1">{{item.name}}</view>
			<view class="p2">
				<text style="overflow:hidden" class="flex1" v-if="params.showtime==1">{{item.createtime}}</text>
				<text style="overflow:hidden" v-if="params.showreadcount==1">{{lang('read')}} {{item.readcount}}</text>
			</view>
		</view>
	</view>
	<!--双排-->
	<view v-if="params.style=='2'" class="article-item2" v-for="(item,index) in data" :style="{marginRight:index%2==0?'2%':'0'}" :key="item.id" @click="goto" :data-url="'/pages/article/detail?id='+item.artid">
		<view class="article-pic" v-if="params.showpic==1">
			<image class="image" :src="item.pic" mode="widthFix"/>
		</view>
		<view class="article-info">
			<view class="p1">{{item.name}}</view>
			<view class="p2">
				<text style="overflow:hidden" class="flex1" v-if="params.showtime==1">{{item.createtime}}</text>
				<text style="overflow:hidden" v-if="params.showreadcount==1">{{lang('read')}}  {{item.readcount}}</text>
			</view>
		</view>
	</view>
	<!--左图-->
	<view v-if="params.style=='4'" class="article-itemlist" v-for="(item,index) in data" :key="item.id" @click="goto" :data-url="'/pages/article/detail?id='+item.artid">
		<view class="article-pic" v-if="params.showpic==1">
			<image class="image" :src="item.pic" mode="widthFix"/>
		</view>
		<view class="article-info">
			<view class="p1">{{item.name}}</view>
			<view class="p2">
				<text style="overflow:hidden" class="flex1" v-if="params.showtime==1">{{item.createtime}}</text>
				<text style="overflow:hidden" v-if="params.showreadcount==1">{{lang('read')}}  {{item.readcount}}</text>
			</view>
		</view>
	</view>
	<!--右图-->
	<view v-if="params.style=='5'" class="article-itemlist" v-for="(item,index) in data" :key="item.id" @click="goto" :data-url="'/pages/article/detail?id='+item.artid">
		<view class="article-info" style="padding-left:10rpx">
			<view class="p1">{{item.name}}</view>
			<view class="p2">
				<text style="overflow:hidden" class="flex1" v-if="params.showtime==1">{{item.createtime}}</text>
				<text style="overflow:hidden" v-if="params.showreadcount==1">{{lang('read')}}  {{item.readcount}}</text>
			</view>
		</view>
		<view class="article-pic" v-if="params.showpic==1">
			<image class="image" :src="item.pic" mode="widthFix"/>
		</view>
	</view>
	<waterfall-article v-if="params.style=='6'" :list="data" ref="waterfall" :showtime="params.showtime" :showreadcount="params.showreadcount" idKey="artid"></waterfall-article>

</view>
</template>
<script>
		var app = getApp();
	export default {
		props: {
			params:{},
			data:{}
		},
		methods:{
			lang: function(k) {
				return app.lang(k);
			},
		}
	}
</script>
<style>
.dp-article{height: auto; position: relative;overflow: hidden; padding:10rpx 0px; background: #fff;}
.dp-article .article-item1 {width: 100%;display: inline-block;position: relative;margin-bottom:12rpx;background: #fff;border-radius:12rpx;overflow:hidden}
.dp-article .article-item1 .article-pic {width:100%;height:auto;overflow:hidden;background: #ffffff;}
.dp-article .article-item1 .article-pic .image{width: 100%;height:auto}
.dp-article .article-item1 .article-info {padding:10rpx 20rpx;}
.dp-article .article-item1 .article-info .p1{color:#222222;font-weight:bold;font-size:28rpx;line-height:46rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}
.dp-article .article-item1 .article-info .t1{word-break: break-all;text-overflow: ellipsis;overflow: hidden;display: block;font-size: 32rpx;}
.dp-article .article-item1 .article-info .t2{word-break: break-all;text-overflow: ellipsis;padding-top:4rpx;overflow:hidden;}
.dp-article .article-item1 .article-info .p2{flex-grow:0;flex-shrink:0;display:flex;padding:10rpx 0;font-size:24rpx;color:#a88;overflow:hidden}

.dp-article .article-item2 {width: 49%;display: inline-block;position: relative;margin-bottom: 12rpx;background: #fff;border-radius:8rpx;}
/*.article-item2:nth-child(even){margin-right:2%}*/
.dp-article .article-item2 .article-pic {width: 100%;height:0;overflow:hidden;background: #ffffff;padding-bottom:70%;position: relative;border-radius:8rpx 8rpx 0 0;}
.dp-article .article-item2 .article-pic .image{position:absolute;top:0;left:0;width: 100%;height:auto}
.dp-article .article-item2 .article-info {padding:10rpx 20rpx;display:flex;flex-direction:column;}
.dp-article .article-item2 .article-info .p1{color:#222222;font-weight:bold;font-size:28rpx;line-height:46rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}
.dp-article .article-item2 .article-info .p2{flex-grow:0;flex-shrink:0;display:flex;align-items:center;padding-top:10rpx;font-size:24rpx;color:#a88;overflow:hidden}

.dp-article .article-itemlist {width:100%;display: inline-block;position: relative;margin-bottom:12rpx;padding:12rpx;background: #fff;display:flex;border-radius:8rpx;}
.dp-article .article-itemlist .article-pic {width: 35%;height:0;overflow:hidden;background: #ffffff;padding-bottom: 25%;position: relative;}
.dp-article .article-itemlist .article-pic .image{position:absolute;top:0;left:0;width: 100%;height:auto}
.dp-article .article-itemlist .article-info {width: 65%;height:172rpx;overflow:hidden;padding:0 20rpx;display:flex;flex-direction:column;justify-content:space-between}
.dp-article .article-itemlist .article-info .p1{color:#222222;font-weight:bold;font-size:28rpx;line-height:46rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;height:92rpx}
.dp-article .article-itemlist .article-info .p2{display:flex;flex-grow:0;flex-shrink:0;font-size:24rpx;color:#a88;overflow:hidden}

.dp-article .article-waterfall-info{padding:10rpx 20rpx 20rpx 20rpx;display:flex;flex-direction:column;}
.dp-article .article-waterfall-info .p1{color:#222222;font-weight:bold;font-size:28rpx;line-height:46rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;}
.dp-article .article-waterfall-info .p2{flex-grow:0;flex-shrink:0;display:flex;align-items:center;padding-top:10rpx;font-size:24rpx;color:#a88;overflow:hidden}
</style>
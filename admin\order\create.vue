<template>
  <view>
    <form>
      <view class="form-box">
        <view class="form-item">
          <view>客户ID:<text style="color:red"> *</text></view>
          <view class="flex-y-center">
            <input type="text" v-model="form.user_id" placeholder="请输入客户ID"></input>
          </view>
        </view>
        <view class="form-item">
          <view>省市区:<text style="color:red"> *</text></view>
          <view class="flex-y-center">
            <input type="text" v-model="form.area" placeholder="请输入省市区"></input>
          </view>
        </view>
        <view class="form-item">
          <view>地址:<text style="color:red"> *</text></view>
          <view class="flex-y-center">
            <input type="text" v-model="form.address" placeholder="请输入地址"></input>
          </view>
        </view>
        <view class="form-item">
          <view>电话:<text style="color:red"> *</text></view>
          <view class="flex-y-center">
            <input type="text" v-model="form.tel" placeholder="请输入电话"></input>
          </view>
        </view>
        <view class="form-item">
          <view>快递单号:<text style="color:red"> *</text></view>
          <view class="flex-y-center">
            <input type="text" v-model="form.expressnum" placeholder="请输入快递单号"></input>
          </view>
        </view>
        <view class="form-item">
          <view>重量(kg):<text style="color:red"> *</text></view>
          <view class="flex-y-center">
            <input type="number" v-model="form.weight" @blur="onWtBlur" placeholder="请输入整数"></input>
          </view>
        </view>
        <view class="form-item">
          <view>长(cm):<text style="color:red"> *</text></view>
          <view class="flex-y-center">
            <input type="number" v-model="form.length" @blur="onLBlur" placeholder="请输入整数"></input>
          </view>
        </view>
        <view class="form-item">
          <view>宽(cm):<text style="color:red"> *</text></view>
          <view class="flex-y-center">
            <input type="number" v-model="form.width" @blur="onWBlur" placeholder="请输入整数"></input>
          </view>
        </view>
        <view class="form-item">
          <view>高(cm):<text style="color:red"> *</text></view>
          <view class="flex-y-center">
            <input type="number" v-model="form.height" @blur="onHBlur" placeholder="请输入整数"></input>
          </view>
        </view>
        <view class="form-item">
          <view>属性<text style="color:red"> *</text></view>
          <view>
            <picker @change="goodstypeChange" :value="index" :range="goodstype">
              <view class="picker">{{goodstype[index]}}</view>
            </picker>
          </view>
        </view>
        <view class="form-item">
          <view>自提点地址<text style="color:red"> *</text></view>
          <view>
            <picker @change="storeChange" :value="sindex" :range="store_list" range-key="name">
              <view class="picker">{{store_list[sindex].name}}</view>
            </picker>
          </view>
        </view>
        <view class="form-item">
          <view>运输方式:<text style="color:red"> *</text></view>
          <view>
            <picker @change="wuliuChange" :value="windex" :range="wuliu">
              <view class="picker">{{wuliu[windex]}}</view>
            </picker>
          </view>
        </view>
        <view class="form-item">
          <view>拍照<text style="color:red"> *</text></view>
          <view class="upload-box">
            <view class="upload-btn" v-if="!form.ocrimage"
              :style="'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;'"
              @tap="uploadImg"></view>
            <view class="preview" v-if="form.ocrimage">
              <image :src="form.ocrimage" mode="aspectFill"></image>
            </view>
          </view>
        </view>
        <view style="padding:30rpx 0"><button form-type="submit" class="set-btn" @tap="submit"
            :style="'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'">确
            定</button></view>
      </view>

    </form>

  </view>
  </view>
</template>

<script>
  var app = getApp();

  export default {
    onLoad() {
      this.getStoreList();
    },
    data() {
      return {
        goodstype: [
          '普货',
          '敏感货',
          '酒',
          '特货',
          'YAN'
        ],

        wuliu: [
          '陆运公斤',
          '海运公斤',
          '海运立方',
          '陆运立方',
        ],
        index: 0,
        pre_url: app.globalData.pre_url,
        imageSrc: '', // 用于存储选择后的图片地址
        windex: 0,
        store_list: [],
        sindex: 0,

        form: {
          user_id: "",
          area: "",
          address: "",
          tel: "",
          expressnum: "",
          ocrimage: "",
          goodstype: "",
          wuliu: "",
          store_id: "",
          weight: 0.1,
          length: 0,
          width: 0,
          height: 0

        }
      }
    },
    mounted() {

      this.form.goodstype = this.goodstype[this.index]
      this.form.wuliu = this.wuliu[this.windex]


    },

    methods: {
      submit() {
        console.log(this.form);
        app.post('ApiOrder/createOrder', this.form, res => {
          if (res.code == 0) {
            uni.showToast({
              title: res.msg,
              icon: 'success'
            });
            setTimeout(function() {
              uni.navigateTo({
                url: '/admin/order/shoporder'
              })
            }, 1500);
          } else {
            uni.showToast({
              title: res.msg,
              icon: 'none'
            });
            setTimeout(function() {
              uni.navigateBack();
            }, 1500);
          }
        })
      },
      getStoreList() {
        var that = this;
        app.get('ApiIndex/mendian', {}, function(res) {
          console.log(res.status);
          if (res.status == 1) {
            that.store_list = res.data;
            that.form.store_id = that.store_list[that.sindex].id;
          } else {
            uni.showToast({
              title: res.msg,
              icon: 'none'
            });
          }
        });
      },
      storeChange(e) {
        this.sindex = e.detail.value
        this.form.store_id = this.store_list[this.sindex].id;
      },
      onWtBlur(e) {
        let value = parseFloat(e.detail.value);
        if (!isNaN(value)) {
          this.form.weight = Number(value.toFixed(1));
        }
      },
      onLBlur(e) {
        let value = parseFloat(e.detail.value);
        if (!isNaN(value)) {
          this.form.length = Math.round(value);
        }
      },
      onWBlur(e) {
        let value = parseFloat(e.detail.value);
        if (!isNaN(value)) {
          this.form.width = Math.round(value);
        }
      },
      onHBlur(e) {
        let value = parseFloat(e.detail.value);
        if (!isNaN(value)) {
          this.form.height = Math.round(value);
        }
      },
      goodstypeChange(e) {
        this.index = e.detail.value
        this.form.goodstype = this.goodstype[this.index]
      },
      wuliuChange(e) {
        this.windex = e.detail.value
        this.form.wuliu = this.wuliu[this.windex]
      },
      uploadImg: function() {
        var that = this;
        app.chooseImage(function(urls) {
          var headimg = urls[0];
          that.form.ocrimage = headimg;

        }, 1)
      }
    }
  }
</script>


<style scoped>
  .set-btn {
    width: 90%;
    margin: 0 5%;
    height: 96rpx;
    line-height: 96rpx;
    border-radius: 48rpx;
    color: #FFFFFF;
    font-weight: bold;
  }

  .upload-box {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
  }

  .upload-btn {

    /* 设置背景图片路径 */
    background-size: cover;
    /* 背景图片大小设置为覆盖整个按钮 */
    background-repeat: no-repeat;
    /* 不重复背景图片 */
    background-position: center;
    /* 背景图片居中 */
    color: white;
    font-size: 18px;
    width: 200rpx;
    height: 200rpx;
    text-align: center;
    cursor: pointer;
    border: none;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease-in-out;
    /* 添加一些额外的样式，以确保文本可读性 */
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  }

  .upload-btn:active {
    transform: scale(0.95);
  }

  .preview {
    overflow: hidden;
    border-radius: 8px;
    margin-top: 20px;
  }

  .preview image {
    width: 200rpx;
    height: 200rpx;
    display: block;
  }

  .page {
    background-color: #f5f5f5;
  }

  .form-item {
    display: flex;
    justify-content: space-between;
    padding: 20rpx;

  }

  .form-item:last-child {
    border-bottom: none;
  }

  .form-item view:first-child {
    font-size: 28rpx;
    color: #333;
    margin-bottom: 10rpx;
  }

  .form-item view:last-child {
    font-size: 26rpx;
    color: #666;
  }

  .flex-y-center {
    width: 500rpx;
  }

  .form-item:last-child {
    margin-bottom: 0;
  }

  .form-item view:first-child {
    font-size: 28rpx;
    color: #333;
    margin-bottom: 10rpx;
  }

  .form-item view:last-child {
    font-size: 26rpx;
    color: #666;
  }

  .form-item view:last-child input {
    width: 100%;
    height: 80rpx;
    line-height: 80rpx;
    border: 1rpx solid #ccc;
    border-radius: 10rpx;
    padding: 0 20rpx;
  }

  .form-item view:last-child input:focus {
    border-color: #007aff;
  }

  .form-item view:last-child text {
    color: #999;
  }

  .form-item view:last-child text.active {
    color: #007aff;
    display: flex;
    justify-content: space-around;
  }

  .form-box {
    padding: 20rpx;
    background: #fff;
    border-radius: 10rpx;
  }
</style>
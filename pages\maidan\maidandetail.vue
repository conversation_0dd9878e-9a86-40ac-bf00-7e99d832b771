<template>
<view class="container">
	<block v-if="isload">
		<view class="orderinfo">
			<view class="item">
				<text class="t1">{{lang('the order no.')}}</text>
				<text class="t2">{{detail.ordernum}}</text>
			</view>
			<view class="item" v-if="mendian">
				<text class="t1">{{lang('payment of stores')}}</text>
				<text class="t2">{{mendian.name}}</text>
			</view>
			<view class="item">
				<text class="t1">{{lang('the payment amount')}}</text>
				<text class="t2">{{detail.money}}</text>
			</view>
			<view class="item">
				<text class="t1">{{lang('amount of real pay')}}</text>
				<text class="t2" style="font-size:32rpx;color:#e94745">{{detail.paymoney}}</text>
			</view>
			<view class="item">
				<text class="t1">{{lang('terms of payment')}}</text>
				<text class="t2">{{detail.paytype}}</text>
			</view>
			<view class="item">
				<text class="t1">{{lang('state')}}</text>
				<text class="t2" v-if="detail.status==1" style="color:green">{{lang('payment has been')}}</text>
				<text class="t2" v-else style="color:red">{{lang('not paying')}}</text>
			</view>
			<view class="item" v-if="detail.status>0 && detail.paytime">
				<text class="t1">{{lang('time of payment')}}</text>
				<text class="t2">{{detail.paytime}}</text>
			</view>
			<view class="item" v-if="detail.disprice>0">
				<text class="t1">{{t('会员')}}{{lang('discount')}}</text>
				<text class="t2">-{{detail.disprice}}</text>
			</view>
			<view class="item" v-if="detail.scoredk>0">
				<text class="t1">{{t('积分')}}{{lang('deduction')}}</text>
				<text class="t2">-{{detail.scoredk}}</text>
			</view>
			<view class="item" v-if="detail.couponrid">
				<text class="t1">{{t('优惠券')}}{{lang('deduction')}}</text>
				<text class="t2">-{{detail.couponmoney}}</text>
			</view>
			<view class="item" v-if="couponrecord">
				<text class="t1">{{t('优惠券')}}{{lang('the name of the')}}</text>
				<text class="t2">{{couponrecord.couponname}}</text>
			</view>
		</view>
	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
			opt:{},
			loading:false,
      isload: false,
			menuindex:-1,
			
			textset:{},
      prodata: ''
    };
  },

  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.getdata();
		uni.setNavigationBarTitle({
			title:app.lang("Bill payment details"),
			success() {
				console.log(app.lang("Bill payment details"))
			}
		})
  },
	onPullDownRefresh: function () {
		this.getdata();
	},
  methods: {
	  lang: function(k) {
	  	return app.lang(k);
	  },
		getdata: function (option) {
			var that = this;
			that.loading= true;
			app.get('ApiMaidan/maidandetail', {id: that.opt.id}, function (res) {
				that.loading= false;
				that.textset = app.globalData.textset;
				that.detail = res.detail;
				that.couponrecord = res.couponrecord;
				that.mendian = res.mendian;
				that.loaded();
			});
		},
  }
};
</script>
<style>
.orderinfo{ width:94%;margin:20rpx 3%;border-radius:5px;padding:20rpx 20rpx;padding: 14rpx 3%;background: #FFF;}
.orderinfo .item{display:flex;width:100%;padding:20rpx 0;border-bottom:1px dashed #ededed;}
.orderinfo .item:last-child{ border-bottom: 0;}
.orderinfo .item .t1{width:200rpx;}
.orderinfo .item .t2{flex:1;text-align:right}
.orderinfo .item .red{color:red}
</style>
<template>
	<view class="container">
		<view class="x-between">
			<view>{{lang('Warehouse Address')}}</view>
			<view>曼谷</view>
		</view>
		<view style="margin-top: 40rpx;margin-bottom: 20rpx;">{{lang('weights')}}({{lang('unit')}}KG)</view>
		<view class="block xy-center">
			<input v-model="weight" :placeholder="lang('Input Weight')" style="font-size: 28rpx;text-align: center;" />
		</view>
		<view style="margin-top: 40rpx;margin-bottom: 20rpx;">{{lang('Package Size')}}{{lang('optional')}}</view>
		<view class="y-center">
			<view class="block xy-center" style="width: 216rpx;margin-right: 16rpx;">
				<input v-model="elder" :placeholder="lang('elder') + '：CM'" style="font-size: 28rpx;text-align: center;" />
			</view>
			<view class="block xy-center" style="width: 216rpx;margin-right: 16rpx;">
				<input v-model="broad" :placeholder="lang('broad') + '：CM'" style="font-size: 28rpx;text-align: center;" />
			</view>
			<view class="block xy-center" style="width: 216rpx;">
				<input v-model="loud" :placeholder="lang('loud') + '：CM'" style="font-size: 28rpx;text-align: center;" />
			</view>
		</view>
		<view style="margin-top: 40rpx;margin-bottom: 20rpx;">{{lang('Item Properties')}}</view>
		<view class="flex-wrap">
			<view class="block xy-center" :class="{'active': index === propertieIndex}" @tap="propertieIndex = index" style="width: auto;margin-right: 16rpx;margin-bottom: 16rpx; padding: 0 40rpx;" v-for="(item,index) in properties" :key="index">
				{{item}}
			</view>
		</view>
		<view style="margin-top: 40rpx;margin-bottom: 20rpx;">{{lang('Type of transportation')}}</view>
		<view class="flex-wrap">
			<view class="block xy-center" :class="{'active': index === transportationIndex}" @tap="transportationIndex = index" style="width: auto;margin-right: 16rpx;margin-bottom: 16rpx; padding: 0 40rpx;" v-for="(item,index) in transportation" :key="index">
				{{item}}
			</view>
		</view>
		<view class="button xy-center">{{lang('Enquire Now')}}</view>
	</view>
</template>

<script>
	var app = getApp();
	export default {
		data() {
			return {
				weight: '',
				elder: '',
				broad: '',
				loud: '',
				properties: ['普货', '特货', '特货YAN/条', '特货YAN/条', '特价JIU/KG'],
				propertieIndex: 0,
				transportation: ['海运公斤', '陆运公斤', '海运立方', '陆运立方'],
				transportationIndex: 0
			}
		},
		onLoad() {
			uni.setNavigationBarTitle({
				title: this.lang('Freight estimates')
			})
		},
		methods: {
			lang: function(k) {
				return app.lang(k);
			},
		}
	}
</script>

<style>
	uni-page-body,
	page {
		height: 100%;
		background-color: #ffffff;
	}
</style>

<style lang="scss" scoped>
	.container {
		padding: 30rpx;
		color: #3D3D3D;
		font-size: 28rpx;
	}
	.block {
		width: 274rpx;
		height: 92rpx;
		background: #EDEDED;
		
		box-sizing: border-box;
		border: 2rpx solid #D1D1D1;
		
		&.active {
			background: #4187FF;
			border: 2rpx solid #4187FF;
			color: #fff;
		}
	}
	
	.button {
		width: 656rpx;
		height: 96rpx;
		border-radius: 8rpx;
		background: #4187FF;
		color: #fff;
		margin-top: 40rpx;
	}
	
	.flex {
		display: flex;
	}
	
	.flex1 {
		flex: 1;
	}
	
	.flex-wrap {
		display: flex;
		flex-wrap: wrap;
	}
	
	.flex-column {
		display: flex;
		flex-direction: column;
	}
	
	.x-center {
		display: flex;
		justify-content: center;
	}
	
	.x-between {
		display: flex;
		justify-content: space-between;
	}
	
	.y-center {
		display: flex;
		align-items: center;
	}
	
	.xy-center {
		display: flex;
		justify-content: center;
		align-items: center;
	}
	
	.line1 {
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
	}
</style>
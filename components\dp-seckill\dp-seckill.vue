<template>
<view class="dp-seckill" :style="{
	backgroundColor:params.bgcolor,
	margin:(params.margin_y*2.2)+'rpx '+params.margin_x*2.2+'rpx 0',
	padding:(params.padding_y*2.2)+'rpx '+params.padding_x*2.2+'rpx'
}">
	<view class="dp-seckill-item" v-if="params.style=='1' || params.style=='2' || params.style=='3'">
		<view class="item" v-for="(item,index) in data" :style="params.style==2 ? 'width:49%;margin-right:'+(index%2==0?'2%':0) : (params.style==3 ? 'width:32%;margin-right:'+(index%3!=2?'2%':0) :'width:100%')" :key="item.id" @click="goto" :data-url="'/activity/seckill/product?id='+item.proid">
			<view class="product-pic">
				<image class="image" :src="item.pic" mode="widthFix"/>
				<image class="saleimg" :src="params.saleimg" v-if="params.saleimg!=''" mode="widthFix"/>
			</view>
			<view class="product-info">
				<view class="p1" v-if="params.showname == 1">{{item.name}}</view>
				<view class="p2">
					<view class="p2-1" v-if="params.showprice != '0'">
						<text class="t1" :style="{color:t('color1')}"><text style="font-size:24rpx">$</text>{{item.sell_price}}</text>
						<text class="t2" v-if="params.showprice == '1'">{{item.market_price}}</text>
					</view>
				</view>
				<view v-if="params.showtime == 1 && params.style!='3'" style="color:#333;font-size:24rpx">
					<view v-if="item.seckill_status == 2">{{lang('activity has ended')}}</view>
					<view v-if="item.seckill_status == 1" class="flex-row"><view class="h24">{{lang('also the rest of the')}}</view><view class="flex1"></view><uni-countdown :show-day="false" color="#FFFFFF" background-color="#fd4a46" :hour="item.hour" :minute="item.minute" :second="item.second" splitorColor="#333"></uni-countdown></view>
					<view v-if="item.seckill_status == 0" class="flex-row"><view class="h24">{{lang('from open to rob')}}</view><view class="flex1"></view><uni-countdown :show-day="false" color="#FFFFFF" background-color="#fd4a46" :hour="item.hour" :minute="item.minute" :second="item.second" splitorColor="#333"></uni-countdown></view>
				</view>
				
				<view class="p3">
					<view class="p3-1" :style="{background:'rgba('+t('color1rgb')+',0.12)',color:t('color1')}">{{lang('seconds kill')}}</view>
					<view class="p3-2" v-if="params.showsales=='1' && item.sales>0"><text style="overflow:hidden">{{lang('have to snap up')}}{{item.sales}}</text></view>
				</view>
			</view>
		</view>
	</view>
	<view class="dp-seckill-itemlist" v-if="params.style=='list'">
		<view class="item" v-for="(item,index) in data" :key="item.id" @click="goto" :data-url="'/activity/seckill/product?id='+item.proid">
			<view class="product-pic">
				<image class="image" :src="item.pic" mode="widthFix"/>
				<image class="saleimg" :src="params.saleimg" v-if="params.saleimg!=''" mode="widthFix"/>
			</view>
			<view class="product-info">
				<view class="p1" v-if="params.showname == 1">{{item.name}}</view>
				<view class="p2" v-if="params.showprice != '0'">
					<text class="t1" :style="{color:t('color1')}"><text style="font-size:24rpx">$</text>{{item.sell_price}}</text>
					<text class="t2" v-if="params.showprice == '1'">{{item.market_price}}</text>
				</view>
				<view v-if="params.showtime == 1" style="color:#333;font-size:24rpx">
					<view v-if="item.seckill_status == 2">{{lang('activity has ended')}}</view>
					<view v-if="item.seckill_status == 1" class="flex-row"><view class="h24">{{lang('is apart from the end of the activity')}}</view><view class="flex1"></view>
						<uni-countdown v-if="item.day > 0" :show-day="true" color="#FFFFFF" background-color="#fd4a46" :day="item.day" :hour="item.day_hour" :minute="item.minute" :second="item.second" splitorColor="#333"></uni-countdown>
						<uni-countdown v-else :show-day="false" color="#FFFFFF" background-color="#fd4a46" :day="item.day" :hour="item.hour" :minute="item.minute" :second="item.second"></uni-countdown>
					</view>
					<view v-if="item.seckill_status == 0" class="flex-row"><view class="h24">{{lang('from the activity start')}}</view><view class="flex1"></view>
						<uni-countdown v-if="item.day > 0" :show-day="true" color="#FFFFFF" background-color="#fd4a46" :day="item.day" :hour="item.day_hour" :minute="item.minute" :second="item.second" splitorColor="#333"></uni-countdown>
						<uni-countdown v-else :show-day="false" color="#FFFFFF" background-color="#fd4a46" :day="item.day" :hour="item.hour" :minute="item.minute" :second="item.second"></uni-countdown>
					</view>
				</view>
				<view class="p3">
					<view class="p3-1" :style="{background:'rgba('+t('color1rgb')+',0.12)',color:t('color1')}">{{lang('seconds kill')}}</view>
					<view class="p3-2" v-if="params.showsales=='1' && item.sales>0"><text style="overflow:hidden">{{lang('have to snap up":')}}{{item.sales}}</text></view>
				</view>
			</view>
		</view>
	</view>
	<view class="dp-seckill-itemline" v-if="params.style=='line'">
		<view class="item" v-for="(item,index) in data" :key="item.id" @click="goto" :data-url="'/activity/seckill/product?id='+item.proid">
			<view class="product-pic">
				<image class="image" :src="item.pic" mode="widthFix"/>
				<image class="saleimg" :src="params.saleimg" v-if="params.saleimg!=''" mode="widthFix"/>
			</view>
			<view class="product-info">
				<view class="p1" v-if="params.showname == 1">{{item.name}}</view>
				<view class="p2">
					<view class="p2-1" v-if="params.showprice != '0'">
						<text class="t1" :style="{color:t('color1')}"><text style="font-size:24rpx">$</text>{{item.sell_price}}</text>
						<text class="t2" v-if="params.showprice == '1'">{{item.market_price}}</text>
					</view>
				</view>
				<view class="p3">
					<view class="p3-1" :style="{background:'rgba('+t('color1rgb')+',0.12)',color:t('color1')}">{{lang('seconds kill')}}</view>
					<view class="p3-2" v-if="params.showsales=='1' && item.sales>0"><text style="overflow:hidden">{{lang('have to snap up":')}}{{item.sales}}</text></view>
				</view>
			</view>
		</view>
	</view>
</view>
</template>
<script>
		var app =getApp();
	export default {
		props: {
			params:{},
			data:{}
		},
		methods:{
			lang: function(k) {
				return app.lang(k);
			},
		}
	}
</script>
<style>
.dp-seckill{height: auto; position: relative;overflow: hidden; padding: 0px; background: #fff;}
.dp-seckill-item{height: auto; position: relative;overflow: hidden; padding: 0px; display:flex;flex-wrap:wrap}
.dp-seckill-item .item{display: inline-block;position: relative;margin-bottom: 12rpx;background: #fff;border-radius:10rpx;overflow:hidden}
.dp-seckill-item .product-pic {width: 100%;height:0;overflow:hidden;background: #ffffff;padding-bottom: 100%;position: relative;}
.dp-seckill-item .product-pic .image{position:absolute;top:0;left:0;width: 100%;height:auto}
.dp-seckill-item .product-pic .saleimg{ position: absolute;width: 60px;height: auto; top: -3px; left:-3px;}
.dp-seckill-item .product-info {padding:20rpx 10rpx;position: relative;}
.dp-seckill-item .product-info .p1 {color:#323232;font-weight:bold;font-size:26rpx;line-height:36rpx;margin-bottom:10rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;height:72rpx}
.dp-seckill-item .product-info .p2{display:flex;align-items:center;overflow:hidden;padding:2px 0}
.dp-seckill-item .product-info .p2-1{flex-grow:1;flex-shrink:1;height:40rpx;line-height:40rpx;overflow:hidden;white-space: nowrap}
.dp-seckill-item .product-info .p2-1 .t1{font-size:36rpx;}
.dp-seckill-item .product-info .p2-1 .t2 {margin-left:10rpx;font-size:24rpx;color: #aaa;text-decoration: line-through;/*letter-spacing:-1px*/}
.dp-seckill-item .product-info .p2-2{font-size:20rpx;height:40rpx;line-height:40rpx;text-align:right;padding-left:20rpx;color:#999}
.dp-seckill-item .product-info .p3{display:flex;align-items:center;overflow:hidden;margin-top:10rpx;justify-content:space-between}
.dp-seckill-item .product-info .p3-1{height:40rpx;line-height:40rpx;border:0 #FF3143 solid;border-radius:10rpx;color:#FF3143;padding:0 24rpx;font-size:24rpx}
.dp-seckill-item .product-info .p3-2{color:#999999;font-size:20rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;}
.dp-seckill-item .product-info .h24 {height: 48rpx; line-height: 48rpx;padding: 2rpx 0; margin: 4rpx 0;}

.dp-seckill-itemlist{height: auto; position: relative;overflow: hidden; padding: 0px; display:flex;flex-wrap:wrap}
.dp-seckill-itemlist .item{width:100%;display: inline-block;position: relative;margin-bottom: 12rpx;background: #fff;display:flex;padding:20rpx;border-radius:10rpx}
.dp-seckill-itemlist .product-pic {width: 30%;height:0;overflow:hidden;background: #ffffff;padding-bottom: 30%;position: relative;border-radius:4px;}
.dp-seckill-itemlist .product-pic .image{width: 100%;height:auto}
.dp-seckill-itemlist .product-pic .saleimg{ position: absolute;width: 120rpx;height: auto; top: -6rpx; left:-6rpx;}
.dp-seckill-itemlist .product-info {width: 70%;padding:6rpx 0rpx 5rpx 20rpx;position: relative;}
.dp-seckill-itemlist .product-info .p1 {color:#323232;font-weight:bold;font-size:26rpx;line-height:36rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;height:72rpx}
.dp-seckill-itemlist .product-info .p2 {height:56rpx;line-height:56rpx;overflow:hidden;}
.dp-seckill-itemlist .product-info .p2 .t1{font-size:36rpx;}
.dp-seckill-itemlist .product-info .p2 .t2 {margin-left:10rpx;font-size:24rpx;color: #aaa;text-decoration: line-through;/*letter-spacing:-1px*/}
.dp-seckill-itemlist .product-info .p3{display:flex;align-items:center;overflow:hidden;margin-top:10rpx;justify-content:space-between}
.dp-seckill-itemlist .product-info .p3-1{height:40rpx;line-height:40rpx;border:0 #FF3143 solid;border-radius:10rpx;color:#FF3143;padding:0 24rpx;font-size:24rpx}
.dp-seckill-itemlist .product-info .p3-2{color:#999999;font-size:20rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;}
.dp-seckill-itemlist .product-info .h24 {height: 48rpx; line-height: 48rpx;padding: 2rpx 0; margin: 4rpx 0;}

.dp-seckill-itemline{width:100%;display:flex;overflow-x:scroll;overflow-y:hidden}
.dp-seckill-itemline .item{width: 220rpx;display: inline-block;position: relative;margin-bottom: 12rpx;background: #fff;border-radius:10rpx;margin-right:4px}
.dp-seckill-itemline .product-pic {width:220rpx;height:0;overflow:hidden;background: #ffffff;padding-bottom: 100%;position: relative;}
.dp-seckill-itemline .product-pic .image{position:absolute;top:0;left:0;width: 100%;height:auto}
.dp-seckill-itemline .product-pic .saleimg{ position: absolute;width: 60px;height: auto; top: -3px; left:-3px;}
.dp-seckill-itemline .product-info {padding:20rpx 20rpx;position: relative;}
.dp-seckill-itemline .product-info .p1 {color:#323232;font-weight:bold;font-size:26rpx;line-height:36rpx;margin-bottom:10rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:2;overflow:hidden;height:72rpx}
.dp-seckill-itemline .product-info .p2{display:flex;align-items:center;overflow:hidden;padding:2px 0}
.dp-seckill-itemline .product-info .p2-1{flex-grow:1;flex-shrink:1;height:40rpx;line-height:40rpx;overflow:hidden;white-space: nowrap}
.dp-seckill-itemline .product-info .p2-1 .t1{font-size:36rpx;}
.dp-seckill-itemline .product-info .p2-1 .t2 {margin-left:10rpx;font-size:24rpx;color: #aaa;text-decoration: line-through;/*letter-spacing:-1px*/}
.dp-seckill-itemline .product-info .p2-2{font-size:20rpx;height:40rpx;line-height:40rpx;text-align:right;padding-left:20rpx;color:#999}
.dp-seckill-itemline .product-info .p3{display:flex;align-items:center;overflow:hidden;margin-top:10rpx;justify-content:space-between}
.dp-seckill-itemline .product-info .p3-1{height:40rpx;line-height:40rpx;border:0 #FF3143 solid;border-radius:10rpx;color:#FF3143;padding:0 24rpx;font-size:24rpx}
.dp-seckill-itemline .product-info .p3-2{color:#999999;font-size:20rpx;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1;}
</style>
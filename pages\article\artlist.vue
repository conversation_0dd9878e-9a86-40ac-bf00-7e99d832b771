<template>
	<view class="container">
		<block v-if="isload">
			<view class="topsearch flex-y-center">
				<view class="f1 flex-y-center">
					<image class="img" src="/static/img/search_ico.png"></image>
					<input :value="keyword" :placeholder="lang('search for articles of interest')"
						placeholder-style="font-size:24rpx;color:#C2C2C2" @confirm="searchConfirm"></input>
				</view>
			</view>
			<dd-tab :itemdata="cnamelist" :itemst="cidlist" :st="cid" :isfixed="false" @changetab="changetab"
				v-if="clist.length>0"></dd-tab>


			<view class="article_list">
				<!--{{lang('ttest')}}-->
				<view v-if="listtype=='0'" class="article-itemlist" v-for="(item,index) in datalist" :key="item.id"
					@click="goto" :data-url="'/pages/article/detail?id='+item.id">
					<view class="article-pic">
						<image class="image" :src="item.pic" mode="widthFix" />
					</view>
					<view class="article-info">
						<view class="p1">{{item.name}}</view>
						<view class="p2">
							<text style="overflow:hidden" class="flex1">{{item.createtime}}</text>
							<text style="overflow:hidden">{{lang('read')}} {{item.readcount}}</text>
						</view>
					</view>
				</view>
				<!--{{lang('ttest')}}-->
				<view v-if="listtype=='1'" class="article-item2" v-for="(item,index) in datalist" :key="item.id"
					:style="{marginRight:index%2==0?'2%':'0'}" @click="goto"
					:data-url="'/pages/article/detail?id='+item.id">
					<view class="article-pic">
						<image class="image" :src="item.pic" mode="widthFix" />
					</view>
					<view class="article-info">
						<view class="p1">{{item.name}}</view>
						<view class="p2">
							<text style="overflow:hidden" class="flex1">{{item.createtime}}</text>
							<text style="overflow:hidden">{{lang('read')}} {{item.readcount}}</text>
						</view>
					</view>
				</view>
				<waterfall-article v-if="listtype=='2'" :list="datalist" ref="waterfall"></waterfall-article>
				<!--{{lang('ttest')}}-->
				<view v-if="listtype=='3'" class="article-item1" v-for="(item,index) in datalist" :key="item.id"
					@click="goto" :data-url="'/pages/article/detail?id='+item.id">
					<view class="article-pic">
						<image class="image" :src="item.pic" mode="widthFix" />
					</view>
					<view class="article-info">
						<view class="p1">{{item.name}}</view>
						<view class="p2">
							<text style="overflow:hidden" class="flex1">{{item.createtime}}</text>
							<text style="overflow:hidden">{{lang('read')}} {{item.readcount}}</text>
						</view>
					</view>
				</view>
			</view>
		</block>
		<nodata v-if="nodata"></nodata>
		<nomore v-if="nomore"></nomore>
		<loading v-if="loading"></loading>
		<dp-tabbar :opt="opt"></dp-tabbar>
		<popmsg ref="popmsg"></popmsg>
	</view>
</template>

<script>
	var app = getApp();
	export default {
		data() {
			return {
				opt: {},
				loading: false,
				isload: false,
				menuindex: -1,
				nodata: false,
				nomore: false,
				keyword: '',
				datalist: [],
				pagenum: 1,
				clist: [],
				cnamelist: [],
				cidlist: [],
				datalist: [],
				cid: 0,
				bid: 0,
				listtype: 0,
			};
		},
		onLoad: function(opt) {
			this.opt = app.getopts(opt);
			this.cid = this.opt.cid || 0;
			this.bid = this.opt.bid || 0;
			this.getdata();
		},
		onPullDownRefresh: function() {
			this.getdata();
		},
		onReachBottom: function() {
			if (!this.nomore && !this.nodata) {
				this.pagenum = this.pagenum + 1;
				this.getdata(true);
			}
		},
		methods: {
			lang: function(k) {
				return app.lang(k);
			},
			getdata: function(loadmore) {
				if (!loadmore) {
					this.pagenum = 1;
					this.datalist = [];
				}
				var that = this;
				var pagenum = that.pagenum;
				var keyword = that.keyword;
				var cid = that.cid;
				console.log(cid)
				that.loading = true;
				that.nodata = false;
				that.nomore = false;
				app.post('ApiArticle/getartlist', {
					bid: that.bid,
					cid: cid,
					pagenum: pagenum,
					keyword: keyword
				}, function(res) {
					that.loading = false;
					var data = res.data;
					if (pagenum == 1) {
						that.listtype = res.listtype || 0;
						that.clist = res.clist
						if ((res.clist).length > 0) {
							var cnamelist = [];
							var cidlist = [];
							cnamelist.push('全部');
							cidlist.push('0');
							for (var i in that.clist) {
								cnamelist.push(that.clist[i].name);
								cidlist.push(that.clist[i].id);
							}
							that.cnamelist = cnamelist;
							that.cidlist = cidlist;
						}

						uni.setNavigationBarTitle({
							title: res.title
						});
						that.datalist = data;
						if (data.length == 0) {
							that.nodata = true;
						}
						that.loaded();
					} else {
						if (data.length == 0) {
							that.nomore = true;
						} else {
							var datalist = that.datalist;
							var newdata = datalist.concat(data);
							that.datalist = newdata;
						}
					}
				});
			},
			searchConfirm: function(e) {
				var that = this;
				var keyword = e.detail.value;
				that.keyword = keyword
				that.getdata();
			},
			changetab: function(cid) {
				this.cid = cid;
				uni.pageScrollTo({
					scrollTop: 0,
					duration: 0
				});
				if (this.listtype == 2) {
					this.$refs.waterfall.refresh();
				}
				this.getdata();
			},
		}
	};
</script>
<style>
	page {
		background: #f6f6f7
	}

	.topsearch {
		width: 100%;
		padding: 20rpx 20rpx;
		background: #fff
	}

	.topsearch .f1 {
		height: 70rpx;
		border-radius: 35rpx;
		border: 0;
		background-color: #f5f5f5;
		flex: 1;
		overflow: hidden
	}

	.topsearch .f1 image {
		width: 30rpx;
		height: 30rpx;
		margin-left: 10px
	}

	.topsearch .f1 input {
		height: 100%;
		flex: 1;
		padding: 0 20rpx;
		font-size: 28rpx;
		color: #333;
		background-color: #f5f5f5;
	}

	.article_list {
		padding: 10rpx 16rpx;
		background: #f6f6f7;
		margin-top: 6rpx;
	}

	.article_list .article-item1 {
		width: 100%;
		display: inline-block;
		position: relative;
		margin-bottom: 16rpx;
		background: #fff;
		border-radius: 12rpx;
		overflow: hidden
	}

	.article_list .article-item1 .article-pic {
		width: 100%;
		height: auto;
		overflow: hidden;
		background: #ffffff;
	}

	.article_list .article-item1 .article-pic .image {
		width: 100%;
		height: auto
	}

	.article_list .article-item1 .article-info {
		padding: 10rpx 20rpx 20rpx 20rpx;
	}

	.article_list .article-item1 .article-info .p1 {
		color: #222222;
		font-weight: bold;
		font-size: 28rpx;
		line-height: 46rpx;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 2;
		overflow: hidden;
	}

	.article_list .article-item1 .article-info .t1 {
		word-break: break-all;
		text-overflow: ellipsis;
		overflow: hidden;
		display: block;
		font-size: 32rpx;
	}

	.article_list .article-item1 .article-info .t2 {
		word-break: break-all;
		text-overflow: ellipsis;
		padding-top: 4rpx;
		overflow: hidden;
	}

	.article_list .article-item1 .article-info .p2 {
		flex-grow: 0;
		flex-shrink: 0;
		display: flex;
		padding: 10rpx 0;
		font-size: 24rpx;
		color: #a88;
		overflow: hidden
	}

	.article_list .article-item2 {
		width: 49%;
		display: inline-block;
		position: relative;
		margin-bottom: 12rpx;
		background: #fff;
		border-radius: 8rpx;
	}

	/*.article-item2:nth-child(even){margin-right:2%}*/
	.article_list .article-item2 .article-pic {
		width: 100%;
		height: 0;
		overflow: hidden;
		background: #ffffff;
		padding-bottom: 70%;
		position: relative;
		border-radius: 8rpx 8rpx 0 0;
	}

	.article_list .article-item2 .article-pic .image {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: auto
	}

	.article_list .article-item2 .article-info {
		padding: 10rpx 20rpx 20rpx 20rpx;
		display: flex;
		flex-direction: column;
	}

	.article_list .article-item2 .article-info .p1 {
		color: #222222;
		font-weight: bold;
		font-size: 28rpx;
		line-height: 46rpx;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 2;
		overflow: hidden;
	}

	.article_list .article-item2 .article-info .p2 {
		flex-grow: 0;
		flex-shrink: 0;
		display: flex;
		align-items: center;
		padding-top: 10rpx;
		font-size: 24rpx;
		color: #a88;
		overflow: hidden
	}

	.article_list .article-itemlist {
		width: 100%;
		display: inline-block;
		position: relative;
		margin-bottom: 12rpx;
		padding: 12rpx;
		background: #fff;
		display: flex;
		border-radius: 8rpx;
	}

	.article_list .article-itemlist .article-pic {
		width: 35%;
		height: 0;
		overflow: hidden;
		background: #ffffff;
		padding-bottom: 25%;
		position: relative;
	}

	.article_list .article-itemlist .article-pic .image {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: auto
	}

	.article_list .article-itemlist .article-info {
		width: 65%;
		height: 172rpx;
		overflow: hidden;
		padding: 0 20rpx;
		display: flex;
		flex-direction: column;
		justify-content: space-between
	}

	.article_list .article-itemlist .article-info .p1 {
		color: #222222;
		font-weight: bold;
		font-size: 28rpx;
		line-height: 46rpx;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 2;
		overflow: hidden;
		height: 92rpx
	}

	.article_list .article-itemlist .article-info .p2 {
		display: flex;
		flex-grow: 0;
		flex-shrink: 0;
		font-size: 24rpx;
		color: #a88;
		overflow: hidden;
		padding-bottom: 6rpx
	}
</style>
<template>
  <view class="container">
    <block v-if="isload">
      <form @submit="formSubmit">

        <view class="content2">
          <!-- <view class="item2"><view class="f1">接收人手机号</view></view>
			<view class="item3"><view class="f2"><input class="input" type="number" name="mobile" value="" placeholder="请输入接收人手机号" placeholder-style="color:#999;font-size:36rpx" @input="mobileinput"></input></view></view>
			<view class="item4" style="height: 1rpx;">
			</view> -->
          <view class="item2">
            <view class="f1">{{lang('recipient')}}ID</view>
          </view>
          <view class="item3">
            <view class="f2"><input class="input" type="number" name="mid" value="" :placeholder="lang('please enter the recipient')+'ID'"
                placeholder-style="color:#999;font-size:36rpx"></input></view>
          </view>
          <view class="item4" style="height: 1rpx;">
          </view>
          <view class="item2">
            <view class="f1">{{lang('examples of the number')}}</view>
          </view>
          <view class="item3">
            <view class="f2"><input class="input" type="number" name="integral" value="" :placeholder="lang('please enter the number of clothes')"
                placeholder-style="color:#999;font-size:36rpx" @input="moneyinput"></input></view>
          </view>
          <view class="item4">
            <text style="margin-right:10rpx">{{lang('your current')}}{{t('积分')}}：{{myscore}}，{{lang('after the groom will not return')}} </text>
          </view>
        </view>
        <button class="btn" :style="{background:t('color1')}" form-type="submit">{{lang('examples of')}}</button>
        <view class='text-center' @tap="goto" data-url='/myshop/my/usercenter'><text>{{lang('return')}}{{t('会员')}}{{lang('center')}}</text></view>
      </form>
    </block>
    <loading v-if="loading"></loading>
    <dp-tabbar :opt="opt"></dp-tabbar>
  </view>
</template>

<script>
var app = getApp();

export default {
  data () {
    return {
      opt: {},
      loading: false,
      isload: false,
      menuindex: -1,

      userinfo: [],
      myscore: 0,
    };
  },

  onLoad: function (opt) {
    this.opt = app.getopts(opt);

    var that = this;
    // app.checkLogin();
    uni.setNavigationBarTitle({
      title: that.t('积分') + that.lang('examples of')
    });
    this.getdata();
	uni.setNavigationBarTitle({
		title:app.lang("SGMAll"),
		success() {
			console.log(app.lang("SGMAll"))
		}
	})
  },
  onPullDownRefresh: function () {
    this.getdata();
  },
  methods: {
	lang: function(k) {
		return app.lang(k);
	},   
    getdata: function () {
      var that = this;
      app.get('ApiMy/scoreTransfer', {}, function (res) {
        if (res.status == 0) {
          app.alert(res.msg); return;
        }
        if (res.status == 1) {
          that.myscore = res.myscore;
        }
        that.loaded();
      });
    },

    mobileinput: function (e) {
      var value = parseFloat(e.detail.value);
    },

    moneyinput: function (e) {
      var money = parseFloat(e.detail.value);

    },
    changeradio: function (e) {
      var that = this;
      var paytype = e.currentTarget.dataset.paytype;
      that.paytype = paytype;
    },
    formSubmit: function (e) {
      var that = this;
      var money = parseFloat(e.detail.value.integral);
      var mid = parseInt(e.detail.value.mid);
      var mobile = e.detail.value.mobile;
      if (typeof (mobile) != 'undefined' && !/^1[3456789]\d{9}$/.test(mobile)) {
        app.error(that.lang('mobile phone number is wrong'));
        return false;
      }
      if (typeof (mid) != 'undefined' && (mid == '' || isNaN(mid))) {
        app.error(that.lang('please enter the recipient')+"ID");
        return false;
      }
      if (typeof (mid) != 'undefined' && mid == app.globalData.mid) {
        app.error(that.lang('cannot donates his'));
        return false;
      }
      if (isNaN(money) || money <= 0) {
        app.error(that.lang('the number must be greater than')+'0');
        return;
      }

      if (money < 0) {
        app.error(that.lang('the number must be greater than')+'0'); return;
      } else if (money > that.myscore) {
        app.error(this.t('积分') + that.lang('insufficient')); return;
      }

      app.confirm(that.lang('confirm')+'？', function () {
        app.showLoading();
        app.post('ApiMy/scoreTransfer', { integral: money, mobile: mobile, mid: mid }, function (data) {
          app.showLoading(false);
          if (data.status == 0) {
            app.error(data.msg);
            return;
          } else {
            app.success(data.msg);
            that.subscribeMessage(function () {
              setTimeout(function () {
                app.goto('/myshop/my/usercenter');
              }, 1000);
            });
          }
        }, that.lang('please wait'));
      })
    }
  }
};
</script>
<style>
.container {
  display: flex;
  flex-direction: column;
}
.content2 {
  width: 94%;
  margin: 10rpx 3%;
  border-radius: 10rpx;
  display: flex;
  flex-direction: column;
  background: #fff;
}
.content2 .item1 {
  display: flex;
  width: 100%;
  border-bottom: 1px solid #f0f0f0;
  padding: 0 30rpx;
}
.content2 .item1 .f1 {
  flex: 1;
  font-size: 32rpx;
  color: #333333;
  font-weight: bold;
  height: 120rpx;
  line-height: 120rpx;
}
.content2 .item1 .f2 {
  color: #fc4343;
  font-size: 44rpx;
  font-weight: bold;
  height: 120rpx;
  line-height: 120rpx;
}

.content2 .item2 {
  display: flex;
  width: 100%;
  padding: 0 30rpx;
  padding-top: 10rpx;
}
.content2 .item2 .f1 {
  height: 80rpx;
  line-height: 80rpx;
  color: #999999;
  font-size: 28rpx;
}

.content2 .item3 {
  display: flex;
  width: 100%;
  padding: 0 30rpx;
  padding-bottom: 20rpx;
}
.content2 .item3 .f1 {
  height: 100rpx;
  line-height: 100rpx;
  font-size: 60rpx;
  color: #333333;
  font-weight: bold;
  margin-right: 20rpx;
}
.content2 .item3 .f2 {
  display: flex;
  align-items: center;
  font-size: 36rpx;
  color: #333333;
  font-weight: bold;
}
.content2 .item3 .f2 .input {
  font-size: 36rpx;
  height: 100rpx;
  line-height: 100rpx;
}
.content2 .item4 {
  display: flex;
  width: 94%;
  margin: 0 3%;
  border-top: 1px solid #f0f0f0;
  height: 100rpx;
  line-height: 100rpx;
  color: #8c8c8c;
  font-size: 28rpx;
}

.text-center {
  text-align: center;
  margin-top: 20rpx;
}

.btn {
  height: 100rpx;
  line-height: 100rpx;
  width: 90%;
  margin: 0 auto;
  border-radius: 50rpx;
  margin-top: 30rpx;
  color: #fff;
  font-size: 30rpx;
  font-weight: bold;
}
</style>
<template>
<view class="container">
	<block v-if="isload">
		<dd-tab :itemdata="['全部','待处理','处理中 ','已完成','待支付']" :itemst="['all','0','1','2','10']" :st="st" :isfixed="true" @changetab="changetab"></dd-tab>
		<view style="width:100%;height:90rpx"></view>
		<view class="content" id="datalist">
			<view class="item" v-for="(item, index) in datalist" :key="index">
				<view class="f1"  @tap.stop="goto" :data-url="'formdetail?id=' + item.id">
						<view class="flex" style="justify-content: space-between;">
							<text class="t1" >{{item.title}}</text>
								<view class="f2" >
									<text class="t1" v-if="item.status==0 && (!item.payorderid||item.paystatus==1)" style="color:#88e" @tap="setst" :data-id="item.id">立即处理</text>
									<text class="t1" v-if="item.status==0 && item.payorderid && item.paystatus==0" style="color:red">待支付</text>
									<text class="t1" v-if="item.status==1" style="color:green" @tap="goto"  :data-url="'formdetail?id=' + item.id" >立即处理</text>
									<text class="t1" v-if="item.status==2" style="color:green">已完成</text>
									<text class="t1" v-if="item.status==-1" style="color:red">已驳回</text>
								</view>
						</view>	
						<view class="flex" style="justify-content: space-between; margin-top: 20rpx;">
							<view  @tap.stop="goto" :data-url="'formdetail?id=' + item.id">
								<text class="t2">提交时间：{{item.createtime}}</text>
								<text class="t2" v-if="item.paynum" user-select="true" selectable="true">{{item.paynum}}</text>
							</view>
							<view class="jindu" @tap.stop="jindu" :data-id="item.id" >查看进度</view>
						</view>
			
			
			
				</view>
	
			</view>
		</view>
		<view class="modal" v-if="ishowjindu">
			<view class="modal_jindu">
					<view class="close" @tap="closejd"><image src="../../static/img/close.png" /></view>
					<block v-if="jdlist.length>0">
			
						<view class="item " v-for="(item,index) in jdlist" :key="index" style="display: flex;">
							<view class="f1"><image :src="'/static/img/jindu' + (index==0?'2':'1') + '.png'"></image></view>
							<view class="f2">
								<text class="t2"> 时间：{{item.time}}</text>
								<text class="t1">{{item.desc}}({{item.remark}}) </text>
								<view class="t3" v-if="item.hfremark" >用户回复：{{item.hfremark}} </view>
								<view class="t4" v-if="item.hftime" >回复时间：{{item.hftime}} </view>
							</view>
						</view>
					</block>
					<block v-else>
							<view style="font-size:14px;color:#f05555;padding:10px;">等待处理</view>
					</block>
			</view>
		</view>
		
		<view class="modal" v-if="showstatus">
			<view class="modal_jindu">
				<form   @submit="formsubmit">
						<view class="close" @tap="close"><image src="../../static/img/close.png" /></view>
						<view class="title">选择流程</view>
						<view class="uni-list">
								<radio-group name="liucheng">
										<label class="uni-list-cell uni-list-cell-pd" v-for="(item, index) in lclist" :key="index">
												<view>
														<radio :value="''+item.id" style="transform:scale(0.7)"/>
												</view>
												<view>{{item.name}}</view>
										</label>
								</radio-group>
								<view class="beizhu flex">
										<label>备注:</label><textarea placeholder="输入内容" name="content" maxlength="-1"></textarea>
								</view>
						</view>
						<button class="btn" form-type="submit">提交</button>
				</form>
			</view>
		</view>
	</block>
	<nodata v-if="nodata"></nodata>
	<nomore v-if="nomore"></nomore>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
			opt:{},
			loading:false,
      isload: false,
			menuindex:-1,

      st: 'all',
      datalist: [],
      pagenum: 1,
      nomore: false,
			showstatus:0,
			lclist:[],
			current:0,
			ishowjindu:false,
			jdlist:[]
    };
  },
  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.st = this.opt.st || 'all';
		this.getdata();
		this.getliucheng();
  },
	onPullDownRefresh: function () {
		this.getdata();
	},
  onReachBottom: function () {
    if (!this.nodata && !this.nomore) {
      this.pagenum = this.pagenum + 1;
      this.getdata(true);
    }
  },
  methods: {
    changetab: function (st) {
      this.st = st;
      uni.pageScrollTo({
        scrollTop: 0,
        duration: 0
      });
      this.getdata();
    },
    getdata: function (loadmore) {
			if(!loadmore){
				this.pagenum = 1;
				this.datalist = [];
			}
      var that = this;
      var pagenum = that.pagenum;
      var st = that.st;
			this.nodata = false;
			this.nomore = false;
			this.loading = true;
      app.post('ApiAdminWorkorder/formlog', {st: st,pagenum: pagenum}, function (res) {
				that.loading = false;
        var data = res.data;
        if (pagenum == 1) {
					that.datalist = data;
          if (data.length == 0) {
            that.nodata = true;
          }
					that.loaded();
        }else{
          if (data.length == 0) {
            that.nomore = true;
          } else {
            var datalist = that.datalist;
            var newdata = datalist.concat(data);
            that.datalist = newdata;
          }
        }
      });
    },
		getliucheng:function(e){
			var that=this
			app.post('ApiAdminWorkorder/getliucheng', {}, function (res) {
					var lclist = res.datalist;
					that.lclist = lclist;
					
			});
		},
		setst: function (e) {
			var that=this
			console.log(e);
			that.id= e.currentTarget.dataset.id
			that.showstatus=true;
		
		},
		close:function(e){
			var that=this
			that.showstatus=false
		},
		radioChange:function(e){
			var that=this
			console.log(e)
		},
		formsubmit: function (e) {
		  var that = this;		
		  var formdata = e.detail.value;
		  var content = formdata.content;
		  var liucheng = formdata.liucheng;
		  if (liucheng == '') {
		    app.error('请选择流程');
		    return false;
		  }
		  app.showLoading(true);
		  app.post('ApiAdminWorkorder/addjindu', {logid:that.id,lcid:liucheng,content: content}, function (res) {
		    app.showLoading(false);
		    if (res.status == 1) {
		      app.success('处理成功');
					setTimeout(function () {
					  that.getdata();
						that.showstatus=false
					}, 1000);
		    } else {
		      app.error(res.msg);
		    }
		  });
		},
		jindu:function(e){
			var that=this
			that.ishowjindu=true
			var id = e.currentTarget.dataset.id
			//读取进度表
			app.post('ApiWorkorder/selectjindu', { id: id }, function (res) {
					if(res.status==1){
						var data = res.data
						that.jdlist =data
					}
				
			})
		},
		closejd:function(e){
			var that=this
			that.ishowjindu=false
		}
  }
}
</script>
<style>

	.content{ width:100%;margin:0;}
	.content .item{ width:94%;margin:20rpx 3%;;background:#fff;border-radius:16rpx;padding:30rpx 30rpx;display:flex;align-items:center;}
	.content .item:last-child{border:0}
	.content .item .f1{width:100%;display:flex;flex-direction:column}
	.content .item .f1 .t1{color:#000000;font-size:30rpx;word-break:break-all;overflow:hidden;text-overflow:ellipsis;}
	.content .item .f1 .t2{color:#666666;margin-top:10rpx}
	.content .item .f1 .t3{color:#666666}
	.content .item .f2{width:20%;font-size:32rpx;text-align:right}
	.content .item .f2 .t1{color:#03bc01}
	.content .item .f2 .t2{color:#000000}
	.content .item .f3{ flex:1;font-size:30rpx;text-align:right}
	.content .item .f3 .t1{color:#03bc01}
	.content .item .f3 .t2{color:#000000}
	.jindu{ border: 1rpx solid #ccc; font-size: 24rpx; padding: 5rpx 10rpx; border-radius: 10rpx; color: #555;}
	
	
	.modal{ position: fixed; background:rgba(0,0,0,0.3); width: 100%; height: 100%; top:0; z-index: 100;}
	.modal .modal_jindu{ background: #fff; position: absolute; top: 20%; align-items: center; margin: auto; width: 90%; left: 30rpx; border-radius: 10rpx; padding: 40rpx;}
	.modal_jindu .close image { width: 20rpx; height: 20rpx; position: absolute; top:10rpx; right: 20rpx;}
	.modal_jindu .title{ font-size: 32rpx; font-weight: bold;}
	.uni-list{ margin-top: 30rpx;}
	.uni-list-cell{ display: flex; height: 80rpx;}
	.beizhu label{ width: 100rpx;}
	.modal_jindu .btn{  background: #1658c6; border-radius: 3px;line-height: 24px; border: none; padding: 0 10px;color: #fff;font-size: 20px; text-align: center; width: 300px;  display: flex; height: 40px; justify-content: center;align-items: center;}
	
	
	.modal_jindu .item .f1{ width:60rpx;position:relative}
	/*.logistics img{width: 15px; height: 15px; position: absolute; left: -8px; top:11px;}*/
	.modal_jindu .item .f1 image{width: 30rpx; height: 100%; position: absolute; left: -16rpx; top: 0rpx;}
	.modal_jindu .item .f2{display:flex;flex-direction:column;flex:auto;padding:10rpx 0}
	.modal_jindu .item .f2 .t1{font-size: 30rpx;}
	.modal_jindu .item .f2 .t1{font-size: 26rpx;}
	.modal_jindu .item .f2 .t3{font-size: 24rpx; color:#008000; margin-top: 10rpx;}
	.modal_jindu .item .f2 .t4{font-size: 24rpx;  color:#008000;}
</style>
<template>
<view class="dp-business" :style="{
	color:params.color,
	backgroundColor:params.bgcolor,
	margin:(params.margin_y*2.2)+'rpx '+(params.margin_x*2.2)+'rpx',
	padding:(params.padding_y*2.2)+'rpx '+(params.padding_x*2.2)+'rpx',
	fontSize:(params.fontsize*2)+'rpx'
}">
		<view class="item" v-for="(item,index) in data" :key="item.id" @click="goto" :data-url="'/pages/business/index?id='+item.bid">
			<view class="f1"><image class="image" :src="item.logo"/></view>
			<view class="f2">
				<view class="title">{{item.name}}</view>
				<view class="score" v-if="params.showpingfen!=='0'"><image class="image" :src="pre_url+'/static/img/star'+item.commentscore+'.png'"/>{{item.comment_score}}分</view>
				<view class="sales" v-if="params.showsales!=='0'"><text>销量：</text>{{item.sales}}</view>
				<view class="address" v-if="params.showjianjie=='1'"><text :decode="true">{{item.content}}</text></view>
				<view class="address flex"><view class="flex1"><text v-if="params.showaddress!=='0'">{{item.address}}</text></view><view v-if="params.showdistance" :style="{color:t('color1')}">{{item.juli}}</view></view>
			</view>
		</view>
	</view>
</template>
<script>
	export default {
		data(){
			return {
				pre_url:getApp().globalData.pre_url
			}
		},
		props: {
			params:{},
			data:{}
		}
	}
</script>
<style>
.dp-business{height: auto; position: relative;}
.dp-business .item{background: #fff;padding:16rpx;overflow: hidden;margin-bottom:16rpx;display:flex;width:100%}
.dp-business .item .f1{width:200rpx;height:200rpx; margin-right:20rpx;flex-shrink:0}
.dp-business .item .f1 .image{ width: 100%;height:100%;border-radius:20rpx;object-fit: cover;}
.dp-business .item .f2{flex:1}
.dp-business .item .f2 .title{font-size:28rpx;font-weight:bold; color: #222;line-height:46rpx;margin-bottom:3px;}
.dp-business .item .f2 .score{font-size:24rpx;color:#f99716;}
.dp-business .item .f2 .score .image{width:140rpx; height:50rpx; vertical-align: middle;margin-bottom:3px; margin-right:3px;}
.dp-business .item .f2 .sales{font-size:24rpx; color:#31C88E;margin-bottom:3px;}
.dp-business .item .f2 .address{color:#999;font-size:24rpx;line-height:40rpx;margin-bottom:3px;}
</style>
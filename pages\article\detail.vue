<template>
<view>
	<block v-if="isload">
		<view class="container">
			<view class="header">
				<text class="title" v-if="detail.showname==1">{{detail.name}}</text>
				<view class="artinfo" v-if="detail.showsendtime==1 || detail.showauthor==1 || detail.showreadcount==1">
					<text class="t1" v-if="detail.showsendtime==1">{{detail.createtime}}</text>
					<text class="t2" v-if="detail.showauthor==1">{{detail.author}}</text>
					<text class="t3" v-if="detail.showreadcount==1">{{lang('read')}}：{{detail.readcount}}</text>
				</view>
				<view style="padding:8rpx 0">
					<dp :pagecontent="pagecontent"></dp>
				</view>
			</view>
		
			<!--评论-->
			<block v-if="detail.canpl==1">
			<view class="plbox">
				<view class="plbox_title"><text class="t1">{{lang('comment')}}</text><text>({{plcount}})</text></view>
				<view class="plbox_content">
					<block v-for="(item, idx) in datalist" :key="item.id">
					<view class="item1 flex">
						<view class="f1 flex0"><image :src="item.headimg"></image></view>
						<view class="f2 flex-col">
							<text class="t1">{{item.nickname}}</text>
							<view class="t2 plcontent"><rich-text :nodes="item.content"></rich-text></view>
							<block v-if="item.replylist.length>0">
							<view class="relist">
								<block v-for="(hfitem, index) in item.replylist" :key="index">
								<view class="item2">
									<view>{{hfitem.nickname}}：</view>
									<view class="f2 plcontent"> <rich-text :nodes="hfitem.content"></rich-text></view>
								</view>
								 </block>
							</view>
							</block>
							<view class="t3 flex">
								<text>{{item.createtime}}</text>
								<view class="flex1"><text v-if="detail.canplrp==1" class="phuifu" style="cursor:pointer" @tap="goto" :data-url="'pinglun?type=1&id=' + detail.id + '&hfid=' + item.id">{{lang('reply')}}</text></view>
								<view class="flex-y-center pzan" @tap="pzan" :data-id="item.id" :data-index="idx"><image :src="'/static/img/zan-' + (item.iszan==1?'2':'1') + '.png'"></image>{{item.zan}}</view>
							</view>
						</view>
					</view>
					</block>
				</view>
				<!-- <nodata v-if="nodata"></nodata> -->
				<!-- <nomore v-if="nomore"></nomore> -->
				<loading v-if="loading"></loading>
			</view>
			<view style="height:160rpx"></view>
			<view class="pinglun notabbarbot">
				<view class="pinput" @tap="goto" :data-url="'pinglun?type=0&id=' + detail.id">{{lang('add a comment')}}</view>
				<view class="zan flex-y-center" @tap="zan" :data-id="detail.id">
					<image :src="'/static/img/zan-' + (iszan?'2':'1') + '.png'"/><text style="padding-left:2px">{{detail.zan}}</text>
				</view>
				<block v-if="detail.btntxt && detail.btnurl">
						<view class="buybtn" style="cursor:pointer" :onclick="'location.href=' + detail.btnurl">{{detail.btntxt}}</view>
				</block>
			</view>
			</block>
		</view>
	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();
export default {
  data() {
    return {
			opt:{},
			loading:false,
      isload: false,
			menuindex:-1,

			detail:[],
      datalist: [],
      pagenum: 1,
      id: 0,
      pagecontent: "",
      title: "",
      sharepic: "",
			nodata:false,
			nomore:false,
      iszan: "",
			plcount:0,
    };
  },
  onLoad: function (opt) {
		this.opt = app.getopts(opt);
    this.getdata();
  },
	onPullDownRefresh: function () {
		this.getdata();
	},
	onShareAppMessage:function(){
		return this._sharewx({title:this.detail.name,desc:this.detail.subname,pic:this.detail.pic});
	},
	onShareTimeline:function(){
		var sharewxdata = this._sharewx({title:this.detail.name,desc:this.detail.subname,pic:this.detail.pic});
		var query = (sharewxdata.path).split('?')[1];
		return {
			title: sharewxdata.title,
			imageUrl: sharewxdata.imageUrl,
			query: query
		}
	},
  onReachBottom: function () {
    if (!this.nodata && !this.nomore && this.detail.canpl==1) {
      this.pagenum = this.pagenum + 1
      this.getpllist();
    }
  },
  methods: {
	  lang: function(k) {
	  	return app.lang(k);
	  },
		getdata:function(){
			var that = this;
			var id = that.opt.id;
			that.loading = true;
			app.get('ApiArticle/detail', {id: id}, function (res) {
				that.loading = false;
				if (res.status == 1){
					that.detail = res.detail;
					that.pagecontent = res.pagecontent;
					that.plcount = res.plcount;
					that.iszan = res.iszan;
					that.title = res.detail.name;
					that.sharepic = res.detail.pic;
					uni.setNavigationBarTitle({
						title: res.detail.name
					});
				} else {
					app.alert(res.msg);
				}
				that.pagenum = 1;
				that.datalist = [];
				that.getpllist();
				that.loaded({title:res.detail.name,desc:res.detail.subname,pic:res.detail.pic});
			});
		},
    getpllist: function () {
      var that = this;
      var pagenum = that.pagenum;
			that.loading = true;
			that.nodata = false;
			that.nomore = false;
      app.post('ApiArticle/getpllist', {pagenum: pagenum,id: that.detail.id}, function (res) {
				that.loading = false;
        var data = res.data;
				if (data.length == 0) {
					if(pagenum == 1){
						that.nodata = true;
					}else{
						that.nomore = true;
					}
				}
				var datalist = that.datalist;
				var newdata = datalist.concat(data);
				that.datalist = newdata;
      });
    },
    zan: function (e) {
      var that = this;
      var id = e.currentTarget.dataset.id;
      app.post("ApiArticle/zan", {id: id}, function (res) {
        if (res.type == 0) {
          //取消点赞
          var iszan = 0;
        } else {
          var iszan = 1;
        }
        that.iszan = iszan;
        that.detail.zan = res.zancount;
      });
    },
    pzan: function (e) {
      var that = this;
      var id = e.currentTarget.dataset.id;
      var index = e.currentTarget.dataset.index;
      var datalist = that.datalist;
      app.post("ApiArticle/pzan", {id: id}, function (res) {
        if (res.type == 0) {
          //取消点赞
          var iszan = 0;
        } else {
          var iszan = 1;
        }

        datalist[index].iszan = iszan;
        datalist[index].zan = res.zancount;
        that.datalist = datalist;
      });
    }
  }
};
</script>
<style>
.header{ background-color: #fff;padding: 10rpx 20rpx 0 20rpx;position: relative;display:flex;flex-direction:column;}
.header .title{width:100%;font-size: 36rpx;color:#333;line-height: 1.4;margin:10rpx 0;margin-top:20rpx;font-weight:bold}
.header .artinfo{width:100%;font-size:28rpx;color: #8c8c8c;font-style: normal;overflow: hidden;display:flex;margin:10rpx 0;}
.header .artinfo .t1{padding-right:8rpx}
.header .artinfo .t2{color:#777;padding-right:8rpx}
.header .artinfo .t3{text-align:right;flex:1;}
.header .subname{width:100%;font-size:28rpx;color: #888;border:1px dotted #ddd;border-radius:10rpx;margin:10rpx 0;padding:10rpx}


.pinglun{ width:96%;max-width:750px;margin:0 auto;position:fixed;display:flex;align-items:center;bottom:0;left:0;right:0;height:100rpx;background:#fff;z-index:995;border-top:1px solid #f7f7f7;padding:0 2%;box-sizing:content-box}
.pinglun .pinput{flex:1;color:#a5adb5;font-size:32rpx;padding:0;line-height:100rpx}
.pinglun .zan{padding:0 12rpx;line-height:100rpx}
.pinglun .zan image{width:48rpx;height:48rpx}
.pinglun .zan span{height:40rpx;line-height:50rpx;font-size:32rpx}
.pinglun .buybtn{margin-left:0.08rpx;background:#31C88E;height:72rpx;line-height:72rpx;padding:0 20rpx;color:#fff;border-radius:6rpx}

.plbox{width:100%;padding:40rpx 20rpx;background:#fff;margin-top:10px}
.plbox_title{font-size:28rpx;height:60rpx;line-height:60rpx;margin-bottom:20rpx}
.plbox_title .t1{color:#000;font-weight:bold}
.plbox_content .plcontent{vertical-align: middle;color:#111}
.plbox_content .plcontent image{ width:44rpx;height:44rpx;vertical-align: inherit;}
.plbox_content .item1{width:100%;margin-bottom:20rpx}
.plbox_content .item1 .f1{width:80rpx;}
.plbox_content .item1 .f1 image{width:60rpx;height:60rpx;border-radius:50%}
.plbox_content .item1 .f2{flex:1}
.plbox_content .item1 .f2 .t1{}
.plbox_content .item1 .f2 .t2{color:#000;margin:10rpx 0;line-height:60rpx;}
.plbox_content .item1 .f2 .t3{color:#999;font-size:20rpx}
.plbox_content .item1 .f2 .pzan image{width:32rpx;height:32rpx;margin-right:2px}
.plbox_content .item1 .f2 .phuifu{margin-left:6px;color:#507DAF}
.plbox_content .relist{width:100%;background:#f5f5f5;padding:4rpx 20rpx;margin-bottom:20rpx}
.plbox_content .relist .item2{font-size:24rpx;margin-bottom:10rpx}

.copyright{display:none}
</style>
<template>
	<view class="nodata">
		<image class="nodata-img" :src="pic"/>
		<view class="nodata-text">{{text}}</view>
	</view>
</template>
<script>
	export default {
		props: {
			pic:{default:'/static/img/empty.png'},
			text:{default:'No Data'}
		}
	}
</script>
<style>
.nodata {width: 100%; text-align: center; padding-top:100rpx;padding-bottom:100rpx}
.nodata-img{ width: 300rpx; height: 300rpx; display: inline-block; }
.nodata-text{ display: block; text-align: center; color: #999999; font-size:28rpx; width: 100%; margin-top: 30rpx; }
</style>
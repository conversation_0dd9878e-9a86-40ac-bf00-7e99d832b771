<template>
<view class="container">
	<block v-if="isload">
		<form @submit="formSubmit" @reset="formReset" report-submit="true">
			<view class="orderinfo">
				<view class="item">
					<text class="t1">{{lang('"the order no.')}}</text>
					<text class="t2" user-select="true" selectable="true">{{detail.ordernum}}</text>
				</view>
				
				<view class="item">
					<text class="t1">{{lang('can make out an invoice amount')}}</text>
					<text class="t2 red">{{detail.totalprice}}</text>
				</view>
				<view class="item">
					<text class="t1">{{lang('place the order of time')}}</text>
					<text class="t2">{{detail.createtime}}</text>
				</view>
			</view>
			<view class="orderinfo">
				<view class="item">
					<text class="t1">{{lang('invoice type')}}</text>
					<view class="t2">
						<block v-if="inputDisabled">
							<text v-if="invoice && invoice.type == 1">{{lang('commercial invoice')}}</text>
							<text v-if="invoice && invoice.type == 2">{{lang('the special vat invoice')}}</text>
						</block>
						<block v-else>
							<radio-group class="radio-group" @change="changeOrderType" name="invoice_type">
							<label class="radio" v-if="inArray(1,invoice_type)">
								<radio value="1" :checked="invoice_type_select == 1 ? true : false"></radio>{{lang('commercial invoice')}}
							</label>
							<label class="radio" v-if="inArray(2,invoice_type)">
								<radio value="2" :checked="invoice_type_select == 2 ? true : false"></radio>{{lang('the special vat invoice')}}
							</label>
							</radio-group>
						</block>
					 </view>
				</view>
				<view class="item">
					<text class="t1">{{lang('look up the type')}}</text>
					<view class="t2">
						<block v-if="inputDisabled">
							<text v-if="invoice && invoice.name_type == 1">{{lang('personal')}}</text>
							<text v-if="invoice && invoice.name_type == 2">{{lang('the company')}}</text>
						</block>
						<block v-else>
							<radio-group class="radio-group" @change="changeNameType" name="name_type">
							<label class="radio">
								<radio value="1" :checked="name_type_select == 1 ? true : false" :disabled="name_type_personal_disabled ? true : false"></radio>{{lang('personal')}}
							</label>
							<label class="radio">
								<radio value="2" :checked="name_type_select == 2 ? true : false"></radio>{{lang('the company')}}
							</label>
							</radio-group>
						</block>
					</view>
				</view>
				<view class="item">
					<text class="t1">{{lang('payable to the name')}}</text>
					<input class="t2" type="text" placeholder="" placeholder-style="font-size:28rpx;color:#BBBBBB" name="invoice_name" :disabled="inputDisabled" :value="invoice ? invoice.invoice_name : ''" ></input>
				</view>
				<view class="item" v-if="name_type_select == 2">
					<text class="t1">{{lang('company id number')}}</text>
					<input class="t2" type="text" placeholder="" placeholder-style="font-size:28rpx;color:#BBBBBB" name="tax_no" :disabled="inputDisabled" :value="invoice ? invoice.tax_no : ''"></input>
				</view>
				<view class="item" v-if="invoice_type_select == 2">
					<text class="t1">{{lang('the registered address')}}</text>
					<input class="t2" type="text" placeholder="" placeholder-style="font-size:28rpx;color:#BBBBBB" name="address" :disabled="inputDisabled" :value="invoice ? invoice.address : ''"></input>
				</view>
				<view class="item" v-if="invoice_type_select == 2">
					<text class="t1">{{lang('registered telephone')}}</text>
					<input class="t2" type="text" placeholder="" placeholder-style="font-size:28rpx;color:#BBBBBB" name="tel" :disabled="inputDisabled" :value="invoice ? invoice.tel : ''"></input>
				</view>
				<view class="item" v-if="invoice_type_select == 2">
					<text class="t1">{{lang('bank')}}</text>
					<input class="t2" type="text" placeholder="" placeholder-style="font-size:28rpx;color:#BBBBBB" name="bank_name" :disabled="inputDisabled" :value="invoice ? invoice.bank_name : ''"></input>
				</view>
				<view class="item" v-if="invoice_type_select == 2">
					<text class="t1">{{lang('the bank account')}}</text>
					<input class="t2" type="text" placeholder="" placeholder-style="font-size:28rpx;color:#BBBBBB" name="bank_account" :disabled="inputDisabled" :value="invoice ? invoice.bank_account : ''"></input>
				</view>
				<view class="item">
					<text class="t1">{{lang('mobile phone no.')}}</text>
					<input class="t2" type="text" :placeholder="lang('mobile phone number receive an invoice')" placeholder-style="font-size:28rpx;color:#BBBBBB" name="mobile" :disabled="inputDisabled" :value="invoice ? invoice.mobile : ''"></input>
				</view>
				<view class="item">
					<text class="t1">{{lang('email')}}</text>
					<input class="t2" type="text" :placeholder="lang('receive an email invoice')" placeholder-style="font-size:28rpx;color:#BBBBBB" name="email" :disabled="inputDisabled" :value="invoice ? invoice.email : ''"></input>
				</view>
				<!-- <view class="item" v-if="pay_transfer_info.pay_transfer_desc">
					<text class="text-min">{{pay_transfer_info.pay_transfer_desc}}</text>
				</view> -->
				<view class="item">
					<text class="t1">{{lang('state of make out an invoice')}}</text>
					<text class="t2" v-if="invoice">{{invoice.status_label}}</text>
					<text class="t2" v-else>{{lang('did not apply for')}}</text>
				</view>
				<view class="item" v-if="invoice && invoice.check_remark">
					<text class="t1">{{lang('review the note')}}</text>
					<text class="t2">{{invoice.check_remark}}</text>
				</view>
			</view>
			<button class="btn" v-if=" !invoice" form-type="submit" :style="{background:t('color1')}">{{lang('confirm')}}</button>
			<button class="btn" v-if=" invoice && invoice.status != 1" form-type="submit" :style="{background:t('color1')}">{{lang('modify the')}}</button>
			<view class="btn-a" @tap="back">{{lang('return')}}</view>
			<view style="padding-top:30rpx"></view>
		</form>
	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();
	export default {
		data() {
			return {
				opt:{},
				loading:false,
				isload: false,
				
				pics:[],
				detail:{},
				invoice:{},
				invoice_type:[],
				invoice_type_select:1,
				name_type_select:1,
				name_type_personal_disabled:false,
				inputDisabled:false
			}
		},
		onLoad: function (opt) {
			this.opt = app.getopts(opt);
			this.pre_url = app.globalData.pre_url;
			this.getdata();
			uni.setNavigationBarTitle({
				title:app.lang("SGMAll"),
				success() {
					console.log(app.lang("SGMAll"))
				}
			})
		},
		onPullDownRefresh: function () {
			this.getdata();
		},
		methods: {
			lang: function(k) {
				return app.lang(k);
			}, 
			getdata: function () {
				var that = this;
				var type = that.opt.type;
				that.loading = true;
				app.get('ApiOrder/invoice', {id: that.opt.orderid,type:type}, function (res) {
					that.loading = false;
					if (res.status == 0) {
						app.error(res.msg);
						return;
					}
					that.detail = res.detail;
					that.invoice = res.invoice;
					if(res.invoice) {
						that.invoice_type_select = res.invoice.type;
						that.name_type_select = res.invoice.name_type;
						if(that.invoice_type_select == 2) {
							that.name_type_personal_disabled = true;
						} else {
							that.name_type_personal_disabled = false;
						}
						if(res.invoice.status == 1) {
							that.inputDisabled = true;
						}
 					}else{
						if(app.inArray(1,res.invoice_type)){
							that.invoice_type_select = 1;
						}else if(app.inArray(2,res.invoice_type)){
							that.invoice_type_select = 2;
						}
					}
					that.invoice_type = res.invoice_type;
					
					that.loaded();
					//
				});
			},
    formSubmit: function (e) {
      var that = this;
      var id = that.opt.orderid;
			var type = that.opt.type;
			var formdata = e.detail.value;
			if(formdata.invoice_name == '') {
				app.error(that.lang('please fill in the name'));
				return;
			}
			if((formdata.name_type == 2 || formdata.invoice_type == 2) && formdata.tax_no == '') {
				///^[A-Z0-9]{15}$|^[A-Z0-9]{17}$|^[A-Z0-9]{18}$|^[A-Z0-9]{20}$/
				app.error(that.lang('please fill in the company'));
				return;
			}
			if(formdata.invoice_type == 2) {
				if(formdata.address == '') {
					app.error(that.lang('please fill in the registration address'));
					return;
				}
				if(formdata.tel == '') {
					app.error(that.lang('please fill out the registration call'));
					return;
				}
				if(formdata.bank_name == '') {
					app.error(that.lang('please fill in the bank'));
					return;
				}
				if(formdata.bank_account == '') {
					app.error(that.lang('please fill in the bank account'));
					return;
				}
			}
			if (formdata.mobile != '') {
				if(!/^1[3456789]\d{9}$/.test(formdata.mobile)){
					app.error(that.lang('mobile phone number is wrong'));
					return;
				}
			}
			if (formdata.email != '') {
				if(!/^([A-Za-z0-9_\-\.])+\@([A-Za-z0-9_\-\.])+\.([A-Za-z]{2,4})$/.test(formdata.email)){
					app.error(that.lang('email is wrong'));
					return;
				}
			}
			if(formdata.mobile == '' && formdata.email == '') {
				app.error(that.lang('mobile phone number and email, please fill out one of them'));
				return;
			}

			app.showLoading(that.lang('in the submission'));
      app.post('ApiOrder/invoice', {id: id,formdata:formdata,type:type}, function (res) {
				app.showLoading(false);
        app.alert(res.msg);
        if (res.status == 1) {
         setTimeout(function () {
					 if(type == 'shop')
						app.goto('/myshop/order/detail?id='+that.detail.id);
					 else
						app.goto('/activity/'+type+'/orderdetail?id='+that.detail.id);
         }, 1000);
        }
      });
    },
		changeOrderType: function(e) {
			var that = this;
			var value = e.detail.value;
			if(value == 2) {
				that.name_type_select = 2;
				that.name_type_personal_disabled = true;
			} else {
				that.name_type_personal_disabled = false;
			}
			that.invoice_type_select = value;
		},
		changeNameType: function(e) {
			var that = this;
			var value = e.detail.value;
			that.name_type_select = value;
		},
		back:function(e) {
			uni.navigateBack({
				
			})
		}
		}
	}
</script>

<style>
.radio radio{transform: scale(0.8);}
.radio:nth-child(2) { margin-left: 30rpx;}
.btn-a { text-align: center; padding: 30rpx; color: rgb(253, 74, 70);}
.text-min { font-size: 24rpx; color: #999;}
.orderinfo{width:96%;margin:0 2%;border-radius:8rpx;margin-top:16rpx;padding: 14rpx 3%;background: #FFF;}
.orderinfo .item{display:flex;width:100%;padding:20rpx 0;border-bottom:1px dashed #ededed;overflow:hidden}
.orderinfo .item:last-child{ border-bottom: 0;}
.orderinfo .item .t1{width:200rpx;flex-shrink:0}
.orderinfo .item .t2{flex:1;text-align:right; font-size: 28rpx;}
.orderinfo .item .red{color:red}
.orderinfo .item .grey{color:grey}

.form-item4{width:100%;background: #fff; padding: 20rpx 20rpx;margin-top:1px}
.form-item4 .label{ width:150rpx;}

.form-content{width:94%;margin:16rpx 3%;border-radius:10rpx;display:flex;flex-direction:column;background:#fff;overflow:hidden}
.form-item{ width:100%;padding: 32rpx 20rpx;}
.form-item .label{ width:100%;height:60rpx;line-height:60rpx}
.form-item .input-item{ width:100%;}
.form-item textarea{ width:100%;height:200rpx;border: 1px #eee solid;padding: 20rpx;}
.form-item input{ width:100%;border: 1px #f5f5f5 solid;padding: 10rpx;height:80rpx}
.form-item .mid{ height:80rpx;line-height:80rpx;padding:0 20rpx;}
.btn{ height:100rpx;line-height: 100rpx;width:90%;margin:0 auto;border-radius:50rpx;margin-top:50rpx;color: #fff;font-size: 30rpx;font-weight:bold}
</style>

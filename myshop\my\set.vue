<template>
	<view class="container">
		<block v-if="isload">
			<view class="content">
				<view class="info-item" style="height:136rpx;line-height:136rpx">
					<view class="t1" style="flex:1;">{{lang('head portrait')}}</view>
					<image :src="userinfo.headimg" style="width:88rpx;height:88rpx;" @tap="uploadHeadimg" />
					<image class="t3" src="/static/img/arrowright.png" />
				</view>
				<view class="info-item" @tap="goto" data-url="setnickname">
					<view class="t1">{{lang('nickname')}}</view>
					<view class="t2">{{userinfo.nickname}}</view>
					<image class="t3" src="/static/img/arrowright.png" />
				</view>
			</view>
			<view class="content">
				<view class="info-item" @tap="goto" data-url="setrealname">
					<view class="t1">{{lang('the name')}}</view>
					<view class="t2">{{userinfo.realname}}</view>
					<image class="t3" src="/static/img/arrowright.png" />
				</view>
				<view class="info-item" @tap="goto" data-url="settel">
					<view class="t1">{{lang('phone number')}}</view>
					<view class="t2">{{userinfo.tel}}</view>
					<image class="t3" src="/static/img/arrowright.png" />
				</view>
				<!--  <view class="info-item" @tap="goto" data-url="setsex">
          <text class="t1">{{lang('gender')}}</text>
          <text class="t2" v-if="userinfo.sex==1">{{lang("male")}}</text>
          <text class="t2" v-else-if="userinfo.sex==2">{{lang('female')}}</text>
          <text class="t2" v-else>{{lang('the unknown')}}</text>
          <image class="t3" src="/static/img/arrowright.png" />
        </view> -->
				<view class="info-item" @tap="goto" data-url="setbirthday">
					<text class="t1">{{lang('birthday')}}</text>
					<text class="t2">{{userinfo.birthday}}</text>
					<image class="t3" src="/static/img/arrowright.png" />
				</view>
			</view>

			<!-- <view class="content">
			<view class="info-item" @tap="goto" data-url="setweixin">
				<view class="t1">微信号</view>
				<view class="t2">{{userinfo.weixin}}</view>
				<image class="t3" src="/static/img/arrowright.png"/>
			</view>
			<view class="info-item" @tap="goto" data-url="setaliaccount">
				<view class="t1">支付宝账号</view>
				<view class="t2">{{userinfo.aliaccount}}</view>
				<image class="t3" src="/static/img/arrowright.png"/>
			</view>
			<view class="info-item" @tap="goto" data-url="setbankinfo">
				<text class="t1">银行卡</text>
				<text class="t2">{{userinfo.bankname ? '已设置' : ''}}</text>
				<image class="t3" src="/static/img/arrowright.png"/>
			</view>
		</view> -->

			<view class="content">
				<view class="info-item" @tap="goto" data-url="/pages/address/address">
					<view class="t1">{{lang('shipping address')}}</view>
					<view class="t2"></view>
					<image class="t3" src="/static/img/arrowright.png" />
				</view>
			</view>
			<!--  <view class="content" v-if="userinfo.haspwd==1">
        <view class="info-item" @tap="goto" data-url="/myshop/my/setpwd">
          <view class="t1">{{lang('change the password')}}</view>
          <view class="t2"></view>
          <image class="t3" src="/static/img/arrowright.png" />
        </view>
      </view> -->
			<view class="content">
				<view class="info-item" @tap="goto" data-url="/pages/index/changelang">
					<view class="t1">{{lang('changelang')}}</view>
					<view class="t2"></view>
					<image class="t3" src="/static/img/arrowright.png" />
				</view>
			</view>
			<view class="content">
				<view class="info-item" @tap="goto" data-url="/pages/index/changemoney">
					<view class="t1">{{lang('changemoney')}}</view>
					<view class="t2"></view>
					<image class="t3" src="/static/img/arrowright.png" />
				</view>
			</view>
			<view class="content">
				<view class="info-item" @tap="goto" data-url="/pages/index/login">
					<view class="t1">{{lang('switch account')}}</view>
					<view class="t2"></view>
					<image class="t3" src="/static/img/arrowright.png" />
				</view>
			</view>
		</block>
		<loading v-if="loading"></loading>
		<dp-tabbar :opt="opt"></dp-tabbar>
		<popmsg ref="popmsg"></popmsg>
	</view>
</template>

<script>
	var app = getApp();

	export default {
		data() {
			return {
				opt: {},
				loading: false,
				isload: false,
				menuindex: -1,

				userinfo: {},
			};
		},

		onLoad: function(opt) {
			this.opt = app.getopts(opt);
			this.getdata();
			uni.setNavigationBarTitle({
				title: app.lang("SGMAll"),
				success() {
					console.log(app.lang("SGMAll"))
				}
			})
		},
		onPullDownRefresh: function() {
			this.getdata();
		},
		methods: {
			lang: function(k) {
				console.log(k)
				return app.lang(k);
			},
			getdata: function() {
				var that = this;
				that.loading = true;
				app.get('ApiMy/set', {}, function(data) {
					that.loading = false;
					that.userinfo = data.userinfo;
					that.loaded();
				});
			},
			uploadHeadimg: function() {
				var that = this;
				app.chooseImage(function(urls) {
					var headimg = urls[0];
					that.userinfo.headimg = headimg;
					app.post('ApiMy/setfield', {
						headimg: headimg
					});
				}, 1)
			}
		}
	};
</script>
<style>
	.content {
		width: 94%;
		margin: 20rpx 3%;
		background: #fff;
		border-radius: 5px;
		padding: 0 20rpx;
	}

	.info-item {
		display: flex;
		align-items: center;
		width: 100%;
		background: #fff;
		padding: 0 3%;
		border-bottom: 1px #f3f3f3 solid;
		height: 96rpx;
		line-height: 96rpx;
	}

	.info-item:last-child {
		border: none;
	}

	.info-item .t1 {
		width: 300rpx;
		color: #8b8b8b;
		font-weight: bold;
		height: 96rpx;
		line-height: 96rpx;
	}

	.info-item .t2 {
		color: #444444;
		text-align: right;
		flex: 1;
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 1;
		overflow: hidden;
	}

	.info-item .t3 {
		width: 26rpx;
		height: 26rpx;
		margin-left: 20rpx;
	}
</style>

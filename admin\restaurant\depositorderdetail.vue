<template>
<view class="container">
	<block v-if="isload">
		<form @submit="formSubmit">
		<view class="form">
			<view class="form-item">
				<text class="label">寄存名称</text>
				<text class="flex1"></text>
				<text>{{info.name}}</text>
			</view>
			<view class="form-item">
				<text class="label">寄存数量</text>
				<text class="flex1"></text>
				<text>{{info.num}}</text>
			</view>
			<view class="form-item">
				<text class="label">寄存人</text>
				<text class="flex1"></text>
				<text>{{info.linkman}}</text>
			</view>
			<view class="form-item">
				<text class="label">手机号</text>
				<text class="flex1"></text>
				<text>{{info.tel}}</text>
			</view>
		</view>
		<view class="form">
			<view class="form-item">
				<text class="label">寄存备注</text>
				<text class="flex1"></text>
				<text>{{info.message}}</text>
			</view>
		</view>
		<view class="form">
			<view class="form-item">
				<text class="label">状态</text>
				<text class="flex1"></text>
				<text>{{info.statusLabel}}</text>
			</view>
		</view>
		<view class="form">
			<view class="flex-col">
				<text class="label" style="height:98rpx;line-height:98rpx;font-size:30rpx">寄存拍照</text>
				<view class="flex" style="flex-wrap:wrap;padding-bottom:20rpx;">
					
						<view class="layui-imgbox-img"><image :src="info.pic" @tap="previewImage" :data-url="item" mode="widthFix"></image></view>
					
				</view>
				<input type="text" hidden="true" name="pic" :value="pic.join(',')" maxlength="-1"></input>
			</view>
		</view>
		<button class="btn" v-if="info.status == 1" @tap="takeout" :style="'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'">取出</button>
		<button class="btn" v-if="info.status == 0" @tap="check" data-type="access" data-operate="通过审核" :style="'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'">通过审核</button>
		<button class="btn btn2" v-if="info.status == 0" @tap="check"  data-type="refuse" data-operate="驳回">驳回</button>
		</form>
	</block>
	<loading v-if="loading"></loading>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
			opt:{},
			loading:false,
      isload: false,
			menuindex:-1,
			pre_url:app.globalData.pre_url,

			pic:[],
			info:{}
			
    };
  },

  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.getdata();
  },
	onPullDownRefresh: function () {
		this.getdata();
	},
  methods: {
		getdata: function () {
			var that = this;
			that.loading = true;
			app.post('ApiAdminRestaurantDepositOrder/detail', {id: that.opt.id}, function (res) {
				that.loading = false;
				var data = res.detail;
				that.info = data;
				that.loaded();
			  
			});
		},
		takeout: function (e) {
		  var that = this;
		  app.confirm('确定要取出吗?', function () {
		    app.post('ApiAdminRestaurantDepositOrder/takeout', {orderid: that.info.id}, function (data) {
					if(data.status== 0){
						app.alert(data.msg);return;
					}
		      app.success(data.msg);
		      setTimeout(function () {
		        that.getdata();
		      }, 1000);
		    });
		  });
		},
		check: function(e){
			var that = this;
			var type = e.currentTarget.dataset.type;
			var operate = e.currentTarget.dataset.operate;
			app.confirm('确定要'+operate+'吗?', function () {
			  app.post('ApiAdminRestaurantDepositOrder/check', {orderid: that.info.id, type:type}, function (data) {
					if(data.status== 0){
						app.alert(data.msg);return;
					}
			    app.success(data.msg);
			    setTimeout(function () {
			      that.getdata();
			    }, 1000);
			  });
			});
		}
		
  }
};
</script>
<style>
page {position: relative;width: 100%;height: 100%;}
.container{height:100%;overflow:hidden;position: relative;}

.form{ width:94%;margin:0 3%;border-radius:5px;padding:20rpx 20rpx;padding: 0 3%;background: #FFF;margin-top:20rpx}
.form-item{display:flex;align-items:center;width:100%;border-bottom: 1px #ededed solid;height:98rpx;line-height:98rpx;font-size:30rpx}
.form-item:last-child{border:0}
.form-item .label{color: #000;width:200rpx;}
.form-item .input{flex:1;color: #000;text-align:right}
.form-item .f2{flex:1;color: #000;text-align:right}
.form-item .picker{height: 60rpx;line-height:60rpx;margin-left: 0;flex:1;color: #000;}

.btn{width:94%;margin:0 3%;margin-top:40rpx;height:90rpx;line-height:90rpx;text-align:center;background: linear-gradient(90deg, #FF7D15 0%, #FC5729 100%);color:#fff;font-size:32rpx;font-weight:bold;border-radius:10rpx}
.btn2 {background: #FFEEEE;border: 1px solid #FF9595;border-radius: 8rpx; color: #E34242;}

.layui-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}
.layui-imgbox-close{position: absolute;display: block;width:32rpx;height:32rpx;right:-16rpx;top:-16rpx;z-index:90;color:#999;font-size:32rpx;background:#fff}
.layui-imgbox-close image{width:100%;height:100%}
.layui-imgbox-img{display: block;width:200rpx;height:200rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}
.layui-imgbox-img>image{max-width:100%;}
.layui-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}
.uploadbtn{position:relative;height:200rpx;width:200rpx}
</style>
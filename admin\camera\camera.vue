<template>
  <view class="camera" style="height: 100%;width: 100%;">
    <view style="position: absolute;top: -999999px;"><canvas :style="{ width: w, height: h }"
                                                             canvas-id="firstCanvas"></canvas></view>
    <image id="camera" v-if="showimg" style="width: 100%;height: 1500rpx;display: block;" :src="src"></image>
    <camera v-if="showcamera" :device-position="taketype" flash="off" @error="error"
            :style="{width: w + 'px', height: h + 'px'}"></camera>

    <view v-if="istake"
          style="background-color: black;display: flex;justify-items: center;align-items: center;height: 140rpx;justify-content: space-around;">

      <image @click="backcamer" class="takephoto" mode="widthFix" src="/static/img/back.png"></image>
      <image @click="takePhotoNew()" class="takephoto" mode="widthFix" src="/static/img/take_btn_icon.png"></image>
      <image @click="chkcamera" class="anmation" style="width: 80rpx;height: 80rpx;" mode="widthFix"
             src="/static/img/icon_nocheck.png"></image>
    </view>
  </view>

</template>

<script>
export default {
  data() {
    return {
      taketype: 'back',
      socketOpen: false,
      src: '',
      canvasStyle: {},
      w: '',
      h: '',
      showimg: false,
      showcamera: true,
      isshow: true,
      istake: true,
      address: '',
      isautho: true,
    };
  },

  onLoad() {
    console.log("==========onLoad=============")
    this.initCamera();
  },

  methods: {
    //初始化相机
    initCamera() {
      const that = this;
      uni.getSystemInfo({
        success: function(res) {
          that.w = res.windowWidth;
          that.h = res.windowHeight - 60;
          console.log("初始化相机，宽:{}, 高:{}", that.w, that.h)
        }
      });
    },

    error() {
      const that = this;

      uni.showModal({
        title: '',
        showCancel: true,
        content: '打开相机失败,是否立即前往设置打开相机授权，否侧拍照功能无法使用',
        success: function(res) {
          if (res.confirm) {
            //查看是否点击确定
            uni.openSetting({
              //打开设置
              success: function(data) {
                if (data.authSetting['scope.camera'] === true) {
                  //到这一步表示打开了位置授权
                  that.againtake();
                  uni.showToast({
                    title: '授权成功',
                    icon: 'success',
                    duration: 1000
                  });

                } else {
                  uni.showToast({
                    title: '授权失败',
                    icon: 'none',
                    duration: 1000
                  });
                }
              },
              fail: function() {}
            });
          }
        }
      });
    },

    //重拍
    againtake() {
      this.src = '';
      this.showcamera = true;
      this.showimg = false;
      this.istake = true;
    },

    //切换摄像头
    chkcamera() {
      if (this.taketype === 'back') {
        this.taketype = 'front';
      } else this.taketype = 'back';
    },

    //取消拍照
    backcamer() {
      this.showcamera = true;
      this.showimg = false;
      uni.navigateBack();
    },

    takePhotoNew() {
      let that = this;
      uni.showLoading({
        title: '图片正在处理中...'
      });
      const ctx = uni.createCameraContext();

      ctx.takePhoto({
        quality: 'high',
        success: res => {
          this.src = res.tempImagePath;
          console.log("图片地址=========", this.src)
          uni.uploadFile({
            url: 'https://wuliu.ohomer.cn/?s=/ApiWuliu/createUploadOrder&aid=2',
            filePath: this.src,
            name: 'file',
            formData: {
              'ocrimage': this.src
            },
            success: (uploadFileRes) => {
              uni.hideLoading();
              console.log(uploadFileRes.data);
              let result = JSON.parse(uploadFileRes.data);
              if (result.code !== 1){
                uni.showToast({
                  title: result.msg,
                  icon: 'error',
                  duration: 1000
                });
              }else {
                uni.showToast({
                  title: '上传成功',
                  icon: 'success',
                  duration: 1000
                });
              }

            },
            fail: (err) => {
              uni.hideLoading();
              uni.showToast({
                title: '上传失败',
                icon: 'error',
                duration: 1000
              });
            }
          });
        }
      });
    }

  }
}
</script>

<style scoped>
.takephoto {
  width: 110rpx;
  height: 110rpx;
  position: relative;
}

.takephoto:active {
  top: 2px;
  /**向下偏移2px **/
}

.anmation {
  position: relative;
}

.anmation:active {
  top: 2px;
  /**向下偏移2px **/
}
</style>

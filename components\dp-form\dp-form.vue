<template>
<view class="dp-form" :style="{
	color:params.color,
	backgroundColor:params.bgcolor,
	margin:(params.margin_y*2.2)+'rpx '+(params.margin_x*2.2)+'rpx',
	padding:(params.padding_y*2.2)+'rpx '+(params.padding_x*2.2)+'rpx',
	fontSize:(params.fontsize*2)+'rpx'
}">
	<form @submit="editorFormSubmit" :data-formcontent="data.content" :data-tourl="params.hrefurl" :data-formid="data.id">
		<view style="display:none">{{test}}</view>
		<view :class="params.style==2&&item.key!='upload'?'dp-form-item':'dp-form-item2'" v-for="(item,idx) in data.content" :style="{borderColor:params.linecolor}" :key="item.id">
			<view class="label">{{item.val1}}<text v-if="item.val3==1&&params.showmuststar" style="color:red"> *</text></view>
			<block v-if="item.key=='input'">
				<text v-if="item.val5" style="margin-right:10rpx">{{item.val5}}</text>
				<input :type="(item.val4==1 || item.val4==2) ? 'digit' : 'text'" :name="'form'+idx" class="input" :placeholder="item.val2" placeholder-style="font-size:28rpx" :style="{borderColor:params.inputbordercolor}" :value="formdata['form'+idx]" @input="setfield" :data-formidx="'form'+idx"/>
			</block>
			<block v-if="item.key=='textarea'">
				<textarea :name="'form'+idx" class='textarea' :placeholder="item.val2" placeholder-style="font-size:28rpx" :style="{borderColor:params.inputbordercolor}" :value="formdata['form'+idx]" @input="setfield" :data-formidx="'form'+idx"/>
			</block>
			<block v-if="item.key=='radio'">
				<radio-group class="flex" :name="'form'+idx" style="flex-wrap:wrap" @change="setfield" :data-formidx="'form'+idx">
					<label v-for="(item1,idx1) in item.val2" :key="item1.id" class="flex-y-center">
							<radio class="radio" :value="item1" :checked="formdata['form'+idx] && formdata['form'+idx]==item1 ? true : false"/>{{item1}}
					</label>
				</radio-group>
			</block>
			<block v-if="item.key=='checkbox'">
				<checkbox-group :name="'form'+idx" class="flex" style="flex-wrap:wrap" @change="setfield" :data-formidx="'form'+idx">
					<label v-for="(item1,idx1) in item.val2" :key="item1.id" class="flex-y-center">
						<checkbox class="checkbox" :value="item1" :checked="formdata['form'+idx] && inArray(item1,formdata['form'+idx]) ? true : false"/>{{item1}}
					</label>
				</checkbox-group>
			</block>
			<block v-if="item.key=='selector'">
				<picker class="picker" mode="selector" :name="'form'+idx" :value="editorFormdata[idx]" :range="item.val2" @change="editorBindPickerChange" :data-idx="idx" :data-formidx="'form'+idx">
					<view v-if="editorFormdata[idx] || editorFormdata[idx]===0"> {{item.val2[editorFormdata[idx]]}}</view>
					<view v-else>{{lang('please select a')}}</view>
				</picker>
			</block>
			<block v-if="item.key=='time'">
				<picker class="picker" mode="time" :name="'form'+idx" :value="formdata['form'+idx]" :start="item.val2[0]" :end="item.val2[1]" :range="item.val2" @change="editorBindPickerChange" :data-idx="idx" :data-formidx="'form'+idx">
					<view v-if="editorFormdata[idx]">{{editorFormdata[idx]}}</view>
					<view v-else>{{lang('please select a')}}</view>
				</picker>
			</block>
			<block v-if="item.key=='date'">
				<picker class="picker" mode="date" :name="'form'+idx" :value="formdata['form'+idx]" :start="item.val2[0]" :end="item.val2[1]" :range="item.val2" @change="editorBindPickerChange" :data-idx="idx" :data-formidx="'form'+idx">
					<view v-if="editorFormdata[idx]">{{editorFormdata[idx]}}</view>
					<view v-else>{{lang('please select a')}}</view>
				</picker>
			</block>

			<block v-if="item.key=='region'">
				<uni-data-picker :localdata="items" :popup-title="lang('please select provinces,')" :placeholder="formdata['form'+idx] || lang('please select provinces,')" @change="onchange" :data-formidx="'form'+idx"></uni-data-picker>
				<!-- <picker class="picker" mode="region" :name="'form'+idx" value="" @change="editorBindPickerChange" :data-idx="idx">
					<view v-if="editorFormdata[idx]">{{editorFormdata[idx]}}</view> 
					<view v-else>请选择省市区</view>
				</picker> -->
				<input type="text" style="display:none" :name="'form'+idx" :value="regiondata ? regiondata : formdata['form'+idx]"/>
			</block>
			
			<block v-if="item.key=='upload' &&item.val1=='IC reverse'">
			
				<input type="text" style="display:none" :name="'form'+idx" :value="editorFormdata[idx]"/>
				<view class="flex updatabox" style="flex-wrap:wrap;padding-top:20rpx">
					<view class="dp-form-imgbox" v-if="editorFormdata[idx]">
						<view class="dp-form-imgbox-close" @tap="removeimg" :data-idx="idx" :data-formidx="'form'+idx"><image src="/static/img/ico-del.png" class="image"></image></view>
						<view class="dp-form-imgbox-img"><image class="image" :src="editorFormdata[idx]" @click="previewImage" :data-url="editorFormdata[idx]" mode="widthFix" :data-idx="idx"/></view>
					</view>
					<view v-else class="dp-form-uploadbtn updatabox" :style="{background:'url('+'../'+'/static/img/upimg2.png) no-repeat',backgroundSize:'560rpx auto',backgroundColor:'#F3F3F3'}" @click="editorChooseImage" :data-idx="idx" :data-formidx="'form'+idx"></view>
				</view>
			</block>
			<block v-if="item.key=='upload' &&item.val1=='IC front'">
				<input type="text" style="display:none" :name="'form'+idx" :value="editorFormdata[idx]"/>
				<view class="flex updatabox" style="flex-wrap:wrap;padding-top:20rpx">
					<view class="dp-form-imgbox updatabox2" v-if="editorFormdata[idx]">
						<view class="dp-form-imgbox-close" @tap="removeimg" :data-idx="idx" :data-formidx="'form'+idx"><image src="/static/img/ico-del.png" class="image"></image></view>
						<view class="dp-form-imgbox-img updatabox2"><image class="image updatabox2" :src="editorFormdata[idx]" @click="previewImage" :data-url="editorFormdata[idx]" mode="widthFix" :data-idx="idx"/></view>
					</view>
					<view v-else class="dp-form-uploadbtn updatabox" :style="{background:'url('+'../'+'/static/img/upimg1.png) no-repeat',backgroundSize:'560rpx auto',backgroundColor:'#F3F3F3'}" @click="editorChooseImage" :data-idx="idx" :data-formidx="'form'+idx"></view>
				</view>
			</block>
			<block v-if="item.key=='upload' &&item.val1=='IC handheld'">
				<input type="text" style="display:none" :name="'form'+idx" :value="editorFormdata[idx]"/>
				<view class="flex updatabox" style="flex-wrap:wrap;padding-top:20rpx">
					<view class="dp-form-imgbox updatabox2" v-if="editorFormdata[idx]">
						<view class="dp-form-imgbox-close" @tap="removeimg" :data-idx="idx" :data-formidx="'form'+idx"><image src="/static/img/ico-del.png" class="image"></image></view>
						<view class="dp-form-imgbox-img updatabox2"><image class="image updatabox2" :src="editorFormdata[idx]" @click="previewImage" :data-url="editorFormdata[idx]" mode="widthFix" :data-idx="idx"/></view>
					</view>
					<view v-else class="dp-form-uploadbtn updatabox" :style="{background:'url('+'../'+'/static/img/upimg3.png) no-repeat ',backgroundSize:'560rpx auto',backgroundColor:'#F3F3F3'}" @click="editorChooseImage" :data-idx="idx" :data-formidx="'form'+idx"></view>
				</view>
			</block>
			<block v-if="item.key=='upload' &&item.val1!='IC front'&&item.val1!='IC reverse' &&item.val1!='IC handheld'">
			
				<input type="text" style="display:none" :name="'form'+idx" :value="editorFormdata[idx]"/>
				<view class="flex" style="flex-wrap:wrap;padding-top:20rpx">
					<view class="dp-form-imgbox updatabox2" v-if="editorFormdata[idx]">
						<view class="dp-form-imgbox-close" @tap="removeimg" :data-idx="idx" :data-formidx="'form'+idx"><image src="/static/img/ico-del.png" class="image"></image></view>
						<view class="dp-form-imgbox-img updatabox2"><image class="image updatabox2" :src="editorFormdata[idx]" @click="previewImage" :data-url="editorFormdata[idx]" mode="widthFix" :data-idx="idx"/></view>
					</view>
					<view v-else class="dp-form-uploadbtn" :style="{background:'url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx',backgroundSize:'80rpx 80rpx',backgroundColor:'#F3F3F3'}" @click="editorChooseImage" :data-idx="idx" :data-formidx="'form'+idx"></view>
				</view>
			</block>
		</view>
		<view class="dp-form-item" v-if="data.payset==1">
			<text class="label">{{lang('pay the amount')}}：</text>
			<input type="text" class="input" name="price" :value='data.price' v-if="data.priceedit==1" @input="setfield" data-formidx="price"/>
			<text v-if="data.priceedit==0">{{data.price}}</text>
			<text style="padding-left:10rpx">{{lang('yuan')}}</text>
		</view>
		<button @tap="editorFormSubmit" v-if="data != ''" class="dp-form-btn" :style="{backgroundColor:params.btnbgcolor,border:'1px solid '+params.btnbordercolor,fontSize:(params.btnfontsize*2)+'rpx',color:params.btncolor,width:(params.btnwidth*2.2)+'rpx',height:(params.btnheight*2.2)+'rpx',lineHeight:(params.btnheight*2.2)+'rpx',borderRadius:(params.btnradius*2.2)+'rpx'}" :data-formcontent="data.content" :data-tourl="params.hrefurl" :data-formid="data.id">{{params.btntext}}</button>
	</form>
</view>
</template>
<script>
	var app = getApp();
	export default {
		data(){
			return {
				pre_url:getApp().globalData.pre_url,
				editorFormdata:[],
				test:'test',
				regiondata:'',
				items: [],
				tmplids: [],
				submitDisabled:false,
				formdata:{},
				formvaldata:{},
			}
		},
		props: {
			params:{},
			data:{},
			latitude:'',
			longitude:'',
		},
		mounted:function(){
			var that = this;
			app.get('ApiIndex/getCustom',{}, function (customs) {
				var url = app.globalData.pre_url+'/static/area.json';
				if(customs.data.includes('plug_zhiming')) {
					url = app.globalData.pre_url+'/static/area_gaoxin.json';
				}
				uni.request({
					url: app.globalData.pre_url+'/static/area.json',
					data: {},
					method: 'GET',
					header: { 'content-type': 'application/json' },
					success: function(res2) {
						that.items = res2.data
					}
				});
			});

			var pages = getCurrentPages(); //获取加载的页面
			var currentPage = pages[pages.length - 1]; //获取当前页面的对象
			var thispath = '/' + (currentPage.route ? currentPage.route : currentPage.__route__); //当前页面url 
			var opts = currentPage.$vm.opt;
			app.get('ApiForm/getlastformdata',{formid:that.data.id}, function (res) {
				if(res && res.status == 1 && res.data){
					var formcontent = that.data.content;
					var editorFormdata = [];
					var formvaldata = {};
					formvaldata.price = that.data.price
					for(var i in formcontent){
						var thisval = res.data['form'+i];
						if (formcontent[i].key == 'region') {
							that.regiondata = thisval;
						}
						if (formcontent[i].key == 'selector') {
							for(var j in formcontent[i].val2){
								if(formcontent[i].val2[j] == res.data['form'+i]){
									thisval = j;
								}
							}
						}
						if (formcontent[i].key == 'checkbox') {
							if(res.data['form'+i]){
								res.data['form'+i] = (res.data['form'+i]).split(',');
							}else{
								res.data['form'+i] = [];
							}
						}
						editorFormdata.push(thisval);
						formvaldata['form'+i] = thisval;
					}
					that.editorFormdata = editorFormdata;
					that.formvaldata = formvaldata;
					that.formdata = res.data;
				}else{
					var formvaldata = {};
					formvaldata.price = that.data.price;
					that.formvaldata = formvaldata;
				}
			})
		},
		methods:{
			lang: function(k) {
				return app.lang(k);
			},
			onchange(e) {
				console.log(e) 
        const value = e.detail.value
				console.log(value[0].text + ',' + value[1].text + ',' + value[2].text)
				this.regiondata = value[0].text + ',' + value[1].text + ',' + value[2].text;
      },
			setfield:function(e){
				var field = e.currentTarget.dataset.formidx;
				var value = e.detail.value;
				this.formvaldata[field] = value;
			},
			editorFormSubmit:function(e){
				var that = this;
				if(this.submitDisabled) return ;
				//console.log('form发生了submit事件，携带数据为：', e.detail.value)
				//var subdata = e.detail.value;
				var subdata = this.formvaldata;
				console.log(subdata)
				var formcontent = e.currentTarget.dataset.formcontent;
				var formid = e.currentTarget.dataset.formid;
				var tourl = e.currentTarget.dataset.tourl;
				var formdata = new Array();
				for (var i = 0; i < formcontent.length;i++){
					//console.log(subdata['form' + i]);
					if (formcontent[i].key == 'region') {
							subdata['form' + i] = that.regiondata;
					}
					if (formcontent[i].val3 == 1 && (subdata['form' + i] === '' || subdata['form' + i] === null || subdata['form' + i] === undefined || subdata['form' + i].length==0)){
							app.alert(formcontent[i].val1+' 必填');return;
					}
					if (formcontent[i].key =='switch'){
							if (subdata['form' + i]==false){
									subdata['form' + i] = '否'
							}else{
									subdata['form' + i] = '是'
							}
					}
					if (formcontent[i].key == 'selector') {
							subdata['form' + i] = formcontent[i].val2[subdata['form' + i]]
					}
					if (formcontent[i].key == 'input' && formcontent[i].val4 && subdata['form' + i]!==''){
						if(formcontent[i].val4 == '2'){ //手机号
							if (!/^1[3456789]\d{9}$/.test(subdata['form' + i])) {
								app.alert(formcontent[i].val1+' 格式错误');return;
							}
						}
						if(formcontent[i].val4 == '3'){ //身份证号
							if (!/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/.test(subdata['form' + i])) {
								app.alert(formcontent[i].val1+' 格式错误');return;
							}
						}
						if(formcontent[i].val4 == '4'){ //邮箱
							if (!/^(.+)@(.+)$/.test(subdata['form' + i])) {
								app.alert(formcontent[i].val1+' 格式错误');return;
							}
						}
					}
					formdata.push(subdata['form' + i])
				}
				
				//范围
				if(that.data.fanwei == 1 && (that.latitude == '' || that.longitude == '')) {
					app.alert('请定位当前地址或者刷新重试');return;
				}
				
				//console.log(formdata);
				that.submitDisabled = true;
				app.showLoading('提交中');

				var pages = getCurrentPages(); //获取加载的页面
				var currentPage = pages[pages.length - 1]; //获取当前页面的对象
				var thispath = '/' + (currentPage.route ? currentPage.route : currentPage.__route__); //当前页面url 
				var opts = currentPage.$vm.opt;

				app.post('ApiForm/formsubmit', {formid:formid,formdata:subdata,price:subdata.price,fromurl:thispath+'?id='+opts.id,latitude:that.latitude,longitude:that.longitude},function(data){
					that.tmplids = data.tmplids;
					app.showLoading(false);
					if (data.status == 0) {
						//that.showsuccess(res.data.msg);
						setTimeout(function () {
							app.error(data.msg);
						}, 100)
						that.submitDisabled = false;
						return;
					}else if(data.status == 1) { //无需付款
						that.subscribeMessage(function () {
							setTimeout(function () {
								app.success(data.msg);
							}, 100)
							setTimeout(function () {
								app.goto(tourl);
							}, 1000)
						});
						return;
					}else if(data.status==2){
						that.subscribeMessage(function () {
							setTimeout(function () {
								app.goto('/pages/pay/pay?id='+data.payorderid+'&tourl='+tourl);
							}, 100);
						});
					}
					that.submitDisabled = false;
				});
			},
			editorChooseImage: function (e) {
				var that = this;
				var idx = e.currentTarget.dataset.idx;
				var tplindex = e.currentTarget.dataset.tplindex;
				var editorFormdata = this.editorFormdata;
				if(!editorFormdata) editorFormdata = [];
				app.chooseImage(function(data){
					editorFormdata[idx] = data[0];
					console.log(editorFormdata)
					that.editorFormdata = editorFormdata
					that.test = Math.random();

					var field = e.currentTarget.dataset.formidx;
					that.formvaldata[field] = data[0];

				})
			},
			removeimg:function(e){
				var that = this;
				var idx = e.currentTarget.dataset.idx;
				var tplindex = e.currentTarget.dataset.tplindex;
				var field = e.currentTarget.dataset.formidx;
				var editorFormdata = this.editorFormdata;
				if(!editorFormdata) editorFormdata = [];
				editorFormdata[idx] = '';
				that.editorFormdata = editorFormdata
				that.test = Math.random();
				that.formvaldata[field] = '';
			},
			editorBindPickerChange:function(e){
				var idx = e.currentTarget.dataset.idx;
				var tplindex = e.currentTarget.dataset.tplindex;
				var val = e.detail.value;
				var editorFormdata = this.editorFormdata;
				if(!editorFormdata) editorFormdata = [];
				editorFormdata[idx] = val;
				console.log(editorFormdata)
				this.editorFormdata = editorFormdata
				this.test = Math.random();

				var field = e.currentTarget.dataset.formidx;
				this.formvaldata[field] = val;
			},
		}
	}
</script>
<style>
.dp-form{height: auto; position: relative;overflow: hidden; padding: 10rpx 0px; background: #fff;}
.dp-form .radio{transform:scale(.7);}
.dp-form .checkbox{transform:scale(.7);}
.dp-form-item{width: 100%;border-bottom: 1px #ededed solid;padding:10rpx 0px;display:flex;align-items: center;}
.dp-form-item:last-child{border:0}
.dp-form-item .label{line-height: 70rpx;width:140rpx;margin-right: 10px;flex-shrink:0}
.dp-form-item .input{height: 70rpx;line-height: 70rpx;overflow: hidden;flex:1;border:1px solid #eee;padding:0 8rpx;border-radius:2px;background:#fff}
.dp-form-item .textarea{height:180rpx;line-height:40rpx;overflow: hidden;flex:1;border:1px solid #eee;border-radius:2px;padding:8rpx}
.dp-form-item .radio{height: 70rpx;line-height: 70rpx;display:flex;align-items:center}
.dp-form-item .radio2{display:flex;align-items:center;}
.dp-form-item .radio .myradio{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:50%}
.dp-form-item .checkbox{height: 70rpx;line-height: 70rpx;display:flex;align-items:center}
.dp-form-item .checkbox2{display:flex;align-items:center;height: 40rpx;line-height: 40rpx;}
.dp-form-item .checkbox .mycheckbox{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:2px}
.dp-form-item .layui-form-switch{}
.dp-form-item .picker{height: 70rpx;line-height:70rpx;flex:1;}

.dp-form-item2{width: 100%;border-bottom: 1px #ededed solid;padding:10rpx 0px;display:flex;flex-direction:column;align-items: flex-start;}
.dp-form-item2:last-child{border:0}
.dp-form-item2 .label{height:70rpx;line-height: 70rpx;width:100%;margin-right: 10px;}
.dp-form-item2 .input{height: 70rpx;line-height: 70rpx;overflow: hidden;width:100%;border:1px solid #eee;padding:0 8rpx;border-radius:2px;background:#fff}
.dp-form-item2 .textarea{height:180rpx;line-height:40rpx;overflow: hidden;width:100%;border:1px solid #eee;border-radius:2px;padding:8rpx}
.dp-form-item2 .radio{height: 70rpx;line-height: 70rpx;display:flex;align-items:center;}
.dp-form-item2 .radio2{display:flex;align-items:center;}
.dp-form-item2 .radio .myradio{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:50%}
.dp-form-item2 .checkbox{height: 70rpx;line-height: 70rpx;display:flex;align-items:center;}
.dp-form-item2 .checkbox2{display:flex;align-items:center;height: 40rpx;line-height: 40rpx;}
.dp-form-item2 .checkbox .mycheckbox{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:2px}
.dp-form-item2 .layui-form-switch{}
.dp-form-item2 .picker{height: 70rpx;line-height:70rpx;flex:1;width:100%;}
.dp-form-btn{margin: 0 auto;background: #ff4f4f;color: #fff;margin-top: 15px;margin-bottom:10px;text-align:center}
.flex-y-center {margin-right: 20rpx;}

.dp-form-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}
.dp-form-imgbox-close{position: absolute;display: block;width:32rpx;height:32rpx;right:-16rpx;top:-16rpx;color:#999;font-size:32rpx;background:#fff}
.dp-form-imgbox-close .image{width:100%;height:100%}
.dp-form-imgbox-img{display: block;width:200rpx;height:200rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}
.dp-form-imgbox-img>.image{max-width:100%;}
.dp-form-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}
.dp-form-uploadbtn{position:relative;height:200rpx;width:200rpx}
.updatabox{
	width: 560rpx !important;
 	height: 280rpx !important; 
	margin: 0 auto 40rpx auto !important;
	
	background-size: 100% 100% !important;

}
.updatabox2{
	width: 560rpx !important;
	height: 280rpx !important; 
	margin: 0 auto !important;
}
.input,.picker{
	background: #eee !important;
	border-radius: 10rpx !important;
}
</style>
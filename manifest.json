{
  "name": "E速优选",
  "appid": "__UNI__EF8674F", //__UNI__9080613
  "description": "",
  "versionName": "1.0.3",
  "versionCode": 103,
  "transformPx": false,
  "app-plus": {
    "compatible": {
      "ignoreVersion": true
    },
    "usingComponents": true,
    "nvueCompiler": "uni-app",
    "compilerVersion": 3,
    "splashscreen": {
      "alwaysShowBeforeRender": true,
      "waiting": true,
      "autoclose": true,
      "delay": 0
    },
    "modules": {
      "OAuth": {},
      "Share": {},
      "Payment": {},
      "VideoPlayer": {}
    },
    "distribute": {
      "android": {
        "permissions": [
          "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
          "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
          "<uses-permission android:name=\"android.permission.READ_CONTACTS\"/>",
          "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
          "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
          "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
          "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
          "<uses-permission android:name=\"android.permission.WRITE_CONTACTS\"/>",
          "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
          "<uses-permission android:name=\"android.permission.CAMERA\"/>",
          "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>",
          "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
          "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>",
          "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
          "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
          "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
          "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>",
          "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
          "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>",
          "<uses-feature android:name=\"android.hardware.camera\"/>",
          "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>",
          "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
        ]
      },
      "ios": {
        "capabilities": {
          "entitlements": {
            "com.apple.developer.associated-domains": [
              "applinks:v2ulink.diandashop.com",
              "applinks:static-7c82a81f-93f5-4c76-aa1d-9f56479b257e.bspapp.com",
              "applinks:ulink.diandashop.com"
            ]
          }
        },
        "idfa": false,
        "privacyDescription": {
          "NSPhotoLibraryUsageDescription": "从相册中选择图片作为用户头像及商品评价等",
          "NSLocationWhenInUseUsageDescription": "用于展示店铺距离",
          "NSCameraUsageDescription": "拍摄图片作为用户头像及商品评价等",
          "NSPhotoLibraryAddUsageDescription": "保存商品海报等图片到手机",
          "NSMicrophoneUsageDescription": "录音上传到用户点评",
          "NSLocationAlwaysUsageDescription": "实时获取位置进行定位",
          "NSLocationAlwaysAndWhenInUseUsageDescription": "获取当前位置用于展示距离"
        }
      },
      "sdkConfigs": {
        "ad": {},
        "oauth": {
          "weixin": {
            "appid": "wxd441dec22ba4fdff",
            "appsecret": "",
            "UniversalLinks": "https://ulink.diandashop.com/uni-universallinks/__UNI__9080613"
          },
          "apple": {}
        },
        "share": {
          "weixin": {
            "appid": "wxd441dec22ba4fdff",
            "UniversalLinks": "https://ulink.diandashop.com/uni-universallinks/__UNI__9080613"
          }
        },
        "payment": {
          "weixin": {
            "__platform__": ["ios", "android"],
            "appid": "wxd441dec22ba4fdff",
            "UniversalLinks": "https://ulink.diandashop.com/uni-universallinks/__UNI__9080613"
          },
          "alipay": {
            "__platform__": ["ios", "android"]
          }
        },
        "maps": {}
      },
      "icons": {
        "android": {
          "hdpi": "unpackage/res/icons/72x72.png",
          "xhdpi": "unpackage/res/icons/96x96.png",
          "xxhdpi": "unpackage/res/icons/144x144.png",
          "xxxhdpi": "unpackage/res/icons/192x192.png"
        },
        "ios": {
          "appstore": "unpackage/res/icons/1024x1024.png",
          "ipad": {
            "app": "unpackage/res/icons/76x76.png",
            "app@2x": "unpackage/res/icons/152x152.png",
            "notification": "unpackage/res/icons/20x20.png",
            "notification@2x": "unpackage/res/icons/40x40.png",
            "proapp@2x": "unpackage/res/icons/167x167.png",
            "settings": "unpackage/res/icons/29x29.png",
            "settings@2x": "unpackage/res/icons/58x58.png",
            "spotlight": "unpackage/res/icons/40x40.png",
            "spotlight@2x": "unpackage/res/icons/80x80.png"
          },
          "iphone": {
            "app@2x": "unpackage/res/icons/120x120.png",
            "app@3x": "unpackage/res/icons/180x180.png",
            "notification@2x": "unpackage/res/icons/40x40.png",
            "notification@3x": "unpackage/res/icons/60x60.png",
            "settings@2x": "unpackage/res/icons/58x58.png",
            "settings@3x": "unpackage/res/icons/87x87.png",
            "spotlight@2x": "unpackage/res/icons/80x80.png",
            "spotlight@3x": "unpackage/res/icons/120x120.png"
          }
        }
      }
    }
  },
  "quickapp": {},
  "mp-weixin": {
    "appid": "wxe03fffbc8b18adb2",
    "setting": {
      "urlCheck": false,
      "es6": true,
      "minified": true,
      "postcss": false
    },
    "usingComponents": true,
    "permission": {
      "scope.userLocation": {
        "desc": "你的位置信息将用于小程序位置接口的效果展示"
      }
    },
    "requiredPrivateInfos": [
      "chooseLocation",
      "getLocation",
      "onLocationChange",
      "startLocationUpdateBackground",
      "chooseAddress"
    ],
    "plugins": {},
    "optimization": {
      "subPackages": true
    },
    "lazyCodeLoading": "requiredComponents"
  },
  // "requiredPrivateInfos" : [ "getLocation" ]
  "mp-alipay": {
    "usingComponents": true,
    "appid": "2021002142648644"
  },
  "mp-baidu": {
    "usingComponents": true,
    "appid": "23935205"
  },
  "mp-toutiao": {
    "usingComponents": true,
    "appid": "ttdf53a9ee406be5c001"
  },
  "h5": {
    "router": {
      "base": "./"
    },
    "devServer": {
      "disableHostCheck": true,
      "proxy": {
        "/api": {
          "target": "https://esu.top/",
          "changeOrigin": true,
          "pathRewrite": {
            "^/api": "" // https://www.abc.com/login
          }
        }
      }
    },
    "title": "",
    "sdkConfigs": {
      "maps": {
        "google": {
          "key": "AIzaSyAJtPYLmT1jUQGAS23zca1G2eBDFyKR-Zo"
        }
      }
    }
  },
  "mp-qq": {
    "appid": "1111614727",
    "setting": {
      "es6": true,
      "minified": true
    }
  },
  "_spaceID": "de1419c6-c6c9-4817-a01a-f508977ee3df"
}
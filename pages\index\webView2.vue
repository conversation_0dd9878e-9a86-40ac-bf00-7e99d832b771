<template>
<view>
<web-view :src="url" v-if="url!=''"></web-view>
<view v-if="showBrowserOpen" style="width:100%;height:100vh;overflow:hidden">
	<image :src="pre_url + '/static/img/showBrowserOpen.jpg'" style="width:100%;" mode="widthFix"/>
</view>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
			opt:{},
			loading:false,
      isload: false,
			menuindex:-1,

      url: '',
			showBrowserOpen:false,
			pre_url:app.globalData.pre_url,
    };
  },

  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		if(this.opt.tourl) this.tourl = decodeURIComponent(this.opt.tourl);
		this.getdata();
  },
  methods: {
	  lang: function(k) {
	  	return app.lang(k);
	  },
		getdata: function(){
			this.orderid = this.opt.orderid;
			var that = this;
			if(this.opt.typeid == '23' && app.globalData.platform == 'mp'){
				that.showBrowserOpen = true;
				return;
			}
			app.globalData.session_id = this.opt.session_id;
			app.showLoading(this.lang('in the submission'));
			app.post('ApiPay/pay',{op:'submit',orderid: this.orderid,typeid: this.opt.typeid},function(res){
				app.showLoading(false);
				console.log(res)
				if(app.globalData.platform == 'h5'){
					app.goto(res.url);
				}else{
					that.url = res.url;
				}
			});
		}
  }
};
</script>

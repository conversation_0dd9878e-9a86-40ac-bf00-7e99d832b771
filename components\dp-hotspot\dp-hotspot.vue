<template>
<view class="dp-hotspot" :style="{
	margin:(params.margin_y*2.2)+'rpx '+(params.margin_x*2.2)+'rpx 0'
}">
	<image class="image" :src="params.imgurl" mode="widthFix"/>
	<view v-for="(item,index) in data" :key="item.id" @click="goto" :data-url="item.hrefurl" :style="{
		width:(item.width*2.2)+'rpx',
		height:(item.height*2.2)+'rpx',
		left:(item.left*2.2)+'rpx',
		top:(item.top*2.2)+'rpx'
	}" class="hotarea"></view>
</view>
</template>
<script>
	export default {
		props: {
			params:{},
			data:{}
		}
	}
</script>
<style>
.dp-hotspot {position:relative}
.dp-hotspot .image{width:100%;height:auto;margin: 0px; padding: 0px;}
.dp-hotspot .hotarea{position:absolute;z-index:3}
</style>
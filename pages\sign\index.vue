<template>
<view class="container">
	<block v-if="isload">
		<view class="qd_head">
			<image :src="pre_url + '/static/img/sign-bg.png'" class="qdbg"></image>
			<view class="myscore"><view class="f1">{{userinfo.score}}</view><view class="f2">{{t('积分')}}</view></view>
			<view class="signlog" @tap="goto" data-url="signrecord">签到记录</view>
			
			<view class="signbtn" v-if="!hassign">
				<button class="btn" :style="{background:t('color1')}" @tap="signin">立即签到</button>
			</view>
			<view class="signbtn" v-else>
				<button class="btn2">今日已签到</button>
				<view class="signtip">已连续签到{{userinfo.signtimeslx}}天</view>
			</view>
		</view>
         <view v-if="display" class="qd_guize" >
         	<view class="gztitle"> — 签到排名 — </view>
         	<view class="content">
         		<view v-for="(item, index) in list" :key="index" class="item">
         			<view class="f1">
         					<text class="t1">{{item.nickname}}</text>
         			</view>
         			<view class="f2">
         				    <text class="t2">连续签到</text>
         					<text class="t1">{{item.signtimeslx}}</text>
         					<text class="t2">天 </text>
         			</view>
         		</view>
         	</view>
         </view>
		<view class="qd_guize">
			<view class="gztitle"> — 签到规则 — </view>
			<view class="guize_txt">
				<parse :content="signset.guize" />
			</view>
		</view>
		
		
		
	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
			opt:{},
			loading:false,
            isload: false,
			menuindex:-1,
			pre_url:app.globalData.pre_url,
			hassign:false,
			signset:{},
			userinfo:{},
			list: [],
			display:false,
    };
  },

  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.getdata();
  },
	onPullDownRefresh: function () {
		this.getdata();
	},
  methods: {
	  lang: function(k) {
	  	return app.lang(k);
	  }, 
		getdata: function () {
			var that = this;
			that.loading = true;
			app.get('ApiSign/index', {}, function (res) {
				that.loading = false;
				that.hassign = res.hassign;
				that.signset = res.signset;
				that.userinfo = res.userinfo;
				that.list = res.list;
				that.display = res.signset.display;
				that.loaded();
			});
		},
    signin: function () {
      var that = this;
      app.post('ApiSign/signin', {}, function (data) {
        if (data.status == 1) {
          app.success('+' + data.scoreadd + that.t('积分'));
          that.getdata();
        } else {
          app.alert(data.msg);
        }
      });
    }
  }
};
</script>
<style>
page{background:#f4f4f4}
.qd_head{width: 100%;height:940rpx;position: relative;}
.qdbg{width: 100%;height:940rpx;}
.myscore{position:absolute;top:60rpx;width:100%;display:flex;color:#fff;flex-direction:column;align-items:center;z-index:2}
.myscore .f1{font-size:56rpx;font-weight:bold}
.myscore .f2{font-size:32rpx}
.signlog{position:absolute;top:50rpx;right:20rpx;color:#fff;font-size:28rpx;z-index:2}
.signbtn{position:absolute;top:760rpx;width:100%;display:flex;flex-direction:column;align-items:center;z-index:2}
.signbtn .btn{width:440rpx;height:80rpx;border-radius:40rpx;font-size:32rpx;font-weight:bold;color:#fff}
.signbtn .btn2{width:440rpx;height:80rpx;background:#FCB0B0;border-radius:40rpx;font-size:32rpx;font-weight:bold;color:#fff}
.signbtn .signtip{color:#999999;margin-top:20rpx}

.qd_guize{width:100%;margin:0;margin-bottom:20rpx}
.qd_guize .gztitle{width:100%;text-align:center;font-size:32rpx;color:#656565;font-weight:bold;height:100rpx;line-height:100rpx}
.guize_txt{box-sizing: border-box;padding:0 30rpx;line-height:42rpx;}
.content{ width:94%;margin:0 3%;}
.content .item{width:100%;margin:20rpx 0;background:#fff;border-radius:5px;padding:20rpx 20rpx;display:flex;align-items:center}
.content .item:last-child{border:0}
.content .item .f1{flex:1;display:flex;flex-direction:column}
.content .item .f1 .t1{color:#000000;font-size:30rpx;word-break:break-all;overflow:hidden;text-overflow:ellipsis;}
.content .item .f1 .t2{color:#666666}
.content .item .f1 .t3{color:#666666}
.content .item .f2{ flex:1;font-size:36rpx;text-align:right}
.content .item .f2 .t1{color:#03bc01}
.content .item .f2 .t2{color:#000000}
.content .item .f3{ flex:1;font-size:32rpx;text-align:right}
.content .item .f3 .t1{color:#03bc01}
.content .item .f3 .t2{color:#000000}
</style>
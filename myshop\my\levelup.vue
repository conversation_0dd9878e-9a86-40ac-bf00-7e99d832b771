<template>
<view class="container">
	<block v-if="errmsg=='' && isload">
		<image :src="pre_url + '/static/img/lv-upbanner.png'" class="banner" mode="widthFix"></image>
		
		<view class="contentbox">
			<view class="title">{{lang('welcome to join')}}{{sysset.name}}</view>
			<view class="title">{{lang('your current level')}}：<text style="font-weight:bold">{{userlevel.name}}</text></view>
		</view>
		<form @submit="formSubmit">
		<view class="contentbox">
			<view class="form-item1" v-if="aglevelCount>0">
				 <view class="panel">{{lang('level')}}：</view>
				 <radio-group @change="changelevel" name="levelid">
				 <block v-for="(item, idx) in aglevelList" :key="idx">
				 <label class="radio-item">
						<view class="flex1"><text style="font-weight:bold">{{item.name}}</text></view>
						<!-- <text>{{item.apply_paymoney>0 ? '加盟费'+item.apply_paymoney+'元':''}}</text> -->
						<radio :value="item.id+''"></radio>
				 </label>
				 </block>
				 </radio-group>
			</view>
			<view v-else class="noup"><text class="fa fa-check"></text> {{lang('you have reached the highest level of scalable')}}</view>
		</view>
		<view class="contentbox" v-if="selectedLevel.can_apply==1 && selectedLevel.id!=userinfo.levelid">
			<view class="applytj">
				<view class="f1">
					<text>{{selectedLevel.name}}</text>
					<text class="t2">{{lang('the application requirements')}}：</text>
				</view>
				<view class="f2">
					<view class="t1" v-if="selectedLevel.applytj!=''">{{selectedLevel.applytj}}</view>
					<view class="t2" v-if="selectedLevel.apply_paymoney>0">{{selectedLevel.apply_paytxt}}：{{selectedLevel.apply_paymoney}}</view>
					<view class="t3" v-if="selectedLevel.applytj_reach==0">{{lang('you temporarily do not meet the application requirements')}}</view>
					<view class="t4" v-if="selectedLevel.applytj_reach==1"><text v-if="selectedLevel.applytj!=''">{{lang('ou have reached the application requirements')}}，</text>{{lang('please fill out the application materials')}}</view>
				</view>
			</view>
			<view class="applydata" v-if="selectedLevel.applytj_reach==1">
				<view style="display:none">{{test}}</view>
				<view class="form-item" v-for="(item,idx) in selectedLevel.apply_formdata" :key="item.id">
					<view class="label">{{item.val1}}<text v-if="item.val3==1" style="color:red"> *</text></view>
					<block v-if="item.key=='input'">
						<input type="text" :name="'form'+idx" class="input" :placeholder="item.val2" placeholder-style="font-size:28rpx"/>
					</block>
					<block v-if="item.key=='textarea'">
						<textarea :name="'form'+idx" class='textarea' :placeholder="item.val2" placeholder-style="font-size:28rpx"/>
					</block>
					<block v-if="item.key=='radio'">
						<radio-group class="flex" :name="'form'+idx" style="flex-wrap:wrap">
							<label v-for="(item1,idx1) in item.val2" :key="item1.id" class="flex-y-center">
									<radio class="radio" :value="item1"/>{{item1}}
							</label>
						</radio-group>
					</block>
					<block v-if="item.key=='checkbox'">
						<checkbox-group :name="'form'+idx" class="flex" style="flex-wrap:wrap">
							<label v-for="(item1,idx1) in item.val2" :key="item1.id" class="flex-y-center">
								<checkbox class="checkbox" :value="item1"/>{{item1}}
							</label>
						</checkbox-group>
					</block>
					<block v-if="item.key=='selector'">
						<picker class="picker" mode="selector" :name="'form'+idx" value="" :range="item.val2" @change="editorBindPickerChange" :data-idx="idx">
							<view v-if="editorFormdata[idx] || editorFormdata[idx]===0"> {{item.val2[editorFormdata[idx]]}}</view>
							<view v-else>{{lang('please select a')}}</view>
						</picker>
					</block>
					<block v-if="item.key=='time'">
						<picker class="picker" mode="time" :name="'form'+idx" value="" :start="item.val2[0]" :end="item.val2[1]" :range="item.val2" @change="editorBindPickerChange" :data-idx="idx">
							<view v-if="editorFormdata[idx]">{{editorFormdata[idx]}}</view>
							<view v-else>{{lang('please select a')}}</view>
						</picker>
					</block>
					<block v-if="item.key=='date'">
						<picker class="picker" mode="date" :name="'form'+idx" value="" :start="item.val2[0]" :end="item.val2[1]" :range="item.val2" @change="editorBindPickerChange" :data-idx="idx">
							<view v-if="editorFormdata[idx]">{{editorFormdata[idx]}}</view>
							<view v-else>{{lang('please select a')}}</view>
						</picker>
					</block>

					<block v-if="item.key=='region'">
						<uni-data-picker :localdata="items" :popup-title="lang('please select provinces,')" @change="onchange" styleData="width:100%"></uni-data-picker>
						<!-- <picker class="picker" mode="region" :name="'form'+idx" value="" @change="editorBindPickerChange" :data-idx="idx">
							<view v-if="editorFormdata[idx]">{{editorFormdata[idx]}}</view> 
							<view v-else>请选择省市区</view>
						</picker> -->
						<input type="text" style="display:none" :name="'form'+idx" :value="regiondata"/>
					</block>
					<block v-if="item.key=='upload'">
						<input type="text" style="display:none" :name="'form'+idx" :value="editorFormdata[idx]"/>
						<view class="flex" style="flex-wrap:wrap;padding-top:20rpx">
							<view class="form-imgbox" v-if="editorFormdata[idx]">
								<view class="form-imgbox-img"><image class="image" :src="editorFormdata[idx]" @click="previewImage" :data-url="editorFormdata[idx]" mode="widthFix" :data-idx="idx"/></view>
							</view>
							<view class="form-uploadbtn" :style="{background:'url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 50rpx',backgroundSize:'80rpx 80rpx',backgroundColor:'#F3F3F3'}" @click="editorChooseImage" :data-idx="idx"></view>
						</view>
					</block>
				</view>
				<view class="form-item" v-if="selectedLevel.areafenhong == 1">
					<view class="label">{{lang('agency area')}}<text style="color:red"> *</text></view>
					<uni-data-picker :localdata="provincedata" :popup-title="lang('agency area')" :placeholder="areafenhong_province || lang('agency area')" @change="onchange1" styleData="width:100%"></uni-data-picker>
				</view>
				<view class="form-item" v-if="selectedLevel.areafenhong == 2">
					<view class="label">{{lang('agency area')}}<text style="color:red"> *</text></view>
					<uni-data-picker :localdata="citydata" :popup-title="lang('agency area')" :placeholder="areafenhong_city ? areafenhong_province + '/' + areafenhong_city : lang('agency area')" @change="onchange2" styleData="width:100%"></uni-data-picker>
				</view>
				<view class="form-item" v-if="selectedLevel.areafenhong == 3">
					<view class="label">{{lang('agency area')}}<text style="color:red"> *</text></view>
					<uni-data-picker :localdata="items" :popup-title="lang('agency area')" :placeholder="areafenhong_area ? areafenhong_province + '/' + areafenhong_city + '/' + areafenhong_area : lang('agency area')" @change="onchange3" styleData="width:100%"></uni-data-picker>
				</view>
			</view>
			<button class="form-btn" form-type="submit" v-if="selectedLevel.applytj_reach==1">{{lang('apply to be a')}}{{selectedLevel.name}}</button>
		</view>
		</form>

		<view class="contentbox" v-if="selectedLevel.can_up==1 && selectedLevel.up_condition_show == 1 && selectedLevel.id!=userinfo.levelid">
			<view class="uplvtj">
				<view class="f1">
                    
					<text>{{selectedLevel.name}}</text>
					<text class="t2">{{lang('upgrade conditions')}}：</text>
				</view>
				<view class="f2">
          <rich-text :nodes="selectedLevel.autouptj"></rich-text>
					<view class="t3">{{lang('you will automatically after upgraded to upgrade conditions')}}{{selectedLevel.name}}，{{lang('please continue to work hard')}}！</view>
				</view>
			</view>
		</view>
		<view class="contentbox">
			<view class="explain">
				<view class="f1">
					<text>{{selectedLevel.name}}</text>
					<text class="t2">{{lang('level privileges')}}：</text>
				</view>
				<view class="f2">
					<parse :content="userlevel.explain" v-if="userlevel.id==selectedLevel.id"/>
					<block v-for="(item,index) in aglevelList">
					<parse :content="item.explain" v-if="item.id==selectedLevel.id"/>
					</block>
				</view>
			</view>
		</view>
	</block>

	<block v-if="errmsg!='' && isload">
	<view class="zan-box">
			<image src="/static/img/zan.png" class="zan-img"></image>
			<view class="zan-text">{{errmsg}}</view>
	</view>
	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
</view>
</template>

<script>
var app = getApp();
export default {
  data() {
    return {
        opt:{},
        loading:false,
        isload: false,
        menuindex:-1,

        pre_url:app.globalData.pre_url,
        editorFormdata:[],
        regiondata:'',
        items: [],
        provincedata:[],
        citydata:[],
        test:'test',

        sysset: [],
        userinfo: [],
        aglevelList: [],
        aglevelCount: 0,
        applytj_reach: 0,
        errmsg: '',
        userlevel: "",
        selectedLevel: "",
        explain: "",
        applytj_info: "",
        autouptj_info: "",
        areafenhong_province:'',
        areafenhong_city:'',
        areafenhong_area:'',
    };
  },

  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		var that = this;
		app.get('ApiIndex/getCustom',{}, function (customs) {
			var url = app.globalData.pre_url+'/static/area.json';
			if(customs.data.includes('plug_zhiming')) {
				url = app.globalData.pre_url+'/static/area_gaoxin.json';
			}
			uni.request({
				url: url,
				data: {},
				method: 'GET',
				header: { 'content-type': 'application/json' },
				success: function(res2) {
					that.items = res2.data
					var provincedata = [];
					for(var i in res2.data){
						provincedata.push({text:res2.data[i].text,value:res2.data[i].value})
					}
					that.provincedata = provincedata;
					var citydata = [];
					for(var i in res2.data){
						var citys = [];
						for(var j in res2.data[i].children){
							citys.push({text:res2.data[i].children[j].text,value:res2.data[i].children[j].value});
						}
						citydata.push({text:res2.data[i].text,value:res2.data[i].value,children:citys});
					}
					that.citydata = citydata;
				}
			});
		});
		
		this.getdata();
		uni.setNavigationBarTitle({
			title:app.lang("SGMAll"),
			success() {
				console.log(app.lang("SGMAll"))
			}
		})
  },
	onPullDownRefresh: function () {
		this.getdata(true);
	},
  onPullDownRefresh: function () {
    this.getdata(true);
  },
  methods: {
	  lang: function(k) {
	  	return app.lang(k);
	  },  
		getdata: function () {
			var that = this;
			that.loading = true;
			app.get('ApiMy/levelup', {id:that.opt.id,cid:that.opt.cid}, function (res) {
				that.loading = false;
				uni.setNavigationBarTitle({
					title: that.t('会员') + that.lang('upgrade')
				});
				if (res.status == 0) {
					that.errmsg = res.msg;
				} else if (res.status == 2) {
					that.errmsg = res.msg;
					setTimeout(function () {
						app.goto('index');
					}, 1000);
				} else {
					that.userinfo = res.userinfo;
					that.bankname = res.userinfo.bankname;
					that.userlevel = res.userlevel;
					that.selectedLevel = res.userlevel;
					that.sysset = res.sysset;
					that.aglevelList = res.aglevelList;
					that.aglevelCount = res.aglevelList.length;
					that.explain = res.userlevel.explain;
				}
				that.loaded();
			});
		},
    cannotapply: function () {
      app.alert('不满足申请条件');
    },
    bindBanknameChange: function (e) {
      this.bankname = this.banklist[e.detail.value];
    },
    formSubmit: function (e) {
        var that = this;
        var apply_formdata = this.selectedLevel.apply_formdata;
        var formdata = e.detail.value;
        for (var i = 0; i < apply_formdata.length;i++){
            //console.log(formdata['form' + i]);
            if (apply_formdata[i].val3 == 1 && (formdata['form' + i] === '' || formdata['form' + i] === undefined || formdata['form' + i].length==0)){
                    app.alert(apply_formdata[i].val1+that.lang('mandatory'));return;
            }
            if (apply_formdata[i].key == 'selector') {
                    formdata['form' + i] = apply_formdata[i].val2[formdata['form' + i]]
            }
        }
        if(this.selectedLevel.areafenhong==1 && this.areafenhong_province==''){
            app.alert(that.lang('please select agency area'));return;
        }
        if(this.selectedLevel.areafenhong==2 && this.areafenhong_city==''){
            app.alert(that.lang('please select agency area'));return;
        }
        if(this.selectedLevel.areafenhong==3 && this.areafenhong_area==''){
            app.alert(that.lang('please select agency area'));return;
        }
        if (formdata.levelid == '') {
            app.alert(that.lang('please select agency area'));
            return;
        }
        formdata.areafenhong_province = this.areafenhong_province;
        formdata.areafenhong_city = this.areafenhong_city;
        formdata.areafenhong_area = this.areafenhong_area;
        app.showLoading(that.lang('in the submission'));
        app.post('ApiMy/levelup', formdata, function (res) {
            app.showLoading(false);
            if (res.status == 0) {
              app.alert(res.msg);
              return;
            }
            app.success(res.msg);
            setTimeout(function () {
              app.goto(res.url);
            }, 1000);
        });
    },
    changelevel: function (e) {
      var levelid = e.detail.value;
      var aglevelList = this.aglevelList;
      var agleveldata;

      for (var i in aglevelList) {
        if (aglevelList[i].id == levelid) {
          agleveldata = aglevelList[i];
          break;
        }
      }
      var applytj = [];
      var applytj_reach = 0;
      var member = this.userinfo;

      // var applytj_info = applytj.join(' 或 ');
      var autouptj = [];

      // var autouptj_info = autouptj.join(' 或 ');
      // this.applytj_info = applytj_info;
      // this.applytj_reach = applytj_reach;
      // this.autouptj_info = autouptj_info;
      this.selectedLevel = agleveldata;
      this.explain = agleveldata.explain;
	  this.editorFormdata = [];
    },
    editorChooseImage: function (e) {
        var that = this;
        var idx = e.currentTarget.dataset.idx;
        var tplindex = e.currentTarget.dataset.tplindex;
        var editorFormdata = this.editorFormdata;
        if(!editorFormdata) editorFormdata = [];
        app.chooseImage(function(data){
            editorFormdata[idx] = data[0];
            console.log(editorFormdata)
            that.editorFormdata = editorFormdata
            that.test = Math.random();
        })
    },
    editorBindPickerChange:function(e){
        var idx = e.currentTarget.dataset.idx;
        var tplindex = e.currentTarget.dataset.tplindex;
        var val = e.detail.value;
        var editorFormdata = this.editorFormdata;
        if(!editorFormdata) editorFormdata = [];
        editorFormdata[idx] = val;
        console.log(editorFormdata)
        this.editorFormdata = editorFormdata
        this.test = Math.random();
    },
    onchange(e) {
        const value = e.detail.value
        console.log(value[0].text + ',' + value[1].text + ',' + value[2].text)
        this.regiondata = value[0].text + ',' + value[1].text + ',' + value[2].text
    },
    onchange1(e) {
        const value = e.detail.value
        console.log(value[0].text)
        this.areafenhong_province = value[0].text;
    },
    onchange2(e) {
        const value = e.detail.value
        console.log(value[0].text + ',' + value[1].text)
        this.areafenhong_province = value[0].text;
        this.areafenhong_city = value[1].text
    },
    onchange3(e) {
        const value = e.detail.value
        this.areafenhong_province = value[0].text;
        this.areafenhong_city = value[1].text;
        this.areafenhong_area = value[2].text;
    },
  }
};
</script>
<style>
page{background:#F2D8B2;padding-bottom:10rpx}
.banner{ width:100%;background:#fff;height:400rpx;display:table}

.contentbox{width:94%;margin: 0 3%;padding:20rpx 40rpx;border-radius:20rpx;background:#fff;color:#B17D2D;margin-bottom:30rpx;display:flex;flex-direction:column;margin-bottom:10px}
.title{height:50rpx;line-height:50rpx}

.user-level {margin-left:10rpx;display:flex;}
.user-level image {width: 44rpx;height: 44rpx;margin-right: 10rpx;margin-left: -4rpx;}
.level-name {height: 36rpx;border-radius: 18rpx;font-size: 24rpx;color: #fff;background-color: #5c5652;padding: 0 16rpx 0 0;display:flex;align-items: flex-end;}
.level-name .name{display:flex;align-items:center;height:100%}

.noup{ width:100%;text-align:center;font-size:32rpx;color:green}

.form-item1{width: 100%;display:flex;flex-direction:column;color:#333}
.form-item1 .panel{width: 100%;font-size:32rpx;color:#B17D2D;}
.form-item1 radio-group{width: 100%;background:#fff;padding-left:10rpx;}
.form-item1 .radio-item{display:flex;width:100%;color:#000;align-items: center;background:#fff;padding:12rpx 0;}
.form-item1 .radio-item:last-child{border:0}
.radio-item .user-level{flex:1}
.form-item1 radio{ transform: scale(0.8);}

.applytj{width:100%;}
.applytj .f1{color:#000;font-size:30rpx;height:60rpx;line-height:60rpx;font-size:30rpx;padding-left:20rpx;display:flex;align-items:center}
.applytj .f1 .t2{padding-left:10rpx}
.applytj .f2{padding:20rpx;background-color:#fff;color:#f56060}
.applytj .f2 .t2{padding-top:10rpx;color:#88e}
.applytj .f2 .t3{padding-top:10rpx}
.applytj .f2 .t4{padding-top:10rpx;color:green;font-size:30rpx}
.uplvtj{width:100%;margin-top:20rpx;}
.uplvtj .f1{color:#000;font-size:30rpx;height:60rpx;line-height:60rpx;font-size:30rpx;padding-left:20rpx;display:flex;align-items:center}
.uplvtj .f1 .t2{padding-left:10rpx}
.uplvtj .f2{padding:20rpx;background-color:#fff;color:#f56060}
.uplvtj .f2 .t3{padding-top:10rpx;color:green}

.explain{ width:100%;margin:20rpx 0;}
.explain .f1{color:#000;font-size:30rpx;height:60rpx;line-height:60rpx;font-size:30rpx;padding-left:20rpx;display:flex;align-items:center}
.explain .f1 .t2{padding-left:10rpx}
.explain .f2{padding:20rpx;background-color:#fff;color:#999999}


.applydata{width: 100%;background: #fff;padding: 0 20rpx;color:#333}

.form-btn{width:100%;height: 88rpx;line-height: 88rpx;border-radius:8rpx;background: #FC4343;color: #fff;margin-top: 40rpx;margin-bottom: 20rpx;}

.applydata .radio{transform:scale(.7);}
.applydata .checkbox{transform:scale(.7);}
.form-item{width: 100%;border-bottom: 1px #ededed solid;padding:10rpx 0px;display:flex;align-items: center;}
.form-item:last-child{border:0}
.form-item .label{height:70rpx;line-height: 70rpx;width:160rpx;margin-right: 10px;flex-shrink:0}
.form-item .input{height: 70rpx;line-height: 70rpx;overflow: hidden;flex:1;border:1px solid #eee;padding:0 8rpx;border-radius:2px;}
.form-item .textarea{height:180rpx;line-height:40rpx;overflow: hidden;flex:1;border:1px solid #eee;border-radius:2px;padding:8rpx}
.form-item .radio{height: 70rpx;line-height: 70rpx;display:flex;align-items:center}
.form-item .radio2{display:flex;align-items:center;}
.form-item .radio .myradio{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:50%}
.form-item .checkbox{height: 70rpx;line-height: 70rpx;display:flex;align-items:center}
.form-item .checkbox2{display:flex;align-items:center;height: 40rpx;line-height: 40rpx;}
.form-item .checkbox .mycheckbox{margin-right:10rpx;display:inline-block;border:1px solid #aaa;background:#fff;height:32rpx;width:32rpx;border-radius:2px}
.form-item .layui-form-switch{}
.form-item .picker{height: 70rpx;line-height:70rpx;flex:1;}

.form-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}
.form-imgbox-close{position: absolute;display: block;width:32rpx;height:32rpx;right:-16rpx;top:-16rpx;color:#999;font-size:32rpx;background:#fff}
.form-imgbox-close .image{width:100%;height:100%}
.form-imgbox-img{display: block;width:180rpx;height:180rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}
.form-imgbox-img>.image{max-width:100%;}
.form-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}
.form-uploadbtn{position:relative;height:180rpx;width:180rpx}
</style>
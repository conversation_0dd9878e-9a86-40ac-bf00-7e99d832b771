<template>
	<view class="container">
		<view class="changebox" @tap="tochange">
			{{lang('changelang')}}
			<image src="/static/img/changelang.png" class="imgset" />
		</view>
		<!-- <view class="logoimage">
      <image class="logoimages" src="@/static/img/sign-up_03.png" alt="">
    </view> -->
		<!-- 	<view class="changeclass" v-if="!loading"><span class="rightclot" @tap="changelang('zh_cn')">中文</span><span class="rightclot" @tap="changelang('zh_tw')">中文(繁体)</span><span  @tap="changelang('en')" class="rightclot">English</span></view> -->
		<block v-if="isload">
			<block v-if="logintype==1">

				<form @submit="formSubmit" @reset="formReset">
					<view class="title" :style="{color:t('color1')}">{{lang("user login")}}</view>
					<view class="loginform">
						<!--   <view class="form-item">
              <image src="/static/img/sign-up_07.jpg" class="img" />

              <select class="select" @change=changes() v-model="selected" style="width: 80%;font-size:30rpx;"
                placeholder-style="font-size:30rpx;color:#B2B5BE" name="selectList">
                <option :value="item.code" v-for="(item,index) in countries" :key="index">{{item.name}}</option>
              </select>
            </view> -->
						<view class="form-item">
							<image src="/static/img/sign-up_06.jpg" class="img" />
							<input type="text" class="input"
								:placeholder="lang('please enter your mobile phone number')"
								placeholder-style="font-size:30rpx;color:#B2B5BE" name="tel" value=""
								@input="telinput" />
						</view>
						<view class="form-item">
							<image src="/static/img/sign-up_10.jpg" class="img" />
							<input type="text" class="input" :placeholder="lang('please enter your password')"
								placeholder-style="font-size:30rpx;color:#B2B5BE" name="pwd" value=""
								:password="true" />
						</view>
						<button class="form-btn"
							:style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}"
							form-type="submit">{{lang("login")}}</button>
						<button class="form-btn2" @tap="goback"
							v-if="!login_mast">{{lang("dont login for now")}}</button>
						<button v-if="platform2 == 'ios' && logintype_4==true" class="ioslogin-btn" @tap="ioslogin"
							style="width:100%">
							<image src="/static/images/apple.png" />{{lang("log in by apple account")}}
						</button>
					</view>
				</form>
				<view class="regtip">
					<view @tap="goto" data-url="reg" class="regtip1" :style="{color:t('color1')}"
						data-opentype="redirect">{{lang("existing account1")}}</view>
					<view class="flex1"></view>
					<view @tap="goto" data-url="getpwd" data-opentype="redirect" v-if="needsms">
						{{lang("forget your password")}}
					</view>
				</view>

				<block v-if="logintype_2 || logintype_3">

					<view class="othertip">
						<view class="othertip-line"></view>
						<view class="othertip-text">
							<text class="txt">{{lang("log in by other means")}}</text>
						</view>
						<view class="othertip-line"></view>
					</view>
					<view class="othertype">
						<view class="othertype-item" v-if="logintype_3" @tap="weixinlogin">
							<image class="img" :src="pre_url+'/static/img/login-'+platformimg+'.png'" />
							<text class="txt">{{platformname}}{{lang('login')}}</text>
						</view>
						<view class="othertype-item" v-if="logintype_2" @tap="changelogintype" data-type="2">
							<image class="img" src="/static/img/reg-tellogin.png" />
							<text class="txt">{{lang("log in by phone means")}}</text>
						</view>
					</view>
				</block>
			</block>
			<block v-if="logintype==2">
				<form @submit="formSubmit" @reset="formReset">
					<view class="title" :style="{color:t('color1')}">{{lang("user login")}}</view>
					<view class="loginform">
						<view class="form-item">
							<image src="@/static/img/sign-up_07.jpg" class="img" />

							<select class="select" @change=changes() v-model="selected"
								style="width: 80%;font-size:30rpx;" placeholder-style="font-size:30rpx;color:#B2B5BE"
								name="selectList">
								<option :value="item.code" v-for="(item,index) in countries" :key="index">{{item.name}}
								</option>
							</select>
							<!-- <img class="downimg" src="https://qi-1311633249.cos.ap-nanjing.myqcloud.com/gbshopee/down.png" alt="" srcset=""> -->
						</view>
						<view class="form-item">
							<image src="/static/img/sign-up_06.jpg" class="img" />
							<input type="text" class="input"
								:placeholder="lang('please enter your mobile phone number')"
								placeholder-style="font-size:30rpx;color:#B2B5BE" name="tel" value=""
								@input="telinput" />
						</view>
						<view class="form-item">
							<image src="/static/img/reg-code.png" class="img" />
							<input type="text" class="input" :placeholder="lang('please enter your mobile verify code')"
								placeholder-style="font-size:30rpx;color:#B2B5BE" name="smscode" value="" />
							<view class="code" :style="{color:t('color1')}" @tap="smscode">
								{{smsdjs||lang('obtaining the verification code')}}
							</view>
						</view>
						<view class="form-item" v-if="reg_invite_code && !parent">
							<image src="/static/img/reg-yqcode.png" class="img" />
							<input type="text" class="input"
								:placeholder="lang('please enter the inviter')+reg_invite_code_text+lang('optional')"
								placeholder-style="font-size:30rpx;color:#B2B5BE" name="yqcode" value="" />
						</view>
						<view class="form-item" v-if="reg_invite_code && parent" style="color: #333;">
							{{lang("the inviter")}}：<image :src="parent.headimg"
								style="width: 80rpx; height: 80rpx;border-radius: 50%;"></image>
							{{parent.nickname}}
						</view>
						<view class="xieyi-item" v-if="xystatus==1">
							<checkbox-group @change="isagreeChange"><label class="flex-y-center">
									<checkbox class="checkbox" value="1" :checked="isagree" />
									{{lang('i have read and agree')}}
								</label>
							</checkbox-group>
							<text :style="{color:t('color1')}" @tap="showxieyiFun">{{xyname}}</text>
						</view>
						<button class="form-btn" form-type="submit"
							:style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}">{{lang('login')}}</button>
						<button class="form-btn2" @tap="goback"
							v-if="!login_mast">{{lang('dont login for now')}}</button>
						<button v-if="platform2 == 'ios' && logintype_4==true" class="ioslogin-btn" @tap="ioslogin"
							style="width:100%">
							<image src="/static/images/apple.png" />{{lang("log in by apple account")}}
						</button>
					</view>
				</form>
				<block v-if="logintype_1 || logintype_3">
					<view class="othertip">
						<view class="othertip-line"></view>
						<view class="othertip-text">
							<text class="txt">{{lang('log in by other means')}}</text>
						</view>
						<view class="othertip-line"></view>
					</view>
					<view class="othertype">
						<view class="othertype-item" v-if="logintype_3" @tap="weixinlogin">
							<image class="img" :src="pre_url+'/static/img/login-'+platformimg+'.png'" />
							<text class="txt">{{platformname}}{{lang('login')}}</text>
						</view>
						<view class="othertype-item" v-if="logintype_1" @tap="changelogintype" data-type="1">
							<image class="img" src="/static/img/reg-tellogin.png" />
							<text class="txt">{{lang('log in by password means')}}</text>
						</view>
					</view>
				</block>
			</block>
			<block v-if="logintype==3">
				<view style="height:120rpx;"></view>
				<view class="authlogin">
					<view class="authlogin-logo">
						<image :src="logo" style="width:100%;height:100%" />
					</view>
					<view class="authlogin-name">{{lang('authorized login')}}{{name}}</view>
					<button class="authlogin-btn" @tap="authlogin"
						:style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}">{{platformname}}{{lang('authorized login')}}</button>
					<button class="authlogin-btn2" @tap="goback"
						v-if="!login_mast">{{lang("dont login for now")}}</button>
					<button v-if="platform2 == 'ios' && logintype_4==true" class="ioslogin-btn" @tap="ioslogin">
						<image src="/static/images/apple.png" />{{lang("log in by apple account")}}
					</button>
					<view class="xieyi-item" v-if="xystatus==1">
						<checkbox-group @change="isagreeChange"><label class="flex-y-center">
								<checkbox class="checkbox" value="1" :checked="isagree" />
								{{lang('i have read and agree')}}
							</label>
						</checkbox-group>
						<text :style="{color:t('color1')}" @tap="showxieyiFun">{{xyname}}</text>
					</view>
				</view>
			</block>
			<!-- 绑定手机号 -->
			<block v-if="logintype==4">

				<block v-if="platform == 'wx'">
					<view style="height:120rpx;"></view>
					<view class="authlogin">
						<view class="authlogin-logo">
							<image :src="logo" style="width:100%;height:100%" />
						</view>
						<view class="authlogin-name">{{lang('authorized login')}}{{name}}</view>
						<button class="authlogin-btn" open-type="getPhoneNumber" @getphonenumber="getPhoneNumber"
							:style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}">{{platformname}}{{lang('authorization bind the mobile phone number')}}</button>
						<button class="authlogin-btn2" @tap="nobindregister"
							v-if="login_bind==1">{{lang('dont bind for now')}}</button>
					</view>
				</block>
				<block v-else>
					<form @submit="bindregister" @reset="formReset">
						<view class="title" :style="{color:t('color1')}">{{lang('bind phone number')}}</view>
						<view class="loginform">
							<view class="form-item">
								<image src="/static/img/sign-up_06.jpg" class="img" />
								<input type="text" class="input"
									:placeholder="lang('please enter your mobile phone number')"
									placeholder-style="font-size:30rpx;color:#B2B5BE" name="tel" value=""
									@input="telinput" />
							</view>
							<view class="form-item">
								<image src="/static/img/reg-code.png" class="img" />
								<input type="text" class="input"
									:placeholder="lang('please enter your mobile verify code')"
									placeholder-style="font-size:30rpx;color:#B2B5BE" name="smscode" value="" />
								<view class="code" :style="{color:t('color1')}" @tap="smscode">
									{{smsdjs||lang('obtaining the verification code')}}
								</view>
							</view>
							<button class="form-btn" form-type="submit"
								:style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}">{{lang('confirm')}}</button>
							<button class="form-btn2" @tap="nobindregister"
								v-if="login_bind==1">{{lang('dont bind for now')}}</button>
						</view>
					</form>
				</block>
			</block>
			<uni-popup ref="popupMessage" type="dialog">
				<view class="attorneyPopup">
					<view class="attorneyPopup_title">
						{{JSON.parse(xycontent).name}}
					</view>
					<view class="attorneyPopup_content">
						<rich-text :nodes="JSON.parse(xycontent).content"></rich-text>
					</view>
					<view class="attorneyPopup_btn" @tap="hidexieyi">
						{{lang('i have read and agree')}}
					</view>
				</view>
			</uni-popup>
			<!-- <view v-if="showxieyi" class="xieyibox">
				<view class="xieyibox-content">
					<view style="overflow:scroll;height:100%;">
						<parse :content="xycontent" @navigate="navigate"></parse>
					</view>
					<view
						style="position:absolute;z-index:9999;bottom:10px;left:0;right:0;margin:0 auto;text-align:center; width: 50%;height: 60rpx; line-height: 60rpx; color: #fff; border-radius: 8rpx;"
						:style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}"
						@tap="hidexieyi">{{lang('i have read and agree')}}</view>
				</view>
			</view> -->

		</block>
		<loading v-if="loading"></loading>
		<dp-tabbar :opt="opt"></dp-tabbar>
		<popmsg ref="popmsg"></popmsg>
	</view>
</template>

<script>
	var app = getApp();

	export default {
		data() {
			return {
				opt: {},
				loading: false,
				isload: false,
				menuindex: -1,
				pre_url: app.globalData.pre_url,
				platform2: app.globalData.platform2,
				platform: '',
				platformname: '',
				platformimg: 'weixin',
				logintype: 0,
				logintype_1: true,
				logintype_2: false,
				logintype_3: false,
				logintype_4: false,
				isioslogin: false,
				needsms: false,
				logo: '',
				name: '',
				xystatus: 0,
				xyname: '',
				xycontent: '',
				showxieyi: false,
				isagree: false,
				smsdjs: '',
				tel: '',
				hqing: 0,
				frompage: '/myshop/my/usercenter',
				wxloginclick: false,
				iosloginclick: false,
				login_bind: 0,
				login_mast: false,
				reg_invite_code: 0,
				reg_invite_code_text: '',
				parent: {},
				selected: "+65",
				countries: [{
						"code": "+61",
						"name": app.lang("Australia +61")
					},
					{
						"code": "+673",
						"name": app.lang("Brunei +673")
					},
					{
						"code": "+880",
						"name": app.lang("Bangladesh +880")
					},
					{
						"code": "+855",
						"name": app.lang("Cambodia +855")
					},
					{
						"code": "+86",
						"name": app.lang("China +86")
					},
					{
						"code": "+886",
						"name": app.lang("Hong Kong +852")
					},

					{
						"code": "+91",
						"name": app.lang("India +91")
					},
					{
						"code": "+81",
						"name": app.lang("Japan +81")
					},
					{
						"code": "+60",
						"name": app.lang("Malaysia +60")
					},
					{
						"code": "+95",
						"name": app.lang("Myanmar +95")
					},
					{
						"code": "+64",
						"name": app.lang("New Zealand +64")
					},

					{
						"code": "+968",
						"name": app.lang("Oman +968")
					},
					{
						"code": "+63",
						"name": app.lang("Philippines +63")
					},
					{
						"code": "+65",
						"name": app.lang("Singapore +65")
					},


					{
						"code": "+82",
						"name": app.lang("South Korea +82")
					},
					{
						"code": "+94",
						"name": app.lang("Sri Lanka +94")
					},

					{
						"code": "+66",
						"name": app.lang("Thailand +66")
					},
					{
						"code": "+886",
						"name": app.lang("Taiwan   +886")
					},
					{
						"code": "+84",
						"name": app.lang("Vietnam +84")
					},
					{
						"code": "+62",
						"name": app.lang("Indonesia +62")
					},

				],
			};
		},

		onLoad: function(opt) {
			this.opt = app.getopts(opt);
			if (this.opt.frompage) this.frompage = decodeURIComponent(this.opt.frompage);
			if (this.opt.logintype) this.logintype = this.opt.logintype;
			if (this.opt.login_bind) this.login_bind = this.opt.login_bind;
			this.getdata();
			this.getcodedata()
			uni.setNavigationBarTitle({
				title: app.lang("login"),
				success() {
					console.log(app.lang("login"))
				}
			})
		},
		onPullDownRefresh: function() {
			this.getdata();
			this.getcodedata()
		},
		methods: {
			lang: function(k) {
				return app.lang(k);
			},
			changelang(lang) {
				console.log(111)
				app.changelangs(lang)
			},
			tochange() {
				app.goto('/pages/index/changelang?type=1', 'reLaunch')
			},
			changes: function(e) {
				console.log(this.selected)
			},
			getcodedata: function() {
				var that = this;
				that.loading = true;
				app.get('ApiIndex/areas', {}, function(res) {
					that.loading = false;
					console.log('codedata', res)

					that.countries = res.data
					that.selected = res.default
				})
			},
			getdata: function() {
				var that = this;
				that.loading = true;
				app.get('ApiIndex/login', {
					pid: app.globalData.pid
				}, function(res) {
					that.loading = false;
					if (res.status == 0) {
						app.alert(res.mg);
						return;
					}
					that.logintype_1 = res.logintype_1;
					that.logintype_2 = res.logintype_2;
					that.logintype_3 = res.logintype_3;
					// #ifdef APP-PLUS
					if (that.platform2 == 'ios') {
						if (plus.runtime.isApplicationExist({
								pname: 'com.tencent.mm',
								action: 'weixin://'
							})) {
							console.log('已安装微信')
						} else {
							that.logintype_3 = false;
							console.log('未安装微信')
						}
					}
					// #endif
					that.logintype_4 = res.logintype_4;
					that.login_mast = res.login_mast;
					that.needsms = res.needsms;
					that.reg_invite_code = res.reg_invite_code;
					that.reg_invite_code_text = res.reg_invite_code_text;
					that.parent = res.parent;
					if (that.logintype == 0) {
						if (that.logintype_1) {
							that.logintype = 1;
						} else if (that.logintype_2) {
							that.logintype = 2;
						} else if (that.logintype_3) {
							that.logintype = 3;
						}
					}
					that.xystatus = res.xystatus;
					that.xyname = res.xyname;
					console.log('res.xycontent', res.xycontent);
					that.xycontent = res.xycontent;
					that.logo = res.logo;
					that.name = res.name;
					that.platform = res.platform;
					if (that.platform == 'mp' || that.platform == 'wx' || that.platform == 'app') {
						that.platformname = '微信';
						that.platformimg = 'weixin';
					}
					if (that.platform == 'toutiao') {
						that.platformname = '头条';
						that.platformimg = 'toutiao';
					}
					if (that.platform == 'alipay') {
						that.platformname = '支付宝';
						that.platformimg = 'alipay';
					}
					if (that.platform == 'qq') {
						that.platformname = 'QQ';
						that.platformimg = 'qq';
					}
					if (that.platform == 'baidu') {
						that.platformname = '百度';
						that.platformimg = 'baidu';
					}
					that.loaded();
				});
			},
			formSubmit: function(e) {
				var that = this;
				var formdata = e.detail.value;
				if (formdata.tel == '') {
					app.alert(app.lang("please enter your mobile phone number"));
					return;
				}
				if (that.logintype == 1) {
					if (formdata.pwd == '') {
						app.alert(app.lang("please enter your password"));
						return;
					}
				}
				if (that.logintype == 2) {
					if (formdata.smscode == '') {
						app.alert(app.lang("please enter your mobile verify code"));
						return;
					}
					if (that.xystatus == 1 && !that.isagree) {
						app.error(app.lang("please read and agree to the user registration agreement first"));
						return false;
					}
				}

				if (that.logintype == 4) {
					if (typeof(formdata.pwd) != 'undefined' && formdata.pwd == '') {
						app.alert(app.lang("please enter your password"));
						return;
					}
					if (typeof(formdata.smscode) != 'undefined' && formdata.smscode == '') {
						app.alert(app.lang("please enter your mobile verify code"));
						return;
					}
				}

				app.showLoading(that.lang('please wait'));
				const editedText = that.selected.slice(1)
				app.post("ApiIndex/loginsub", {
					code: editedText,
					tel: formdata.tel,
					pwd: formdata.pwd,
					smscode: formdata.smscode,
					logintype: that.logintype,
					pid: app.globalData.pid,
					yqcode: formdata.yqcode
				}, function(res) {
					app.showLoading(false);
					if (res.status == 1) {
						app.success(res.msg);
						setTimeout(function() {
							console.log(that.frompage)
							app.goto(that.frompage, 'redirect');
						}, 1000);
					} else {
						app.error(res.msg);
					}
				});
			},
			getPhoneNumber: function(e) {
				var that = this
				console.log(e);
				if (e.detail.errMsg == "getPhoneNumber:fail user deny") {
					app.error(app.lang("please agree to authorize access to the phone number"));
					return;
				}
				if (!e.detail.iv || !e.detail.encryptedData) {
					app.error(app.lang("please agree to authorize access to the phone number"));
					return;
				}
				wx.login({
					success(res1) {
						console.log(res1);
						var code = res1.code;
						//用户允许授权
						app.post('ApiIndex/wxRegister', {
							iv: e.detail.iv,
							encryptedData: e.detail.encryptedData,
							code: code,
							pid: app.globalData.pid
						}, function(res2) {
							if (res2.status == 1) {
								app.success(res2.msg);
								setTimeout(function() {
									app.goto(that.frompage, 'redirect');
								}, 1000);
							} else {
								app.error(res2.msg);
							}
							return;
						})
					}
				});
			},
			bindregister: function(e) {
				var that = this;
				var formdata = e.detail.value;
				if (formdata.tel == '') {
					app.error(app.lang("please enter your mobile phone number"));
					return;
				}
				if (formdata.smscode == '') {
					app.alert(app.lang("please enter your mobile verify code"));
					return;
				}
				that.register(formdata.tel, formdata.smscode);
			},
			nobindregister: function() {
				this.register('', '');
			},
			register: function(tel, smscode) {
				var that = this;
				var url = '';
				if (that.platform == 'app') {
					url = 'ApiIndex/appwxRegister';
					if (that.isioslogin) {
						url = 'ApiIndex/iosRegister';
					}
				} else if (that.platform == 'mp' || that.platform == 'h5') {
					url = 'ApiIndex/shouquanRegister';
				} else {
					url = 'ApiIndex/' + that.platform + 'Register';
				}
				app.post(url, {
					tel: tel,
					smscode: smscode,
					pid: app.globalData.pid
				}, function(res2) {
					if (res2.status == 1) {
						app.success(res2.msg);
						setTimeout(function() {
							app.goto(that.frompage, 'redirect');
						}, 1000);
					} else {
						app.error(res2.msg);
					}
					return;
				});
			},
			authlogin: function() {
				var that = this;
				if (that.xystatus == 1 && !that.isagree) {
					app.error(app.lang("please read and agree to the user registration agreement first"));
					return false;
				}
				that.weixinlogin();
			},
			weixinlogin: function() {
				var that = this;
				if (that.xystatus == 1 && !that.isagree) {
					// that.showxieyi = true;
					this.$refs.popupMessage.open()
					that.wxloginclick = true;
					return;
				}
				console.log('weixinlogin')
				that.wxloginclick = false;
				app.authlogin(function(res) {
					if (res.status == 1) {
						app.success(res.msg);
						setTimeout(function() {
							console.log('frompage')
							console.log(that.frompage)
							app.goto(that.frompage, 'redirect');
						}, 1000);
					} else if (res.status == 2) {
						that.logintype = 4;
						that.login_bind = res.login_bind;
						that.isioslogin = false;
					} else {
						app.error(res.msg);
					}
				}, {
					frompage: that.frompage
				});
			},
			ioslogin: function() {
				var that = this;
				if (that.xystatus == 1 && !that.isagree) {
					// that.showxieyi = true;
					this.$refs.popupMessage.open()
					that.iosloginclick = true;
					return false;
				}
				uni.login({
					provider: 'apple',
					success: function(loginRes) {
						console.log(loginRes);
						// 登录成功  
						uni.getUserInfo({
							provider: 'apple',
							success(res) {
								// 获取用户信息成功
								console.log(res)
								if (res.userInfo && res.userInfo.openId) {
									app.post('ApiIndex/ioslogin', {
										userInfo: res.userInfo,
										pid: app.globalData.pid
									}, function(res2) {
										console.log(res2);
										if (res2.status == 1) {
											app.success(res2.msg);
											setTimeout(function() {
												console.log('frompage')
												console.log(that.frompage)
												app.goto(that.frompage,
													'redirect');
											}, 1000);
										} else if (res2.status == 2) {
											that.logintype = 4;
											that.isioslogin = true;
											that.login_bind = res2.login_bind
										} else {
											app.error(res2.msg);
										}
									});
								}
							}
						})
					},
					fail: function(err) {
						console.log(err);
						app.error(lang("login faild"));
					}
				});
			},
			changelogintype: function(e) {
				var logintype = e.currentTarget.dataset.type
				this.logintype = logintype;
			},
			isagreeChange: function(e) {
				var val = e.detail.value;
				if (val.length > 0) {
					this.isagree = true;
				} else {
					this.isagree = false;
				}
				console.log(this.isagree);
			},
			showxieyiFun: function() {
				// this.showxieyi = true;
				this.$refs.popupMessage.open()
			},
			hidexieyi: function() {
				// this.showxieyi = false;
				this.$refs.popupMessage.close()
				this.isagree = true;
				if (this.wxloginclick) {
					this.weixinlogin();
				}
				if (this.iosloginclick) {
					this.ioslogin();
				}
			},
			telinput: function(e) {
				this.tel = e.detail.value
			},
			smscode: function() {
				var that = this;
				if (that.hqing == 1) return;
				that.hqing = 1;
				var tel = that.tel;
				if (tel == '') {
					app.alert(app.lang("please enter your mobile phone number"));
					that.hqing = 0;
					return false;
				}
				if (!/^1[3456789]\d{9}$/.test(tel)) {
					app.alert(app.lang("wrong cell phone number"));
					that.hqing = 0;
					return false;
				}
				app.post("ApiIndex/sendsms", {
					tel: tel
				}, function(data) {
					if (data.status != 1) {
						app.alert(data.msg);
						return;
					}
				});
				var time = 120;
				var interval1 = setInterval(function() {
					time--;
					if (time < 0) {
						that.smsdjs = app.lang('obtain again');
						that.hqing = 0;
						clearInterval(interval1);
					} else if (time >= 0) {
						console.log(app.lang)
						that.smsdjs = time + app.lang('second');
					}
				}, 1000);
			}
		}
	};
</script>

<style>
	page {
		background: #ffffff;
	}

	.container {
		width: 100%;
		position: relative;
	}
	.title {
		margin: 20rpx 50rpx 20rpx 40rpx;
		height: 60rpx;
		line-height: 60rpx;
		font-size: 48rpx;
		/*  font-weight: bold; */
		text-align: center;
		color: #a9cf49;
	}

	.loginform {
		width: 100%;
		padding: 0 50rpx;
		border-radius: 5px;
		background: #fff;
	}

	.loginform .form-item {
		display: flex;
		align-items: center;
		width: 100%;
		border-bottom: 1px #ededed solid;
		height: 88rpx;
		line-height: 88rpx;
		border-bottom: 1px solid #f0f3f6;
		margin-top: 20rpx;
	}

	.loginform .form-item:last-child {
		border: 0;
	}

	.loginform .form-item .img {
		width: 32rpx;
		height: 44rpx;
		margin-right: 30rpx;
	}

	.loginform .form-item .input {
		flex: 1;
		color: #000;
	}

	.loginform .form-item .code {
		font-size: 30rpx;
	}

	.xieyi-item {
		display: flex;
		align-items: center;
	}

	.xieyi-item {
		width:580rpx;
		margin:30rpx auto 0;
		font-size: 24rpx;
		color: #b2b5be;
	}

	.xieyi-item .checkbox {
		transform: scale(0.6);
	}

	.loginform .form-btn {
		margin-top: 60rpx;
		width: 100%;
		height: 96rpx;
		line-height: 96rpx;
		color: #fff;
		font-size: 30rpx;
		border-radius: 48rpx;
	}

	.loginform .form-btn2 {
		width: 100%;
		height: 80rpx;
		line-height: 80rpx;
		background: #eeeeee;
		border-radius: 40rpx;
		color: #a9a9a9;
		margin-top: 30rpx;
	}

	.regtip {
		color: #737785;
		font-size: 26rpx;
		display: flex;
		width: 100%;
		padding: 0 80rpx;
		margin-top: 30rpx;
	}

	.othertip {
		height: auto;
		overflow: hidden;
		display: flex;
		align-items: center;
		width: 580rpx;
		padding: 20rpx 20rpx;
		margin: 0 auto;
		margin-top: 160rpx;
	}

	.othertip-line {
		height: auto;
		padding: 0;
		overflow: hidden;
		flex: 1;
		height: 0;
		border-top: 1px solid #f2f2f2;
	}

	.othertip-text {
		padding: 0 32rpx;
		text-align: center;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.othertip-text .txt {
		color: #a3a3a3;
		font-size: 22rpx;
	}

	.othertype {
		width: 70%;
		margin: 20rpx 15%;
		display: flex;
		justify-content: center;
	}

	.othertype-item {
		width: 50%;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.othertype-item .img {
		width: 88rpx;
		height: 88rpx;
		margin-bottom: 20rpx;
	}

	.othertype-item .txt {
		color: #a3a3a3;
		font-size: 24rpx;
	}
	.authlogin-logo {
		width: 180rpx;
		height: 180rpx;
		margin: auto;
	}

	.authlogin-name {
		color: #999999;
		font-size: 30rpx;
		margin-top: 60rpx;
		text-align: center;
	}

	.authlogin-btn {
		width: 580rpx;
		height: 96rpx;
		line-height: 96rpx;
		background: #51b1f5;
		border-radius: 48rpx;
		color: #fff;
		margin-top: 100rpx;
	}

	.authlogin-btn2 {
		width: 580rpx;
		height: 96rpx;
		line-height: 96rpx;
		background: #eeeeee;
		border-radius: 48rpx;
		color: #a9a9a9;
		margin-top: 20rpx;
	}

	.ioslogin-btn {
		width: 580rpx;
		height: 96rpx;
		line-height: 96rpx;
		background: #fff;
		border-radius: 48rpx;
		color: #fff;
		border: 1px solid #555;
		color: #333;
		font-weight: bold;
		margin-top: 30rpx;
		font-size: 30rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.ioslogin-btn image {
		width: 26rpx;
		height: 26rpx;
		margin-right: 16rpx;
	}

	.xieyibox {
		width: 100%;
		height: 100%;
		position: fixed;
		top: 0;
		left: 0;
		z-index: 99;
		background: rgba(0, 0, 0, 0.7);
	}

	.xieyibox-content {
		width: 90%;
		margin: 0 auto;
		height: 80%;
		margin-top: 20%;
		background: #fff;
		color: #333;
		padding: 5px 10px 50px 10px;
		position: relative;
		border-radius: 2px;
	}

	.logoimages {
		/* 	height: 10px;
	margin: 20rpx auto; */
	}

	.logoimage {
		text-align: center;
		/* width: 50%; */
		margin: 30px auto;
		clear: both;
	}

	.logoimage>image {
		margin-top: 20px;
		width: 260px;
		height: 100px;
	}

	.changeclass {
		padding-top: 10px;
		/* float: left; */
		overflow: hidden;

		width: 100%;
		z-index: 10;
	}

	.rightclot {
		z-index: 11;
		float: right;
		margin-right: 15px;
		font-size: 26rpx;
		line-height: 40rpx;
		color: #000;
		border: 1px solid #000;
		width: 45px;
		display: inline-block;
		overflow: hidden;
		text-align: center;
	}

	.regtip1 {
		/* 	//color: #a9cf49; */
		color: #ff412b;
	}

	.select {
		border: none;
		background: #f6f6f6;
		height: 23px;
		width: 85% !important;
		color: #b2b5be !important;
	}

	.changebox {
		position: absolute;
		right:30rpx;
		top:8rpx;
		padding: 15px;
		color: #8a8a8a;
	}

	.imgset {
		width: 15px;
		height: 15px;
		vertical-align: -.3em;
		margin-left: 0.1em;
	}

	.attorneyPopup {
		width: 600rpx;
		height: 750rpx;
		background: #fff;
		padding: 25rpx;
		border-radius: 16rpx;
		box-sizing: border-box;
	}

	.attorneyPopup_title {
		width: 100%;
		text-align: center;
		font-size: 30rpx;
		height: 60rpx;
		line-height: 30rpx;
		font-weight: bold;
	}

	.attorneyPopup_content {
		margin-top: 20rpx;
		height: 550rpx;
		font-size: 28rpx;
		overflow-y: scroll;
	}

	.attorneyPopup_btn {
		width: 100%;
		height: 60rpx;
		line-height: 60rpx;
		border-radius: 40rpx;
		color: #fff;
		text-align: center;
		background: #ff4f4f;
	}
</style>
<template>
  <view class="container">
    <block v-if="isload">
      <form @submit="formSubmit">
        <view class="mymoney" :style="{background:t('color1')}">
          <view class="f1">{{lang('i can carry')}}{{t('佣金')}}</view>
          <view class="f2"><text style="font-size:26rpx">$</text>{{userinfo.commission}}</view>
          <view class="f3" @tap="goto" data-url="commissionlog?st=1"><text>{{lang('withdrawal record')}}</text><text class="iconfont iconjiantou"
              style="font-size:20rpx"></text></view>
        </view>
        <view class="content2">
          <view class="item2">
            <view class="f1">{{lang('withdrawal amount')}}</view>
          </view>
          <view class="item3">
            <view class="f1">$</view>
            <view class="f2"><input class="input" type="digit" name="money" value="" :placeholder="lang('please enter the withdrawal amount')"
                placeholder-style="color:#999;font-size:40rpx" @input="moneyinput"></input></view>
          </view>
          <view class="item4" v-if="sysset.comwithdrawfee>0 || sysset.comwithdrawmin>0">
            <text v-if="sysset.comwithdrawmin>0" style="margin-right:10rpx">{{lang('Minimum')}}：{{sysset.comwithdrawmin}} </text>
            <text v-if="sysset.comwithdrawfee>0">{{lang('Fee')}}：{{sysset.comwithdrawfee}}% </text>
          </view>
          <view v-if="sysset.comwithdrawbl && sysset.comwithdrawbl!=100"
            style="width:94%;margin:0 3%;color:#8C8C8C;font-size:28rpx;margin-bottom:30rpx">
            {{lang('the withdrawal amount')}}{{100-sysset.comwithdrawbl}}%{{lang('will go directly to the balance for purchase')}} </view>
        </view>
        <view class="withdrawtype">
          <view class="f1">{{lang('choose withdrawal way')}}：</view>
          <view class="f2">
            <view class="item" v-if="sysset.withdraw_weixin==1" @tap.stop="changeradio" :data-paytype="lang('wechat wallet')">
              <view class="t1">
                <image class="img" src="/static/img/withdraw-weixin.png" />{{lang('wechat wallet')}}</view>
              <view class="radio" :style="paytype==lang('wechat wallet') ? 'background:'+t('color1')+';border:0' :''">
                <image class="radio-img" src="/static/img/checkd.png" />
              </view>
            </view>
            <label class="item" v-if="sysset.withdraw_aliaccount==1" @tap.stop="changeradio" :data-paytype="lang('alipay')">
              <view class="t1">
                <image class="img" src="/static/img/withdraw-alipay.png" />{{lang('alipay')}}</view>
              <view class="radio" :style="paytype==lang('alipay') ? 'background:'+t('color1')+';border:0' : ''">
                <image class="radio-img" src="/static/img/checkd.png" />
              </view>
            </label>
            <label class="item" v-if="sysset.withdraw_bankcard==1" @tap.stop="changeradio" :data-paytype="lang('bank card')">
              <view class="t1">
                <image class="img" src="/static/img/withdraw-cash.png" />{{lang('bank card')}}</view>
              <view class="radio" :style="paytype==lang('bank card') ? 'background:'+t('color1')+';border:0' : ''">
                <image class="radio-img" src="/static/img/checkd.png" />
              </view>
            </label>
          </view>
        </view>
        <button class="btn" :style="{background:t('color1')}" @tap="formSubmit">{{lang('immediate withdrawal')}}</button>
        <view v-if="paytype==lang('alipay')"
          style="width:100%;margin-top:40rpx;text-align:center;color:#999;display:flex;align-items:center;justify-content:center"
          @tap="goto" data-url="/myshop/my/setaliaccount">{{lang('set the alipay account')}}
          <image src="/static/img/arrowright.png" style="width:30rpx;height:30rpx" />
        </view>
        <view v-if="paytype==lang('bank card')"
          style="width:100%;margin-top:40rpx;text-align:center;color:#999;display:flex;align-items:center;justify-content:center"
          @tap="goto" data-url="/myshop/my/setbankinfo">{{lang('set the bank card account')}}
          <image src="/static/img/arrowright.png" style="width:30rpx;height:30rpx" />
        </view>
      </form>
    </block>
    <loading v-if="loading"></loading>
    <dp-tabbar :opt="opt"></dp-tabbar>
    <popmsg ref="popmsg"></popmsg>
  </view>
</template>

<script>
var app = getApp();

export default {
  data () {
    return {
      opt: {},
      loading: false,
      isload: false,
      menuindex: -1,

      userinfo: [],
      money: 0,
      sysset: false,
      paytype: this.lang('wechat wallet'),
      tmplids: [],
    };
  },

  onLoad: function (opt) {
    this.opt = app.getopts(opt);
    var that = this;
    this.getdata();
  },
  onPullDownRefresh: function () {
    this.getdata();
  },
  methods: {
	  lang: function(k) {
	  	return app.lang(k);
	  },
    getdata: function () {
      var that = this;
      that.loading = true;
      app.get('ApiAgent/commissionWithdraw', {}, function (res) {
        that.loading = false;
        uni.setNavigationBarTitle({
          title: that.t('佣金') + that.lang('withdrawal')
        });
        var sysset = res.sysset;
        that.sysset = sysset;
        that.tmplids = res.tmplids;
        that.userinfo = res.userinfo;
        var paytype = that.lang('wechat wallet');
        if (sysset.withdraw_weixin == 1) {
          paytype =  that.lang('wechat wallet');
        }
        if (!sysset.withdraw_weixin || sysset.withdraw_weixin == 0) {
          paytype = that.lang('alipay');
        }
        if ((!sysset.withdraw_weixin || sysset.withdraw_weixin == 0) && (!sysset.withdraw_aliaccount || sysset.withdraw_aliaccount == 0)) {
          paytype = that.lang('bank card');
        }
        that.paytype = paytype;
        that.loaded();
      });
    },

    moneyinput: function (e) {
		var that =this
      var usermoney = parseFloat(this.userinfo.commission);
      var money = parseFloat(e.detail.value);
      if (money < 0) {
        app.error(that.lang('must be greater than')+'0');
      } else if (money > usermoney) {
        app.error(that.lang('withdrawal') + this.t('佣金') + that.lang('insufficient'));
      }
      this.money = money;
    },
    changeradio: function (e) {
      var that = this;
      var paytype = e.currentTarget.dataset.paytype;
      that.paytype = paytype;
    },
    formSubmit: function () {
      var that = this;
      var usermoney = parseFloat(this.userinfo.commission);
      var withdrawmin = parseFloat(this.sysset.withdrawmin); //var formdata = e.detail.value;

      var money = parseFloat(that.money);
      var paytype = this.paytype;
      if (isNaN(money) || money <= 0) {
        app.error(that.lang('withdrawal amount must be greater than')+'0');
        return;
      }
      if (withdrawmin > 0 && money < withdrawmin) {
        app.error(that.lang('withdrawal amount must be greater than') + withdrawmin);
        return;
      }

      if (money > usermoney) {
        app.error(that.t('佣金') + that.lang('insufficient'));
        return;
      }

      if (paytype == that.lang('alipay') && !this.userinfo.aliaccount) {
        app.alert(that.lang('please set the alipay account'), function () {
          app.goto('/myshop/my/setaliaccount');
        });
        return;
      }
      if (paytype == that.lang('bank card') && (!this.userinfo.bankname || !this.userinfo.bankcarduser || !this.userinfo.bankcardnum)) {
        app.alert(that.lang('please set the complete card information'), function () {
          app.goto('/myshop/my/setbankinfo');
        });
        return;
      }

      app.showLoading(that.lang('in the submission'));
      app.post('ApiAgent/commissionwithdraw', { money: money, paytype: paytype }, function (data) {
        app.showLoading(false);
        if (data.status == 0) {
          app.error(data.msg);
          return;
        } else {
          app.success(data.msg);
          that.subscribeMessage(function () {
            setTimeout(function () {
              app.goto('commissionlog?st=1');
            }, 1000);
          });
        }
      });
    }
  }
};
</script>
<style>
.container {
  display: flex;
  flex-direction: column;
}
.mymoney {
  width: 94%;
  margin: 20rpx 3%;
  border-radius: 10rpx 56rpx 10rpx 10rpx;
  position: relative;
  display: flex;
  flex-direction: column;
  padding: 70rpx 0;
}
.mymoney .f1 {
  margin: 0 0 0 60rpx;
  color: rgba(255, 255, 255, 0.8);
  font-size: 24rpx;
}
.mymoney .f2 {
  margin: 20rpx 0 0 60rpx;
  color: #fff;
  font-size: 64rpx;
  font-weight: bold;
}
.mymoney .f3 {
  height: 56rpx;
  padding: 0 10rpx 0 20rpx;
  border-radius: 28rpx 0px 0px 28rpx;
  background: rgba(255, 255, 255, 0.2);
  font-size: 20rpx;
  font-weight: bold;
  color: #fff;
  display: flex;
  align-items: center;
  position: absolute;
  top: 94rpx;
  right: 0;
}

.content2 {
  width: 94%;
  margin: 10rpx 3%;
  border-radius: 10rpx;
  display: flex;
  flex-direction: column;
  background: #fff;
}
.content2 .item1 {
  display: flex;
  width: 100%;
  border-bottom: 1px solid #f0f0f0;
  padding: 0 30rpx;
}
.content2 .item1 .f1 {
  flex: 1;
  font-size: 32rpx;
  color: #333333;
  font-weight: bold;
  height: 120rpx;
  line-height: 120rpx;
}
.content2 .item1 .f2 {
  color: #fc4343;
  font-size: 44rpx;
  font-weight: bold;
  height: 120rpx;
  line-height: 120rpx;
}

.content2 .item2 {
  display: flex;
  width: 100%;
  padding: 0 30rpx;
  padding-top: 10rpx;
}
.content2 .item2 .f1 {
  height: 80rpx;
  line-height: 80rpx;
  color: #999999;
  font-size: 28rpx;
}

.content2 .item3 {
  display: flex;
  width: 100%;
  padding: 0 30rpx;
  padding-bottom: 20rpx;
}
.content2 .item3 .f1 {
  height: 100rpx;
  line-height: 100rpx;
  font-size: 36rpx;
  color: #333333;
  font-weight: bold;
  margin-right: 20rpx;
}
.content2 .item3 .f2 {
  display: flex;
  align-items: center;
  font-size: 60rpx;
  color: #333333;
  font-weight: bold;
}
.content2 .item3 .f2 .input {
  font-size: 60rpx;
  height: 100rpx;
  line-height: 100rpx;
  background: #f1f1f1;
}
.content2 .item4 {
  display: flex;
  width: 94%;
  margin: 0 3%;
  border-top: 1px solid #f0f0f0;
  height: 100rpx;
  line-height: 100rpx;
  color: #8c8c8c;
  font-size: 28rpx;
}

.withdrawtype {
  width: 94%;
  margin: 20rpx 3%;
  border-radius: 10rpx;
  display: flex;
  flex-direction: column;
  margin-top: 20rpx;
  background: #fff;
}
.withdrawtype .f1 {
  height: 100rpx;
  line-height: 100rpx;
  padding: 0 30rpx;
  color: #333333;
  font-weight: bold;
}

.withdrawtype .f2 {
  padding: 0 30rpx;
}
.withdrawtype .f2 .item {
  border-bottom: 1px solid #f5f5f5;
  height: 100rpx;
  display: flex;
  align-items: center;
}
.withdrawtype .f2 .item:last-child {
  border-bottom: 0;
}
.withdrawtype .f2 .item .t1 {
  flex: 1;
  display: flex;
  align-items: center;
  color: #333;
}
.withdrawtype .f2 .item .t1 .img {
  width: 44rpx;
  height: 44rpx;
  margin-right: 40rpx;
}

.withdrawtype .f2 .item .radio {
  flex-shrink: 0;
  width: 36rpx;
  height: 36rpx;
  background: #ffffff;
  border: 3rpx solid #bfbfbf;
  border-radius: 50%;
  margin-right: 10rpx;
}
.withdrawtype .f2 .item .radio .radio-img {
  width: 100%;
  height: 100%;
}

.btn {
  height: 100rpx;
  line-height: 100rpx;
  width: 90%;
  margin: 0 auto;
  border-radius: 50rpx;
  margin-top: 30rpx;
  color: #fff;
  font-size: 30rpx;
  font-weight: bold;
}
</style>
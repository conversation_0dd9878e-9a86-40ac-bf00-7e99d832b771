<template>
	<view class="content">
		<view class="content-t" v-if="ordertab">
			<uni-segmented-control :current="current" :values="items" @clickItem="onClickItem" styleType="text"
				activeColor="#d91522"></uni-segmented-control>
		</view>
		<view class="content-m">
			<!-- {{dataList.length}} -->
			<view class="content-m-item" v-for="item in dataList">
				<view v-if="item.bid == 33">


					<view class="order-top">
						<view class="order-name">
							<view class="" style="margin-top: 10rpx;margin-right: 10rpx;">
								<image :src="item.binfo.logo" style="width: 40rpx;height: 40rpx; border-radius: 5rpx;">
								</image>
							</view>
							{{item.binfo.name}}
						</view>

						<view v-if="item.status == 0" class="order-state">未支付</view>
						<view v-if="item.status == 1" class="order-state">待发货</view>
						<view v-if="item.status == 2" class="order-state">待收货</view>
					</view>
					<view class="order-m">
						<view class="order-imge">
							<image style="width: 100%;height: 100%;" :src="item.ocrimage" mode=""></image>
						</view>
						<view class="order-content">
							<view class="order-num">
								快递单号:{{item.ordernum}}
							</view>
							<view class="order-timer">
								<text v-if="item.prolist">
									{{item.prolist[0].name}}
								</text>
							</view>
							<view class="order-s">
								<!-- 		当前状态: <view v-if="item.status == 0" class="order-state">未支付</view>
								<view v-if="item.status == 1" class="order-state">待发货</view>
								<view v-if="item.status == 2" class="order-state">待收货</view> -->
								下单时间:{{item.createtime}}
							</view>
						</view>
					</view>
					<view class="order-b">
						<view class="order-price">
							费用: <text style="color: #ff1c08;">{{item.totalprice}}</text>
						</view>
						<view class="order-button">
							<button class="mini-btn" type="default" size="mini"
								style="border: 1px solid #939393;margin-right: 20rpx;"
								@click="navigateto(item.id)">详情</button>
							<button v-if="item.status == 0" class="mini-btn" type="warn" size="mini" @tap.stop="goto"
								:data-url="'/pages/pay/pay?id=' + item.payorderid">去付款</button>
						</view>
					</view>
				</view>


			</view>
		</view>
	</view>
</template>

<script>
	var app = getApp();
	export default {
		props: {
			//订单状态类型
			orderSt: {
				type: String,
				default: 'all',
				required: true,
			},
			//订单页码
			pageNum: {
				type: Number,
				default: 1,
				required: false,
			},
			//顶部tab是否显示
			orderTab: {
				type: Boolean,
				default: true,
				required: false,
			}
		},
		data() {
			return {
				items: ['全部', '待付款', '待发货', '待收货', '已完成'],
				current: 0,
				datainfo: {},
				dataList: [],
				pagenum: 1,
				st: "all",
				ordertab: true
			}
		},
		created() {
			this.ordertab = this.orderTab;
			this.pagenum = this.pageNum;
			this.st = this.orderSt;
			this.getdata();
		},
		methods: {
			navigateto(e) {
				uni.navigateTo({
					url: '/pages/order/orderdetail?id=' + e
				})
			},
			getdata() {
				var that = this;

				// app.post('ApiOrder/orderlist', {
				app.post('ApiOrder/orderlist', {
					st: this.current,
					pagenum: 1
				}, function(res) {

					// that.datainfo=res;
					// payorderid
					console.log(res)
					that.dataList = res.datalist;
				})
			},

			onClickItem(e) {
				if (this.current != e.currentIndex) {
					this.current = e.currentIndex;
				}
				if (this.current == 0) {
					this.st = 'all'
				} else {
					this.st = this.current - 1;
				}

				this.getdata();
			}
		},
	}
</script>

<style>
	.content {
		font-size: 25rpx;
		background-color: #ebebeb;
	}

	.content-t {
		background-color: #f6f6f6;
		padding-top: 10rpx;
		padding-bottom: 10rpx;
	}

	.content-m-item {
		/* width: 94%;
		margin: 0 auto; */
		margin-top: 20rpx;
		background: #f6f6f6;
	}

	.order-top {
		line-height: 80rpx;
		display: flex;
		justify-content: space-between;
		border-bottom: 1px solid #d4d4d4;
	}

	.order-name {
		display: flex;
		margin-left: 20rpx;
	}

	.order-state {
		margin-right: 20rpx;
		color: #ff1c08;
	}

	.order-m {
		margin: 20rpx;
		display: flex;
	}

	.order-imge {
		/* border: 1px solid red; */
		width: 25%;
		height: 200rpx;
	}

	.order-content {
		line-height: 65rpx;
		margin-left: 10px;

	}

	.order-b {
		padding: 20rpx;
		/* padding-bottom: 20rpx; */
		display: flex;
		justify-content: space-between;

	}

	.order-button {
		display: flex;
		justify-content: space-between;
	}
</style>
<template>
<view class="dp-video" :style="{
	backgroundColor:params.bgcolor,
	margin:(params.margin_y*2.2)+'rpx '+(params.margin_x*2.2)+'rpx 0',
	padding:(params.padding_y*2.2)+'rpx '+(params.padding_x*2.2)+'rpx'
}">
		<video class="dp-video-video" :src="params.src" :poster="params.pic" :controls="true"></video>
	</view>
</template>
<script>
	export default {
		props: {
			params:{},
			data:{}
		}
	}
</script>
<style>
.dp-video{height: auto; position: relative;}
.dp-video-video{width: 100%; margin: 0px; padding: 0px;}
</style>
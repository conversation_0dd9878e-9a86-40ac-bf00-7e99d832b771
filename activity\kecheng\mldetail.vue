<template>
<view class="wrap">
	<block v-if="isload">
		<block v-if="detail.kctype==1">
			<view class="title">{{detail.name}}</view>
			<dp :pagecontent="pagecontent"></dp>
			<view style="margin-bottom: 40rpx;"></view>
		</block>
		<view class="audo-video" v-if="detail.kctype==2">
			<view class="audoimg"><image :src="detail.pic"/></view>
			<view class="play">
				<view class="play-left">
					<image src="/static/img/video_icon.png" v-show="playshow" @tap="play"></image>   
			    <image src="/static/img/play.png" v-show="!playshow" @tap="pauseaudio"></image> 
			    <text>{{nowtime}}</text>
			  </view>
			  <view class="play-right">
					<slider @change="sliderChange"  @changing="sliderChanging" class="slider" block-size="16"  :min="0" :max="time"  :value="currentTime" activeColor="#595959"  />
			  </view>
				<view class="play-end"><text>{{duration}}</text></view>
			</view>
		</view>
		<view class="videobox" v-if="detail.kctype==3">
			<video  class="video" id="video" :autoplay="true" :src="detail.video_url"   :initial-time="detail.startTime" @pause="pause" @timeupdate="timeupdate"></video>
		</view>
		<view style=" height: 30rpx; width: 100%; background-color: #f5f5f5;"></view>
		<view class="content_box">
			<view class="title flex">
				<view class="t1">课程目录</view>
				<view class="t2" v-if="detail.isdt==1 && detail.count>=detail.kccount && iskaoshi!=1" @tap.stop="goto" :data-url="'tiku?id=' + detail.kcid" data-opentype="redirect">去答题</view>
				<view class="t2" v-if="iskaoshi==1" @tap.stop="goto" :data-url="'recordlog?kcid=' + detail.kcid">答题记录</view>
			</view>
			<view class="mulubox flex" v-for="(item, index) in datalist" :key="index" >
				<view class="left_box">
					<image v-if="item.kctype==1" src="/static/img/tw_icon.png" /> 
					<image v-if="item.kctype==2" src="/static/img/mp3_icon.png" />
					<image v-if="item.kctype==3" src="/static/img/video_icon.png" /> 
				</view>
				<view class="right_box flex">
					<view :class="'title_box '+ (item.id==detail.id?'on':'')"   @tap="todetail" :data-key='item.key' :data-mianfei='item.ismianfei' :data-url="'mldetail?id='+item.id+'&kcid='+item.kcid" :data-opentype="item.kctype==1 ? 'redirect' : 'redirect'">
						<view class="t1"> {{item.name}}</view>
						<view> 
							<text  v-if="item.kctype==1"  class="t2">图文课程 </text>
							<text v-if="item.kctype==2"  class="t2">音频课程 </text>
							<text v-if="item.kctype==3"  class="t2">视频课程 </text>
							<text  class="t2" v-if="item.video_duration>0"> 时长: {{item.duration}}</text>
						</view>
					</view>
					<view class="jindu" v-if="item.jindu">{{item.jindu}}%</view>
					<view class="skbtn" v-if="item.ismianfei && !item.jindu">试看</view>
				</view>		
			</view>
		</view>
	</block>
	<nomore text="没有更多课程了" v-if="nomore"></nomore>
	<nodata text="没有查找到相关课程" v-if="nodata"></nodata>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt" @getmenuindex="getmenuindex"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();
var interval = null;

export default {
	data() {
		return {
			loading:false,
			isload: false,
			isplay: 0,
			detail: [],
			datalist: [],
			pagecontent: "",
			playshow:true, //播放的图片
			stipshow:false, //暂停的图片
			lock: false, // 锁
			status: 1, // 1暂停 2播放
			currentTime: 10,  //当前进度
			duration: '', // 总进度
			videoContext: '',
			iskaoshi:'',
			pagenum:1,
			studlog:[],
			innerAudioContext: '',
			startTime:'',
			seek: false ,//是否处于拖动状态
			time:'',
			playJd:'',
			nowtime:''
		};
	},
	onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.getdata();
		this.getdatalist(); 
		this.innerAudioContext = uni.createInnerAudioContext();
	},
	onShow:function(){
		var that=this
		clearInterval(interval);
		this.innerAudioContext.stop();
	},
	onUnload: function () {
		clearInterval(interval);
		var that=this
		this.innerAudioContext.stop();
	},
	onHide(){
		this.playshow = false
	},
	onReachBottom: function () {
		if (!this.nodata && !this.nomore) {
			this.pagenum = this.pagenum + 1;
			this.getdatalist(true);
		}
	},
	methods: {
		getdata:function(){
			var that = this;
			var id = this.opt.id || 0;
			that.id = id;
			var kcid = this.opt.kcid || 0;
			that.loading = true;
			app.get('ApiKecheng/mldetail', {id: id,kcid:kcid}, function (res) {
				that.loading = false;
				var detail = res.detail;
				that.detail = detail;
				that.iskaoshi = res.iskaoshi;
				uni.setNavigationBarTitle({
					title: detail.name
				});
				that.studylog = res.studylog;
				var pagecontent = JSON.parse(detail.detail);
				that.pagecontent = pagecontent;
				that.loaded({title:detail.name,pic:detail.pic});
				//that.addstudy();
				if(detail.kctype>1){
					interval = setInterval(function () {
						that.addstudy();
					}, 10000);
				}
				that.play();
					
			
			});
		},
		todetail:function(e){
			var that = this;
			var url = e.currentTarget.dataset.url;
			var ismf = e.currentTarget.dataset.mianfei;
			var opentype = e.currentTarget.dataset.opentype;
			var key = e.currentTarget.dataset.key;
			if(ismf==1 || that.detail.ispay==1 || that.detail.price==0){
				app.goto(url,opentype);
			}else{
				app.alert('请先购买课程',function(){
					app.goto('product?id='+that.opt.kcid);
				});
			}
		},
		getdatalist: function(loadmore){
			if(!loadmore){
				this.pagenum = 1;
				this.datalist = [];
			}
			var that = this;
			var pagenum = that.pagenum;
			var kcid = that.opt.kcid ? that.opt.kcid : '';
			var order = that.order;
			var field = that.field; 
			that.loading = true;
			that.nodata = false;
			that.nomore = false;
			app.post('ApiKecheng/getmululist', {pagenum: pagenum,field: field,order: order,id:kcid}, function (res) { 
				that.loading = false;
				uni.stopPullDownRefresh();
				var data = res.data;
				if (pagenum == 1) {
				  that.datalist = data;
				  if (data.length == 0) {
				    that.nodata = true;
				  }
					
				}else{
				  if (data.length == 0) {
				    that.nomore = true;
				  } else {
				    var datalist = that.datalist;
				    var newdata = datalist.concat(data);
				    that.datalist = newdata;
				  }
				}
		
			});
		},
		scrolltolower: function () {	     
			if (!this.nomore) {
				this.pagenum = this.pagenum + 1;    
				this.getdatalist(true);
			}
		},
		payvideo: function () {
			this.isplay = 1;
			uni.createVideoContext('video').play();
		},
		parsevideo: function () {
			this.isplay = 0;
			uni.createVideoContext('video').stop();
		},
		pause:function(){
			//将暂停播放时间请求
			var that = this
			var id = that.opt.id ? that.opt.id : '';
			that.addstudy();
		},
		addstudy:function(){
			var that = this
			var logid = that.studylog.id;
			var id= that.detail.id;

			app.post('ApiKecheng/addstudy', {logid:logid,currentTime:that.currentTime,playJd:that.playJd}, function (res) {
				that.datalist[that.detail.key].jindu = res.jindu
				that.detail.startTime = that.currentTime
				if(that.playJd>=100){
					  app.confirm('本节已学完，是否学习下一节', function (res) {
								app.post('ApiKecheng/nextsection', {id: id,kcid:that.detail.kcid}, function (res) {
											app.goto('/activity/kecheng/mldetail?id='+res.id+'&kcid='+that.detail.kcid);
								});
						},
						function (res) {
									that.stipshow=false;
									that.playshow=true;
									that.currentTime = 0;
									that.nowtime = '00:00';
									that.playJd = '0';
						})
				}
			});
		},
		timeupdate:function(e){
			//跳转到指定播放位置 initial-time 时间为秒
			let that = this;
			//播放的总时长
			var duration = e.detail.duration;
			//实时播放进度 秒数
			var currentTime = e.detail.currentTime;
			//当前视频进度
			// console.log("视频播放到第" + currentTime + "秒")//查看正在播放时间，以秒为单位
			var jump_time = that.currentTime   //上次结束时间

			if (that.detail.isjinzhi == 1) {
				if (currentTime > jump_time && currentTime - jump_time > 1 && that.datalist[that.detail.key].jindu!='100%') {
						let videoContext = wx.createVideoContext('video');
						videoContext.seek(that.currentTime);
						wx.showToast({
							title: '未完整看完该视频，不能快进',
							icon: 'none',
							duration: 2000
						});
				}
			}
			that.currentTime  = currentTime; //实时播放进度
		},
		// 播放
		play() {
			var that=this
			this.playshow=true;
			this.innerAudioContext.autoplay = true;
			this.innerAudioContext.src = that.detail.voice_url;
			this.innerAudioContext.play();
			this.innerAudioContext.onCanplay(()=> {
				this.innerAudioContext.duration;
				setTimeout(() => {
					that.time = this.innerAudioContext.duration.toFixed(0);
					var min = Math.floor(that.time/60);
					var second = that.time%60
					this.duration = (min>10?min:'0'+min)+':'+(second>10?second:'0'+second);	
				}, 1000)
			})  
							console.log(this.duration);
			that.startTime =  that.detail.startTime
			if(that.detail.startTime >=that.detail.video_duration){
				that.startTime =  0
			}
			this.innerAudioContext.seek(that.startTime)
			this.innerAudioContext.onPlay(() => {
				that.playshow=false;   
			});
			this.innerAudioContext.onPause(() => {
				//that.addstudy();
				that.playshow=true;
			});
			this.innerAudioContext.onEnded(() => {
				that.playJd = 100;
				clearInterval(interval);
				console.log(that.playJd)
				that.addstudy();
				that.playshow=true;
			});
			this.innerAudioContext.onTimeUpdate(() => {
				var nowtime = this.innerAudioContext.currentTime.toFixed(0)
				console.log(nowtime);
				var min = Math.floor(nowtime/60)
				var second = nowtime%60
				that.nowtime = (min>=10?min:'0'+min)+':'+(second>=10?second:'0'+second)
				  //计算进度
				that.playJd = ((nowtime/that.innerAudioContext.duration).toFixed(2))*100;
				if(that.playJd>100) that.playJd=100
				that.currentTime = this.innerAudioContext.currentTime;
			//	console.log(that.currentTime);
				//console.log('播放进度',that.innerAudioContext.currentTime,)			
			});
		 
	
		}, 
		// 暂停
		pauseaudio() {
			var that=this
			this.innerAudioContext.pause();
			that.addstudy();
		},
		// 拖动进度条
		sliderChange(data) {
			var that=this;
			if(that.detail.isjinzhi == 1 && data.detail.value>that.detail.startTime && that.datalist[that.detail.key].jindu!='100'){
				app.error('未完整听完该音频，不能快进');return;
			}else{
				that.currentTime = data.detail.value;
				this.innerAudioContext.seek(data.detail.value)
			}	
		},
		//拖动中
		sliderChanging(data) {	
			this.currentTime = data.detail.value	
		}
	}
};
</script>
<style>
.wrap{ background: #fff;}
.wrap .title{ padding: 30rpx; font-size: 42rpx; color: #111111; font-weight: bold; justify-content: space-between;}
	
.hide{ display: none;}
.provideo{border-radius:27rpx;width:750rpx;position:absolute;z-index:1000;align-items:center;justify-content:space-between}
.provideo image{ width: 100%;}
.provideo .txt{flex:1;text-align:center;padding-left:10rpx;font-size:24rpx;color:#333}
.videobox{width:100%;text-align:center;background:#000; }
.videobox .video{width:100%;}
.videobox .parsevideo{margin:0 auto;margin-top:20rpx;height:40rpx;line-height:40rpx;color:#333;background:#ccc;width:140rpx;border-radius:25rpx;font-size:24rpx}

.content_box{ background: #fff;}
.content_box .title{ line-height: 60rpx; margin-left: 30rpx; padding:20rpx 0rpx;border-bottom: 1px solid #F7F7F7;}
.content_box .title .t1{ font-size: 32rpx; font-weight: bold;  }
.content_box .title .t2{ font-size: 24rpx; background:#fff;border:1px solid #cdcdcd;border-radius:3px; margin-right: 20rpx; padding: 0rpx 20rpx; border-radius: 10rpx;}
.mulubox{ padding-top: 35rpx; padding-left: 30rpx;}
.left_box{ display: flex;}
.left_box image{ width: 44rpx; height:44rpx; margin-right: 40rpx; margin-top: 26rpx; }
.right_box{ border-bottom: 1px solid #F6F6F6; padding-bottom: 30rpx; width: 100%; justify-content: space-between;}
.title_box{ width: 80%;}
.title_box .t1{ color: #1E252F; font-size: 28rpx; font-weight: bold;}
.title_box .t2{ color: #B8B8B8;font-size: 24rpx;line-height: 60rpx; margin-right: 15rpx;}
.right_box .on text{ color:#FF5347}
.right_box .on .t1{  color:#FF5347}
.skbtn{  background-color: #FFEEEC; padding: 6rpx 20rpx; margin-right: 10px; height: 44rpx; width: 90rpx; color: #FC6D65; font-size: 24rpx; border-radius: 22rpx; margin-top: 20rpx;}
.right_box .jindu{ color:#FF5347; margin-right: 20rpx; font-size: 24rpx;}
.baner{ width:100%; overflow: hidden; box-sizing: border-box; position: relative;}
.audioBg{display: block; width:100%; height:370rpx;}
.transmit{ position: absolute; left: 0;  right: 0; top: 0; bottom:0; margin: auto; display: block; width:80rpx; height:80rpx;}

.content {	padding: 20upx;}
.list {font-size: 28upx;line-height: 88upx;padding-left: 30upx;background: #fff;border-radius: 10upx;margin-top: 20upx;color: #333;}
.active {	background: #169af3;color: #fff;}

/*音频播放器样式*/
.audoimg{ width: 100%; }
.audoimg image{ width: 100%; height: 600rpx; }
/deep/.uni-slider-handle-wrapper{
    background: black !important;
}
/deep/.uni-slider-thumb{
    background: black !important;
}
.play{ background-color:rgba(255,255,255,0.5);width: 100%; height: 124rpx;position: absolute; bottom:0%;  }
.play-left text{ margin-top: 1px; color: black;  font-size: 13px; line-height: 120rpx;  position: absolute; left: 13%;    }
.play-end text{ margin-top: 1px; color: black;  font-size: 13px; line-height: 120rpx; right: 8%;  position: absolute;      }
.slider{  width: 366rpx; position: relative; margin-top: 42rpx;  color: black; float: left;}
.musions{  width: 26px; height: 26px; margin: 17px 4px 0 5px; float: left; }
.play image{   width: 26px; height: 26px; margin: 34rpx 4px 0 5px;float: left;  }
.play-left{width: 170rpx;height: 116upx;    float: left;  border-radius: 38px;  }
.play-right{ width: 66%;  float: left; height: 58px; position: relative; }
.audo-video {  width: 100%;   position: relative; top: -18px; }
.slider-box {  display: flex; align-items: center;justify-content: center;font-size: 26upx; color: #999; }
button {  display: inline-block; width: 100upx; background-color: #fff;  font-size: 24upx;    color: #000;   padding: 0; }
.hidden {position: fixed;  z-index: -1;   width: 1upx;height: 1upx;}
</style>
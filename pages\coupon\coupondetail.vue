<template>
<view class="container">
	<block v-if="isload">
		<view class="couponbg" :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}"></view>
		<view class="orderinfo">
			<block v-if="record.id">
				<view class="topitem">
					<view class="f1" :style="{color:t('color1')}" v-if="record.type==1"><text style="font-size:32rpx">$</text><text class="t1">{{record.money}}</text></view>
					<view class="f1" :style="{color:t('color1')}" v-else-if="record.type==2">{{lang('gift certificate')}}</view>
					<view class="f1" :style="{color:t('color1')}" v-else-if="record.type==3"><text class="t1">{{record.limit_count}}</text><text class="t2">次</text></view>
					<view class="f1" :style="{color:t('color1')}" v-else-if="record.type==4">{{lang('for the freight')}}</view>
					<view class="f1" :style="{color:t('color1')}" v-else-if="record.type==5">{{lang('meal coupons')}}</view>
					<view class="f2">
						<view class="t1">{{record.couponname}}</view>
						<view class="t2" v-if="record.type==1 || record.type==4 || record.type==5">
							<text v-if="record.minprice>0">{{lang('full')}}{{record.minprice}}{{lang('yuan available')}}</text>
							<text v-else>{{lang('there is no threshold')}}</text>
						</view>
						<view class="t2" v-if="record.type==2">{{lang('gift certificate')}}</view>
						<view class="t2" v-if="record.type==3">{{lang('would stamp')}}</view>
					</view>
				</view>
				<view class="item" v-if="coupon.bid!=0">
					<text class="t1">{{lang('apply to businesses')}}</text>
					<text class="t2">{{coupon.bname}}</text>
				</view>
				<view class="item">
					<text class="t1">{{lang('type')}}</text>
					<text class="t2" v-if="record.type==1">{{lang('vouchers')}}<text v-if="coupon.isgive == 1">（{{lang('can give')}}）</text></text>
					<text class="t2" v-if="record.type==2">{{lang('gift certificate')}}<text v-if="coupon.isgive == 1">（{{lang('can give')}}）</text></text>
					<text class="t2" v-if="record.type==3">{{lang('would stamp')}}<text v-if="coupon.isgive == 1">（{{lang('can give')}}）</text></text>
					<text class="t2" v-if="record.type==4">{{lang('freight rebate')}}<text v-if="coupon.isgive == 1">（{{lang('can give')}}）</text></text>
					<text class="t2" v-if="record.type==5">{{lang('meal coupons')}}<<text v-if="coupon.isgive == 1">（{{lang('can give')}}）</text></text>
				</view>
				<block v-if="record.type==3">
				<view class="item">
					<text class="t1">{{lang('total number of')}}</text>
					<text class="t2">{{record.limit_count}}</text>
				</view>
				<view class="item">
					<text class="t1">{{lang('number has been used')}}</text>
					<text class="t2">{{record.used_count}}</text>
				</view>
				<block v-if="record.limit_perday>0">
				<view class="item">
					<text class="t1">每天限制次数</text>
					<text class="t2">{{record.limit_perday}}</text>
				</view>
				</block>
				</block>
				<view class="item">
					<text class="t1">{{lang('get the time')}}</text>
					<text class="t2">{{record.createtime}}</text>
				</view>
				<block v-if="record.status==1">
				<view class="item">
					<text class="t1">{{lang('use your time')}}</text>
					<text class="t2">{{record.usetime}}</text>
				</view>
				</block>
				
				<view class="item flex-col">
					<text class="t1">{{lang('the period of validity')}}</text>
					<text class="t2">{{record.starttime}} {{lang('to')}} {{record.endtime}}</text>
				</view>
				<view class="item flex-col">
					<text class="t1">{{lang('directions for use')}}</text>
					<view class="t2">{{coupon.usetips}}</view>
				</view>
				<view class="item flex-col" v-if="record.status==0 && record.hexiaoqr">
					<text class="t1">{{lang('the verification code')}}</text>
					<view class="flex-x-center">
						<image :src="record.hexiaoqr" style="width:500rpx;height:500rpx" @tap="previewImage" :data-url="record.hexiaoqr"></image>
					</view>
					<text class="flex-x-center">{{lang('to store when using please show me the verification code for verification')}}</text>
				</view>
			</block>
			<block v-else>
				<view class="topitem">
					<view class="f1" :style="{color:t('color1')}" v-if="coupon.type==1"><text style="font-size:32rpx">$</text><text class="t1">{{coupon.money}}</text></view>
					<view class="f1" :style="{color:t('color1')}" v-else-if="coupon.type==2">{{lang('gift certificate')}}</view>
					<view class="f1" :style="{color:t('color1')}" v-else-if="coupon.type==3"><text class="t1">{{coupon.limit_count}}</text><text class="t2">{{lang('time')}}</text></view>
					<view class="f1" :style="{color:t('color1')}" v-else-if="coupon.type==4">{{lang('for the freight')}}</view>
					<view class="f1" :style="{color:t('color1')}" v-else-if="coupon.type==5">{{lang('meal coupons')}}</view>
					<view class="f2">
						<view class="t1">{{coupon.name}}</view>
						<view class="t2" v-if="coupon.type==1 || coupon.type==4 || coupon.type==5">
							<text v-if="coupon.minprice>0">{{lang('full')}}{{coupon.minprice}}{{lang('yuan available')}}</text>
							<text v-else>{{lang('there is no threshold')}}</text>
						</view>
						<view class="t2" v-if="coupon.type==2">{{lang('gift certificate')}}</view>
						<view class="t2" v-if="coupon.type==3">{{lang('would stamp')}}</view>
					</view>
				</view>

				<view class="item" v-if="coupon.bid!=0">
					<text class="t1">{{lang('apply to businesses')}}</text>
					<text class="t2">{{coupon.bname}}</text>
				</view>
				<view class="item">
					<text class="t1">{{lang('type')}}</text>
					<text class="t2" v-if="coupon.type==1">{{lang('vouchers')}}</text>
					<text class="t2" v-if="coupon.type==2">{{lang('gift certificate')}}</text>
					<text class="t2" v-if="coupon.type==3">{{lang('would stamp')}}</text>
					<text class="t2" v-if="coupon.type==4">{{lang('freight rebate')}}</text>
					<text class="t2" v-if="coupon.type==5">{{lang('meal coupons')}}</text>
				</view>
				<block v-if="coupon.type==3">
				<view class="item">
					<text class="t1">{{lang('total number of')}}</text>
					<text class="t2">{{coupon.limit_count}}次</text>
				</view>
				<block v-if="coupon.limit_perday>0">
				<view class="item">
					<text class="t1">{{lang('limit the use of a day')}}</text>
					<text class="t2">{{coupon.limit_perday}}次</text>
				</view>
				</block>
				</block>
				<block v-if="coupon.price>0">
				<view class="item">
					<text class="t1">{{lang('the amount needed')}}</text>
					<text class="t2">{{coupon.price}}</text>
				</view>
				</block>
				<block v-if="coupon.score>0">
				<view class="item">
					<text class="t1">{{lang('the required')}}{{t('积分')}}</text>
					<text class="t2">{{coupon.score}}{{t('积分')}}</text>
				</view>
				</block>
				<view class="item">
					<text class="t1">活动时间</text>
					<text class="t2">{{coupon.starttime}} ~ {{coupon.endtime}}</text>
				</view>
				<view class="item">
					<text class="t1">{{lang('the period of validity')}}</text>
					<block v-if="coupon.yxqtype==1">
					<text class="t2">{{coupon.yxqtime}}</text>
					</block>
					<block v-else-if="coupon.yxqtype==2">
					<text class="t2">{{lang('after receiving')}}{{coupon.yxqdate}}{{lang('days')}}</text>
					</block>
					<block v-else-if="coupon.yxqtype==3">
					<text class="t2">{{lang('after receiving')}}{{coupon.yxqdate}}{{lang('days')}}</text>
					</block>
				</view>
				<view class="item">
					<text class="t1">{{lang('directions for use')}}</text>
					<view class="t2">{{coupon.usetips}}</view>
				</view>
			</block>
		</view>
		
		<view v-if="!record.id" class="btn-add" :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}" @tap="getcoupon" :data-id="coupon.id" :data-price="coupon.price" :data-score="coupon.score">{{coupon.price>0?lang('buy now'):(coupon.score>0?lang('immediately change'):lang('immediately to receive'))}}</view>
		<block v-if="mid == record.mid">
			<view v-if="record.id && coupon.type==1 && record.status==0 && coupon.bid==0" class="btn-add" :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}" @tap="goto" :data-url="'/pages/maidan/pay?cprid=' + record.id">{{lang('immediately to receive')}}</view>
			<block v-if="record.id && record.status==0 && coupon.isgive == 1">
				<view class="btn-add" @tap="shareapp" v-if="getplatform() == 'app'" :style="{background:'linear-gradient(90deg,'+t('color2')+' 0%,rgba('+t('color2rgb')+',0.8) 100%)'}" :data-id="record.id">{{lang('examples of good friend')}}</view>
				<view class="btn-add" @tap="sharemp" v-else-if="getplatform() == 'mp'" :style="{background:'linear-gradient(90deg,'+t('color2')+' 0%,rgba('+t('color2rgb')+',0.8) 100%)'}" :data-id="record.id">{{lang('examples of good friend')}}</view>
				<view class="btn-add" @tap="sharemp" v-else-if="getplatform() == 'h5'" :style="{background:'linear-gradient(90deg,'+t('color2')+' 0%,rgba('+t('color2rgb')+',0.8) 100%)'}" :data-id="record.id">{{lang('examples of good friend')}}</view>
				<button class="btn-add" open-type="share" v-else :style="{background:'linear-gradient(90deg,'+t('color2')+' 0%,rgba('+t('color2rgb')+',0.8) 100%)'}" :data-id="record.id">{{lang('examples of good friend')}}</button>
			</block>
		</block>
		<block v-else>
			<view v-if="coupon.isgive == 1 && opt.pid == record.mid && opt.pid > 0" class="btn-add" @tap="receiveCoupon" :style="{background:'linear-gradient(90deg,'+t('color2')+' 0%,rgba('+t('color2rgb')+',0.8) 100%)'}" :data-id="record.id">{{lang('immediately to receive')}}</view>
		</block>
		
		<view class='text-center' @tap="goto" data-url='/pages/index/index' style="margin-top: 40rpx; line-height: 60rpx;"><text>{{lang('return to the home page')}}</text></view>

	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();
export default {
  data() {
    return {
			opt:{},
			loading:false,
      isload: false,
			menuindex:-1,
			pre_url:app.globalData.pre_url,

			textset:{},
			record:{},
			coupon:{},
			shareTitle:'',
			sharePic:'',
			shareDesc:'',
			shareLink:'',
			mid:0
		}
  },

  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.getdata();
  },
	onPullDownRefresh: function () {
		this.getdata();
	},
	onShareAppMessage:function(){
		return this._sharewx({title:this.shareTitle,pic:this.sharePic,desc:this.shareDesc,link:this.shareLink});
	},
	onShareTimeline:function(){
		var sharewxdata = this._sharewx({title:this.shareTitle,pic:this.sharePic,desc:this.shareDesc,link:this.shareLink});
		var query = (sharewxdata.path).split('?')[1];
		console.log(sharewxdata)
		console.log(query)
		return {
			title: sharewxdata.title,
			imageUrl: sharewxdata.imageUrl,
			query: query
		}
	},
  methods: {
	  lang: function(k) {
	  	return app.lang(k);
	  },
		getdata: function () {
			var that = this;
			that.loading = true;
			app.get('ApiCoupon/coupondetail', {rid: that.opt.rid,id: that.opt.id}, function (res) {
				that.loading = false;
				that.textset = app.globalData.textset;
				uni.setNavigationBarTitle({
					title: that.t('优惠券') + that.lang('details')
				});
				if(!res.coupon.id) {
					app.alert(that.t('优惠券')+'不存在');return;
				}
				that.mid = app.globalData.mid;
				that.record = res.record;
				that.coupon = res.coupon;
				that.shareTitle = that.lang('send you a coupon')+that.lang('click to receive');
				that.shareDesc = that.coupon.name;
				that.sharePic = app.globalData.initdata.logo;
				that.shareLink = app.globalData.pre_url +'/h5/'+app.globalData.aid+'.html#/pages/coupon/coupondetail?scene=id_'+that.coupon.id+'-pid_' + app.globalData.mid+'-rid_' + that.record.id;
				that.loaded({title:that.shareTitle,pic:that.sharePic,desc:that.shareDesc,link:that.shareLink});
			});
		},
    getcoupon: function (e) {
			var that = this;
			var datalist = that.datalist;
			var id = e.currentTarget.dataset.id;
			var score = parseInt(e.currentTarget.dataset.score);
			var price = e.currentTarget.dataset.price;

			if (price > 0) {
				app.post('ApiCoupon/buycoupon', {id: id}, function (res) {
					if(res.status == 0) {
							app.error(res.msg);
					} else {
						app.goto('/pages/pay/pay?id=' + res.payorderid);
					}
				})
				return;
			}
			var key = e.currentTarget.dataset.key;
			if (score > 0) {
				app.confirm(that.lang('sure you want to consume') + score + '' + that.t('积分') + that.lang('exchange?'), function () {
					app.showLoading(that.lang('change in the'));
					app.post('ApiCoupon/getcoupon', {id: id}, function (data) {
						app.showLoading(false);
						if (data.status == 0) {
							app.error(data.msg);
						} else {
							app.success(data.msg);
							datalist[key]['haveget'] = data.haveget;
							that.datalist = datalist;
						}
					});
				});
			} else {
				app.showLoading(that.lang('to receive the'));
				app.post('ApiCoupon/getcoupon', {id: id}, function (data) {
					app.showLoading(false);
					if (data.status == 0) {
						app.error(data.msg);
					} else {
						app.success(data.msg);
						setTimeout(function(){
							app.goto('mycoupon');
						},1000)
					}
				});
			}
    },
		
		receiveCoupon:function(){
			var that = this;
			var datalist = that.datalist;
			var rid = that.record.id;
			var id = that.coupon.id;
			app.showLoading(that.lang('to receive the'));
			app.post('ApiCoupon/receiveCoupon', {id: id,rid:rid}, function (data) {
				app.showLoading(false);
				if (data.status == 0) {
					app.error(data.msg);
				} else {
					app.success(data.msg);
					that.getdata();
				}
			});
		},
		
		sharemp:function(){
			app.error(this.lang('click send to friends or share to the circle of friends'));
			this.sharetypevisible = false
		},
		shareapp:function(){
			var that = this;
			that.sharetypevisible = false;
			uni.showActionSheet({
		    itemList: [that.lang('send to wechat friends'), that.lang('share the wechat circle of friends')],
		    success: function (res){
					if(res.tapIndex >= 0){
						var scene = 'WXSceneSession';
						if (res.tapIndex == 1) {
							scene = 'WXSenceTimeline';
						}
						var sharedata = {};
						sharedata.provider = 'weixin';
						sharedata.type = 0;
						sharedata.scene = scene;
						sharedata.title =  that.lang('send you a coupon')+that.lang('click to receive');
						sharedata.summary = that.shareDesc;
						sharedata.href = that.shareLink;
						sharedata.imageUrl = that.sharePic;
						
						uni.share(sharedata);
					}
		    }
		  });
		},
  }
};
</script>
<style>
.container{display:flex;flex-direction:column; padding-bottom: 30rpx;}
.couponbg{width:100%;height:500rpx;}
.orderinfo{ width:94%;margin:-400rpx 3% 20rpx 3%;border-radius:8px;padding:14rpx 3%;background: #FFF;color:#333;}
.orderinfo .topitem{display:flex;padding:60rpx 40rpx;align-items:center;border-bottom:2px dashed #E5E5E5;position:relative}
.orderinfo .topitem .f1{font-size:50rpx;font-weight:bold;}
.orderinfo .topitem .f1 .t1{font-size:60rpx;}
.orderinfo .topitem .f1 .t2{font-size:40rpx;}
.orderinfo .topitem .f2{margin-left:40rpx}
.orderinfo .topitem .f2 .t1{font-size:36rpx;color:#2B2B2B;font-weight:bold;height:50rpx;line-height:50rpx}
.orderinfo .topitem .f2 .t2{font-size:24rpx;color:#999999;height:50rpx;line-height:50rpx}
.orderinfo .item{display:flex;flex-direction:column;width:100%;padding:0 40rpx;margin-top:40rpx}
.orderinfo .item:last-child{ border-bottom: 0;}
.orderinfo .item .t1{width:200rpx;color:#2B2B2B;font-weight:bold;font-size:30rpx;height:60rpx;line-height:60rpx}
.orderinfo .item .t2{color:#2B2B2B;font-size:24rpx;height:auto;line-height:40rpx;white-space:pre-wrap;}
.orderinfo .item .red{color:red}

.text-center { text-align: center;}
.btn-add{width:90%;margin:30rpx 5%;height:96rpx; line-height:96rpx; text-align:center;color: #fff;font-size:30rpx;font-weight:bold;border-radius:48rpx;}
</style>
<template>
<view class="dp-notice" :style="{
	color:params.color,
	backgroundColor:params.bgcolor,
	margin:(params.margin_y*2.2)+'rpx '+(params.margin_x*2.2)+'rpx',
	padding:(params.padding_y*2.2)+'rpx '+(params.padding_x*2.2)+'rpx'
}">
	<view class="left" v-if="params.showimg==1"><image class="image" :src="params.img" mode="heightFix"/></view>
	<view class="right">
		<image v-if="params.showicon==1" class="ico" :src="params.icon"/>
		<swiper style="position:relative;height:40rpx;" autoplay="true" :interval="params.scroll*1000" vertical="true" class="itemlist">
			<swiper-item class="item" v-for="item in data" :key="item.id" :style="{fontSize:(params.fontsize*2.2)+'rpx'}" @click="goto" :data-url="item.hrefurl">{{item.title}}</swiper-item>
		</swiper>
	</view>
</view>
</template>
<script>
	export default {
		props: {
			params:{},
			data:{}
		}
	}
</script>
<style>
.dp-notice{height: auto;background: #fff; font-size: 28rpx; color: #666666;overflow: hidden; white-space:nowrap; position: relative;display:flex;align-items:center;padding:2px 4px}
.dp-notice .left{position:relative;padding-right:20rpx;margin-right:20rpx;height:40rpx;display:flex;align-items: center;}
.dp-notice .left:before { content: " "; position: absolute; width: 0; top: 2px; right: 0; bottom: 2px; border-right: 1px solid #e2e2e2; }
.dp-notice .image{position:relative;height:36rpx;width:auto}
.dp-notice .right{flex-grow:1;display:flex;align-items: center;overflow:hidden}
.dp-notice .right .ico{width:36rpx;height:36rpx;margin-right:10rpx}
.dp-notice .itemlist{width:100%;height:100%;line-height:40rpx;font-size:28rpx;}
</style>
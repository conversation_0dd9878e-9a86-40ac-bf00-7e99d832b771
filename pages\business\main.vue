<template>
<view class="container" :style="{backgroundColor:pageinfo.bgcolor}">
	<dp :pagecontent="pagecontent" :menuindex="menuindex"></dp>
	<view class="ggdialog" v-if="guanggaopic && hideguanggao==0">
		<view class="main">
			<view class="close" @tap="closegg"><image src="/static/img/close.png"></image></view>
			<image :src="guanggaopic" class="guanggaopic" @tap="goto" :data-url="guanggaourl" mode="widthFix"></image>
		</view>
	</view>
	<view style="position:fixed;top:15vh;left:20rpx;z-index:991;background:rgba(0,0,0,0.6);border-radius:20rpx;color:#fff;padding:0 10rpx" v-if="oglist">
		<swiper style="position:relative;height:54rpx;width:450rpx;" autoplay="true" :interval="5000" vertical="true">
			<swiper-item v-for="(item, index) in oglist" :key="index" @tap="goto" :data-url="'/myshop/shop/product?id=' + item.proid" class="flex-y-center">
				<image :src="item.headimg" style="width:40rpx;height:40rpx;border:1px solid rgba(255,255,255,0.7);border-radius:50%;margin-right:4px"></image>
				<div style="width:400rpx;white-space:nowrap;overflow:hidden;text-overflow: ellipsis;font-size:22rpx">{{item.nickname}} {{item.showtime}}{{lang('made a purchase')}} {{item.name}}</div>
			</swiper-item>
		</swiper>
	</view>
	<dp-tabbar :opt="opt" @getmenuindex="getmenuindex"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>
<script>
var app = getApp();
export default {
	data() {
	return {
			opt:{},
			loading:false,
      isload: false,
			menuindex:-1,
			id: 0,
			pageinfo: [],
			pagecontent: [],
			hideguanggao: 0,
			title: "",
			oglist: "", 
			guanggaopic: "",
			guanggaourl: "",
		}
	},
	onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.getdata();
	},
	onPullDownRefresh:function(e){
		this.getdata();
	},
	methods: {
		lang: function(k) {
			return app.lang(k);
		},
		getdata:function(){
			var that = this;
			var opt = this.opt
			var id = 0;
			if (opt && opt.id) {
			  id = opt.id;
			}
			that.loading = true;
			app.get('ApiBusiness/main', {id: id}, function (data) {
				that.loading = false;
			  if (data.status == 2) {
			    //付费查看
			    app.goto('/pages/pay/pay?fromPage=index&id=' + data.payorderid + '&pageid=' + that.id, 'redirect');
			    return;
			  }
			  if (data.status == 1) {
			    var pagecontent = data.pagecontent;
					that.title = data.pageinfo.title;
					that.oglist = data.oglist;
					that.guanggaopic = data.guanggaopic;
					that.guanggaourl = data.guanggaourl;
					that.pageinfo = data.pageinfo;
					that.pagecontent = data.pagecontent;
			    uni.setNavigationBarTitle({
			      title: data.pageinfo.title
			    });
					that.loaded();
			  } else {
			    if (data.msg) {
			      app.alert(data.msg, function () {
			        if (data.url) app.goto(data.url);
			      });
			    } else if (data.url) {
			      app.goto(data.url);
			    } else {
			      app.alert(app.lang('you do not have permission to view'));
			    }
			  }
			});
		},
		closegg: function () {
			this.hideguanggao = 1;
		},
	}
}
</script>
<style>
.ggdialog{ position:fixed;z-index:99991;width:100%;max-width:640px;height:100%;background:rgba(0,0,0,0.6);display:flex;justify-content:center;align-items:center;top:0;}
.ggdialog .main{ width:80%;height:70%;position:relative;display:flex;justify-content:center;align-items:center}
.ggdialog .close{ position:absolute;padding:20rpx;top:-80rpx;right:-40rpx}
.ggdialog .close image{ width:40rpx;height:40rpx;}
.ggdialog .guanggaopic{max-width:100%;height:auto;max-height:100%}
</style>
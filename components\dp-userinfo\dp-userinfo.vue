<template>
  <view>
    <view v-if="params.style==1"
      :style="'background: linear-gradient(180deg, '+t('color1')+' 0%, rgba('+t('color1rgb')+',0) 100%);'">
      <view class="dp-userinfo"
        :style="{background:'url('+params.bgimg+') no-repeat',backgroundsize:'100% auto',margin:(params.margin_y*2.2)+'rpx '+(params.margin_x*2.2)+'rpx',padding:(params.padding_y*2.2)+'rpx '+(params.padding_x*2.2)+'rpx'}">
        <view class="banner">
          <view class='info'>
            <view class="f1">
              <button class="button-view" hoverClass="button-view-hover" bindgetuserinfo="bindGetUserInfo"
                open-type="getUserInfo" lang="zh_CN" style="margin:0">
                <image :src="data.userinfo.headimg" background-size="cover" class="headimg" /></button>
              <view class="flex-y-center">
                <view class="nickname">{{data.userinfo.nickname}}</view>
                <text v-if="params.midshow=='1'"
                  style="font-size:26rpx;padding-left:10rpx">(ID:{{data.userinfo.id}})</text>
                <view class="user-level" v-if="params.levelshow==1">
                  <image class="level-img" :src="data.userlevel.icon" v-if="data.userlevel.icon" />
                  <view class="level-name">{{data.userlevel.name}}</view>
                </view>
                <view class="user-level" v-for="(item, index) in data.userlevelList" :key="index"v-if="params.levelshow==1">
                  <image class="level-img" :src='item.icon' v-if="item.icon" />
                  <view class="level-name">{{item.name}}</view>
                </view>
                <view class="usermid" style="margin-left:10rpx;font-size:24rpx;color:#999"
                  v-if="data.userlevel.can_agent > 0 && data.sysset.reg_invite_code!='0' && data.sysset.reg_invite_code_type==1">
				  <view>{{lang('Welcome Back to SGMall !')}}</view>
                  {{lang("invitation code")}}：<text user-select="true" @click=coprybtn(data.userinfo.yqcode) selectable="true">{{data.userinfo.yqcode}}</text></view>
              </view>
            </view>
            <block v-if="platform=='wx'">
              <view class="usercard" v-if="params.cardshow==1 && data.userinfo.card_code" @tap="opencard"
                :data-card_id="data.userinfo.card_id" :data-card_code="data.userinfo.card_code">
                <image class="img" src="/static/img/ico-card2.png" /><text class="txt">{{lang("the membership card")}}</text></view>
              <view class="usercard" v-if="params.cardshow==1 && !data.userinfo.card_code" @tap="addmembercard"
                :data-card_id="data.card_id">
                <image class="img" src="/static/img/ico-card2.png" /><text class="txt">{{lang("the membership card")}}</text></view>
            </block>
            <block v-if="platform=='mp'">
              <view class="usercard" v-if="params.cardshow==1 && data.userinfo.card_code" @tap="opencard"
                :data-card_id="data.userinfo.card_id" :data-card_code="data.userinfo.card_code">
                <image class="img" src="/static/img/ico-card2.png" /><text class="txt">{{lang("the membership card")}}</text></view>
              <view class="usercard" v-if="params.cardshow==1 && !data.userinfo.card_code" @tap="goto"
                :data-url="data.card_returl">
                <image class="img" src="/static/img/ico-card2.png" /><text class="txt">{{lang("the membership card")}}</text></view>
            </block>
          </view>
          <view class="custom_field" v-if="params.moneyshow==1 || params.scoreshow==1 || params.couponshow==1">
            <view class='item' v-if="params.moneyshow==1" data-url='/pages/money/recharge' @tap='goto'>
              <text class='t2'>{{data.userinfo.money}}</text>
              <text class="t1">{{t('余额')}}</text>
            </view>
            <view class='item' v-if="params.commissionshow==1 && data.userlevel && data.userlevel.can_agent>0"
              data-url='/myshop/commission/index' @tap='goto'>
              <text class='t2'>{{data.userinfo.commission}}</text>
              <text class="t1">{{t('佣金')}}</text>
            </view>
<!--            <view class='item' v-if="params.scoreshow==1" data-url='/myshop/my/scorelog' @tap='goto'>
              <text class='t2'>{{data.userinfo.score}}</text>
              <text class="t1">{{t('积分')}}</text>
            </view> -->
			<view class='item' >
			  <text class='t2'>{{data.userinfo.children}}</text>
			 <text class="t1">{{lang('subordinate')}}</text>
			</view>
            <view class='item' v-if="params.couponshow==1" data-url='/pages/coupon/mycoupon' @tap='goto'>
              <text class='t2'>{{data.userinfo.couponcount}}</text>
              <text class="t1">{{t('优惠券')}}</text>
            </view>

          </view>
          <block v-if="data.parent_show">
            <view class="parent" v-if="data.parent" :style="'background: rgba('+t('color1rgb')+',10%);'">
              <view class="f1">
                <image class="parentimg" :src="data.parent.headimg"></image>
                <view class="parentimg-tag" :style="'background: rgba('+t('color1rgb')+',100%);'">{{lang("referees")}}</view>
              </view>
              <view class="f2 flex1">
                <view class="nick">{{data.parent.nickname}}</view>
                <view class="nick" v-if="data.parent && data.parent.weixin" @tap="copy" :data-text="data.parent.weixin">
                  {{lang("wechat id")}}：{{data.parent.weixin}}<image src="../../static/img/copy.png" class="copyicon"></image>
                </view>
              </view>
              <view class="f3" v-if="data.parent && data.parent.tel" @tap="goto" :data-url="'tel::'+data.parent.tel">
                <image src="../../static/img/tel2.png" class="handle-img"></image>
              </view>
            </view>
            <view class="parent" v-else :style="'background: rgba('+t('color1rgb')+',10%);'">
              <image class="f1 parentimg" :src="data.sysset.logo" />
              <view class="f2 flex1">
                <view class="nick">{{data.sysset.name}}</view>
                <view class="nick">{{data.sysset.tel}}</view>
              </view>
              <view class="f3" @tap="goto" :data-url="'tel::'+data.sysset.tel">
                <image src="../../static/img/tel2.png" class="handle-img"></image>
              </view>
            </view>
          </block>
        </view>
        <view class="userset" @tap="goto" data-url="/myshop/my/set" v-if="params.seticonshow!=='0'">
          <image src="/static/img/set.png" class="img" />
        </view>
      </view>
    </view>

    <view v-if="params.style==2"
      :style="'background: linear-gradient(45deg,'+t('color1')+' 0%, rgba('+t('color1rgb')+',0.8) 100%)'">
      <view class="dp-userinfo2" v-if="params.style==2"
        :style="{background:'url('+params.bgimg+') no-repeat',backgroundSize:'100% auto',margin:(params.margin_y*2.2)+'rpx '+(params.margin_x*2.2)+'rpx',padding:(params.padding_y*2.2)+'rpx '+(params.padding_x*2.2)+'rpx'}">
        <view class="info">
          <image class="headimg" :src="data.userinfo.headimg" />
          <view class="nickname">
            <view class="nick">{{data.userinfo.nickname}} </view>
            <!-- <view class="desc">ID：{{userinfo.id}}</view> -->
            <view style="display: flex;" v-if="params.levelshow==1">
              <view class="user-level" >
                <image class="level-img" :src='data.userlevel.icon' v-if="data.userlevel.icon" />
                <view class="level-name">{{data.userlevel.name}}</view>
              </view>
              <view class="user-level" v-for="(item, index) in data.userlevelList" :key="index" >
                <image class="level-img" :src='item.icon' v-if="item.icon" />
                <view class="level-name">{{item.name}}</view>
              </view>

            </view>

            <view class="usermid" v-if="params.midshow=='1'">{{lang("the user")}}ID：<text user-select="true"
                selectable="true">{{data.userinfo.id}}</text></view>
            <view class="usermid"
              v-if="data.userlevel.can_agent > 0 && data.sysset.reg_invite_code!='0' && data.sysset.reg_invite_code_type==1">
             <view>{{lang('Welcome Back to SGMall !')}}</view>
			 {{lang("invitation code")}}：<text user-select="true" @click=coprybtn(data.userinfo.yqcode) selectable="true">{{data.userinfo.yqcode}}</text></view>
          </view>
          <view class="ktnum" v-if="params.ktnumshow=='1'"
            style="display: flex; position: absolute; right: 30rpx; top:30%;color:#ffff; ">{{lang("open group number")}}：<text
              style="font-size: 24rpx;line-height: 40rpx;">{{data.userinfo.ktnum}}</text></view>
        </view>
        <view class="custom_field"
          v-if="params.moneyshow==1 || params.scoreshow==1 || params.couponshow==1 || params.commissionshow==1">
          <view class='item' v-if="params.moneyshow==1" data-url='/pages/money/recharge' @tap='goto'>
            <text class='t2'>{{data.userinfo.money}}</text>
            <text class="t1">{{t('余额')}}</text>
          </view>
          <view class='item' v-if="params.commissionshow==1 && data.userlevel && data.userlevel.can_agent>0"
            data-url='/myshop/commission/index' @tap='goto'>
            <text class='t2'>{{data.userinfo.commission}}</text>
            <text class="t1">{{t('佣金')}}</text>
          </view>
          <view class='item' v-if="params.scoreshow==1" data-url='/myshop/my/scorelog' @tap='goto'>
            <text class='t2'>{{data.userinfo.score}}</text>
            <text class="t1">{{t('积分')}}</text>
          </view>
          <view class='item' v-if="params.couponshow==1" data-url='/pages/coupon/mycoupon' @tap='goto'>
            <text class='t2'>{{data.userinfo.couponcount}}</text>
            <text class="t1">{{t('优惠券')}}</text>
          </view>
          <view class='item' v-if="params.formshow==1" data-url='/pages/form/formlog?st=1' @tap='goto'>
            <text class='t2'>{{data.userinfo.formcount}}</text>
            <text class="t1">{{params.formtext}}</text>
          </view>
        </view>
        <view class="userset" @tap="goto" data-url="/myshop/my/set" v-if="params.seticonshow!=='0'">
          <image src="/static/img/set.png" class="img" />
        </view>

        <block v-if="platform=='wx'">
          <view class="usercard" v-if="params.cardshow==1 && data.userinfo.card_code" @tap="opencard"
            :data-card_id="data.userinfo.card_id" :data-card_code="data.userinfo.card_code">
            <image class="img" src="/static/img/ico-card2.png" /><text class="txt">{{lang("the membership card")}}</text></view>
          <view class="usercard" v-if="params.cardshow==1 && !data.userinfo.card_code" @tap="addmembercard"
            :data-card_id="data.card_id">
            <image class="img" src="/static/img/ico-card2.png" /><text class="txt">{{lang("the membership card")}}</text></view>
        </block>
        <block v-if="platform=='mp'">
          <view class="usercard" v-if="params.cardshow==1 && data.userinfo.card_code" @tap="opencard"
            :data-card_id="data.userinfo.card_id" :data-card_code="data.userinfo.card_code">
            <image class="img" src="/static/img/ico-card2.png" /><text class="txt">{{lang("the membership card")}}</text></view>
          <view class="usercard" v-if="params.cardshow==1 && !data.userinfo.card_code" @tap="goto"
            :data-url="data.card_returl">
            <image class="img" src="/static/img/ico-card2.png" /><text class="txt">{{lang("the membership card")}}</text></view>
        </block>
      </view>
    </view>
    <view class="dp-userinfo-order" v-if="params.style==2 && data.parent_show"
      :style="{'margin':(params.padding_y*2.2)+'rpx '+(params.padding_x*2.2)+'rpx',marginTop:params.style==2?'-100rpx':'0',marginBottom:'120rpx'}">
      <view class="parent" v-if="data.parent">
        <view class="f1">
          <image class="parentimg" :src="data.parent.headimg"></image>
          <view class="parentimg-tag" :style="'background: rgba('+t('color1rgb')+',100%);'">{{lang("referees")}}</view>
        </view>
        <view class="f2 flex1">
          <view class="nick">{{data.parent.nickname}}</view>
          <view class="nick" v-if="data.parent && data.parent.weixin" @tap="copy" :data-text="data.parent.weixin">
            {{lang("wechat id")}}：{{data.parent.weixin}}<image src="../../static/img/copy.png" class="copyicon"></image>
          </view>
        </view>
        <view class="f3" v-if="data.parent && data.parent.tel" @tap="goto" :data-url="'tel::'+data.parent.tel">
          <image src="../../static/img/tel2.png" class="handle-img"></image>
        </view>
      </view>
      <view class="parent" v-else>
        <image class="f1 parentimg" :src="data.sysset.logo" />
        <view class="f2 flex1">
          <view class="nick">{{data.sysset.name}}</view>
          <view class="nick">{{data.sysset.tel}}</view>
        </view>
        <view class="f3" @tap="goto" :data-url="'tel::'+data.sysset.tel">
          <image src="../../static/img/tel2.png" class="handle-img"></image>
        </view>
      </view>
    </view>
    <view class="dp-userinfo-order"
      :style="{'margin':(params.padding_y*2.2)+'rpx '+(params.padding_x*2.2)+'rpx',marginTop:params.style==2?'-100rpx':'0'}"
      v-if="params.ordershow==1">
      <view class="head">
        <text class="f1">{{lang("my order")}}</text>
        <view class="f2" @tap="goto" data-url="/myshop/order/orderlist"><text>{{lang("check all order")}}</text>
          <image src="/static/img/arrowright.png" class="image" />
        </view>
      </view>
      <view class="content">
        <view class="item" @tap="goto" data-url="/myshop/order/orderlist?st=0">
          <text class="iconfont icondaifukuan" style="{color:#ff412b}"></text>
          <view class="t2" v-if="data.orderinfo.count0>0">{{data.orderinfo.count0}}</view>
          <text class="t3">{{lang("for the payment")}}</text>
        </view>
        <view class="item" @tap="goto" data-url="/myshop/order/orderlist?st=1">
          <!-- <image src="/static/img/order2.png" class="image"/> -->
          <text class="iconfont icondaifahuo" style="{color:#ff412b}"></text>
          <view class="t2" v-if="data.orderinfo.count1>0">{{data.orderinfo.count1}}</view>
          <text class="t3">{{lang("to send the goods")}}</text>
        </view>
        <view class="item" @tap="goto" data-url="/myshop/order/orderlist?st=2">
          <!-- <image src="/static/img/order3.png" class="image"/> -->
          <text class="iconfont icondaishouhuo" style="{color:#ff412b}"></text>
          <view class="t2" v-if="data.orderinfo.count2>0">{{data.orderinfo.count2}}</view>
          <text class="t3">{{lang("for the goods")}}</text>
        </view>
        <view class="item" @tap="goto" data-url="/myshop/order/orderlist?st=3">
          <!-- <image src="/static/img/order4.png" class="image"/> -->
          <text class="iconfont iconyiwancheng" style="{color:#ff412b}"></text>
          <view class="t2" v-if="data.orderinfo.count3>0">{{data.orderinfo.count3}}</view>
          <text class="t3">{{lang("has been completed")}}</text>
        </view>
    <!--    <view class="item" @tap="goto" data-url="/myshop/order/refundlist"> -->
          <!-- <image src="/static/img/order4.png" class="image"/> -->
        <!--  <text class="iconfont icontuikuandingdan" :style="{color:t('color1')}"></text>
          <view class="t2" v-if="data.orderinfo.count4>0">{{data.orderinfo.count4}}</view>
          <text class="t3">{{lang("a refund")}}/{{lang("after-sales")}}</text> -->
      <!--  </view> -->
      </view>
    </view>

  </view>
</template>
<script>
var app = getApp();
export default {
  data () {
    return {
      textset: app.globalData.textset,
      platform: app.globalData.platform
    }
  },
  props: {
    params: {},
    data: {}
  },
  methods: {
	  lang: function(k) {
	  	return app.lang(k);
	  },
	  coprybtn:function(e){
		 // document.execCommand("copy",e);
		uni.setClipboardData({
					    data: e,
					    success: function () {
					        uni.showToast({
					            title: 'copy success',
					        });
					    },
					    fail:function () {
					        uni.showToast({
					            title: 'fail',
					        });
					    }
					});
		
	  },
    opencard: function (e) {
      var cardId = e.currentTarget.dataset.card_id
      var code = e.currentTarget.dataset.card_code
      if (app.globalData.platform == 'mp') {
        var jweixin = require('jweixin-module');
        jweixin.openCard({
          cardList: [{
            cardId: cardId,
            code: code
          }],
          success: function (res) { }
        })
      } else {
        wx.openCard({
          cardList: [{
            cardId: cardId,
            code: code
          }],
          success: function (res) { }
        })
      }

    },
    //领取微信会员卡
    addmembercard: function (e) {
      var cardId = e.currentTarget.dataset.card_id
      app.post('ApiCoupon/getmembercardparam', { card_id: cardId }, function (res) {
        if (res.status == 0) {
          app.alert(res.msg);
          return;
        }
        wx.navigateToMiniProgram({
          appId: 'wx74316936e2553915', // 固定为此appid，不可改动
          extraData: res.extraData, // 包括encrypt_card_id outer_str biz三个字段，须从step3中获得的链接中获取参数
          success: function () { },
          fail: function () { },
          complete: function () { }
        })
      })
    },
  }
}
</script>
<style>
.dp-userinfo {
  position: relative;
}
.dp-userinfo .banner {
  width: 100%;
  margin-top: 120rpx;
  border-radius: 16rpx;
  background: #fff;
  padding: 0 20rpx 10rpx;
  color: #333;
  position: relative;
}
.dp-userinfo .banner .info {
  display: flex;
  align-items: flex-end;
}
.dp-userinfo .banner .info .f1 {
  display: flex;
  flex-direction: column;
}
.dp-userinfo .banner .headimg {
  margin-top: -60rpx;
  width: 148rpx;
  height: 148rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  border: 3px solid #eee;
}
.dp-userinfo .banner .info {
  margin-left: 20rpx;
  display: flex;
  flex: auto;
}
.dp-userinfo .banner .info .nickname {
  min-width: 140rpx;
  max-width: 460rpx;
  text-align: center;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 34rpx;
  font-weight: bold;
  max-width: 300rpx;
  overflow: hidden;
  white-space: nowrap;
}
.dp-userinfo .banner .getbtn {
  width: 120rpx;
  height: 44rpx;
  padding: 0 3px;
  line-height: 44rpx;
  font-size: 24rpx;
  background: #09bb07;
  color: #fff;
  position: absolute;
  top: 76rpx;
  left: 10rpx;
}
.dp-userinfo .banner .user-level {
  margin-left: 5px;
  color: #b48b36;
  background-color: #ffefd4;
  margin-top: 2px;
  width: auto;
  height: 36rpx;
  border-radius: 18rpx;
  padding: 0 20rpx;
  display: flex;
  align-items: center;
}
.dp-userinfo .banner .user-level .level-img {
  width: 32rpx;
  height: 32rpx;
  margin-right: 3px;
  margin-left: -14rpx;
  border-radius: 50%;
}
.dp-userinfo .banner .user-level .level-name {
  font-size: 24rpx;
}
.dp-userinfo .banner .user-level image {
  border-radius: 50%;
}
.dp-userinfo .banner .usercard {
  position: absolute;
  right: 32rpx;
  top: 28rpx;
  width: 160rpx;
  height: 60rpx;
  text-align: center;
  border: 1px solid #ffb2b2;
  border-radius: 8rpx;
  color: #fc4343;
  font-size: 24rpx;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
}
.dp-userinfo .banner .usercard .img {
  width: 30rpx;
  height: 30rpx;
  margin-right: 8rpx;
  padding-bottom: 4rpx;
}

.dp-userinfo .custom_field {
  display: flex;
  width: 100%;
  align-items: center;
  padding: 16rpx 8rpx;
  background: #fff;
}
.dp-userinfo .custom_field .item {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.dp-userinfo .custom_field .item .t1 {
  color: #666;
  font-size: 26rpx;
}
.dp-userinfo .custom_field .item .t2 {
  color: #111;
  font-weight: bold;
  font-size: 36rpx;
}

.dp-userinfo .userset {
  width: 54rpx;
  height: 54rpx;
  padding: 10rpx;
  position: absolute;
  top: 40rpx;
  right: 30rpx;
}
.dp-userinfo .userset .img {
  width: 100%;
  height: 100%;
}

.dp-userinfo2 {
  width: 100%;
  height: 490rpx;
  display: flex;
  flex-direction: column;
  position: relative;
}
.dp-userinfo2 .info {
  display: flex;
  margin-top: 80rpx;
  margin-left: 40rpx;
}
.dp-userinfo2 .info .headimg {
  width: 108rpx;
  height: 108rpx;
  background: #fff;
  border: 3rpx solid rgba(255, 255, 255, 0.7);
  border-radius: 50%;
}
.dp-userinfo2 .info .nickname {
  margin-left: 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.dp-userinfo2 .info .nickname .nick {
  font-size: 36rpx;
  font-weight: bold;
  color: #fff;
  height: 60rpx;
  line-height: 60rpx;
  max-width: 400rpx;
  overflow: hidden;
  margin-right: 10rpx;
}
.dp-userinfo2 .info .nickname .desc {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.6);
  height: 40rpx;
  line-height: 40rpx;
}
.dp-userinfo2 .info .nickname .user-level {
  color: rgba(255, 255, 255, 0.6);
  margin-top: 2px;
  width: auto;
  height: 36rpx;
  border-radius: 18rpx;
  padding: 0 20rpx;
  display: flex;
  align-items: center;
}
.dp-userinfo2 .info .nickname .user-level .level-img {
  width: 32rpx;
  height: 32rpx;
  margin-right: 3px;
  margin-left: -14rpx;
  border-radius: 50%;
}
.dp-userinfo2 .info .nickname .user-level .level-name {
  font-size: 24rpx;
}
.dp-userinfo2 .info .nickname .usermid {
  color: rgba(255, 255, 255, 0.8);
  font-size: 24rpx;
}

.dp-userinfo2 .custom_field {
  display: flex;
  width: 100%;
  align-items: center;
  padding: 16rpx 8rpx;
  margin-top: 30rpx;
}
.dp-userinfo2 .custom_field .item {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.dp-userinfo2 .custom_field .item .t1 {
  color: rgba(255, 255, 255, 0.6);
  font-size: 24rpx;
  margin-top: 10rpx;
}
.dp-userinfo2 .custom_field .item .t2 {
  color: #ffffff;
  font-weight: bold;
  font-size: 32rpx;
}

.dp-userinfo2 .usercard {
  width: 154rpx;
  height: 54rpx;
  background: #fff;
  border-radius: 27rpx 0 0 27rpx;
  display: flex;
  align-items: center;
  padding-left: 20rpx;
  position: absolute;
  top: 140rpx;
  right: 0;
}
.dp-userinfo2 .usercard .img {
  width: 32rpx;
  height: 32rpx;
  margin-right: 6rpx;
}
.dp-userinfo2 .usercard .txt {
  color: #f4504c;
  font-size: 24rpx;
  font-weight: bold;
}
.dp-userinfo2 .userset {
  width: 54rpx;
  height: 54rpx;
  padding: 10rpx;
  position: absolute;
  top: 40rpx;
  right: 30rpx;
}
.dp-userinfo2 .userset .img {
  width: 100%;
  height: 100%;
}

.dp-userinfo-order {
  background: #fff;
  padding: 0 20rpx;
  border-radius: 16rpx;
  position: relative;
}
.dp-userinfo-order .head {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 16rpx 0;
}
.dp-userinfo-order .head .f1 {
  flex: auto;
  font-size: 30rpx;
  padding-left: 16rpx;
  font-weight: bold;
  color: #333;
}
.dp-userinfo-order .head .f2 {
  display: flex;
  align-items: center;
  color: #999;
  width: 200rpx;
  padding: 10rpx 0;
  text-align: right;
  justify-content: flex-end;
}
.dp-userinfo-order .head .f2 .image {
  width: 30rpx;
  height: 30rpx;
}
.dp-userinfo-order .head .t3 {
  width: 40rpx;
  height: 40rpx;
}
.dp-userinfo-order .content {
  display: flex;
  width: 100%;
  padding: 0 0 10rpx 0;
  align-items: center;
  font-size: 24rpx;
}
.dp-userinfo-order .content .item {
  padding: 10rpx 0;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}
.dp-userinfo-order .content .item .image {
  width: 50rpx;
  height: 50rpx;
}
.dp-userinfo-order .content .item .iconfont {
  font-size: 60rpx;
}
.dp-userinfo-order .content .item .t3 {
  padding-top: 3px;
}
.dp-userinfo-order .content .item .t2 {
  display: flex;
  align-items: center;
  justify-content: center;
  background: red;
  color: #fff;
  border-radius: 50%;
  padding: 0 10rpx;
  position: absolute;
  top: 0px;
  right: 20rpx;
  width: 35rpx;
  height: 35rpx;
  text-align: center;
}

.parent {
  padding: 20rpx;
  border-radius: 16rpx;
  justify-content: center;
  display: flex;
  align-items: center;
  font-size: 24rpx;
  margin-bottom: 10rpx;
}
.parent .parentimg {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  z-index: 10;
}
.parent .parentimg-tag {
  color: #fff;
  text-align: center;
  margin-top: -20rpx;
  z-index: 11;
  border-radius: 12rpx;
  padding: 2rpx 4rpx;
  position: relative;
  bottom: 2rpx;
}
.parent .copyicon {
  width: 26rpx;
  height: 26rpx;
  margin-left: 8rpx;
  position: relative;
  top: 4rpx;
}
.parent .f1 {
  position: relative;
}
.parent .f2 {
  padding: 0 30rpx;
}
.parent .handle-img {
  width: 60rpx;
  height: 60rpx;
}
.parent .btn-box {
  padding: 20rpx 0;
}
.parent button {
  padding: 0 40rpx;
  color: #fff;
  border-radius: 20rpx;
  line-height: 60rpx;
}
	
.info{
	position:relative;
}
.usermid{
	position: absolute;
	top: 0.5em;
	right: 0.5em;
	  text-align:right ;
}
.user-level{
	position: absolute;
	right:0em;
}
.dp-userinfo-order .content .item .iconfont{
	color:#ff4124;
}
</style>
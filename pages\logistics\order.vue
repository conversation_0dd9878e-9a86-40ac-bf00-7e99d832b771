<template>
	<view class="container">
		<view class="tab-block y-center">
			<view v-for="(item, index) in tabList" :key="index" class="flex1 xy-center"
				:class="{active: index === tabIndex}" @tap="onTabClick(item, index)">{{lang(item.name)}}</view>
		</view>
		<view class="order-block">
			<view v-for="(item, index) in orderList" :key="index" class="order-item">
				<view class="_header x-between">
					<view class="y-center">
						<image src="../../static/img/my.png" style="width: 48rpx;height: 48rpx;margin-right: 10rpx;"></image>
						<view>{{item.title}}</view>
					</view>
					<view class="color1">{{item.status}}</view>
				</view>
				<view class="_content">
					<view class="y-center">
						<image class="image" :src="item.image" mode="aspectFill"></image>
						<view class="">
							<view>{{item.name}}</view>
							<view class="info" style="margin-top: 10rpx;">{{item.info}}</view>
						</view>
					</view>
					<view style="margin-top: 20rpx;">{{lang('cost')}}：<span class="color1">¥{{item.price}}</span></view>
					<view class="x-between" style="margin-top: 10rpx;">
						<view></view>
						<view class="y-center">
							<view class="button1 xy-center">{{lang('Close Order')}}</view>
							<view class="button1 xy-center">{{lang('particulars')}}</view>
							<view class="button1 xy-center">{{lang('Logistics Tracking')}}</view>
							<view class="button xy-center">{{lang('Go and pay')}}</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	var app = getApp();
	export default {
		data() {
			return {
				tabList: [{
					name: 'all',
					value: 1
				}, {
					name: 'for the payment',
					value: 3
				}, {
					name: 'to send the goods',
					value: 2
				}, {
					name: 'for the goods',
					value: 2
				}, {
					name: 'has been completed',
					value: 2
				}],
				tabIndex: 0,
				orderList: [{
					title: 'E速物流',
					name: 'E速物流，泰国专运',
					info: '陆运 （普货） ',
					status: '待支付',
					price: '320.00',
					image: 'https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fimage109.360doc.com%2FDownloadImg%2F2023%2F11%2F1102%2F275164660_5_20231111021737523&refer=http%3A%2F%2Fimage109.360doc.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1723034414&t=ecaddefa9b01abdd8213bd39fbc8fe00'
				}, {
					title: 'E速物流',
					name: 'E速物流，泰国专运',
					info: '陆运 （普货） ',
					status: '待支付',
					price: '320.00',
					image: 'https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fimage109.360doc.com%2FDownloadImg%2F2023%2F11%2F1102%2F275164660_5_20231111021737523&refer=http%3A%2F%2Fimage109.360doc.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1723034414&t=ecaddefa9b01abdd8213bd39fbc8fe00'
				}, {
					title: 'E速物流',
					name: 'E速物流，泰国专运',
					info: '陆运 （普货） ',
					status: '待支付',
					price: '320.00',
					image: 'https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fimage109.360doc.com%2FDownloadImg%2F2023%2F11%2F1102%2F275164660_5_20231111021737523&refer=http%3A%2F%2Fimage109.360doc.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1723034414&t=ecaddefa9b01abdd8213bd39fbc8fe00'
				}, {
					title: 'E速物流',
					name: 'E速物流，泰国专运',
					info: '陆运 （普货） ',
					status: '待支付',
					price: '320.00',
					image: 'https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fimage109.360doc.com%2FDownloadImg%2F2023%2F11%2F1102%2F275164660_5_20231111021737523&refer=http%3A%2F%2Fimage109.360doc.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1723034414&t=ecaddefa9b01abdd8213bd39fbc8fe00'
				}, {
					title: 'E速物流',
					name: 'E速物流，泰国专运',
					info: '陆运 （普货） ',
					status: '待支付',
					price: '320.00',
					image: 'https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fimage109.360doc.com%2FDownloadImg%2F2023%2F11%2F1102%2F275164660_5_20231111021737523&refer=http%3A%2F%2Fimage109.360doc.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1723034414&t=ecaddefa9b01abdd8213bd39fbc8fe00'
				}, {
					title: 'E速物流',
					name: 'E速物流，泰国专运',
					info: '陆运 （普货） ',
					status: '待支付',
					price: '320.00',
					image: 'https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fimage109.360doc.com%2FDownloadImg%2F2023%2F11%2F1102%2F275164660_5_20231111021737523&refer=http%3A%2F%2Fimage109.360doc.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1723034414&t=ecaddefa9b01abdd8213bd39fbc8fe00'
				}]
			}
		},
		onLoad() {
			uni.setNavigationBarTitle({
				title: this.lang('Order management')
			})
		},
		methods: {
			lang: function(k) {
				return app.lang(k);
			},
			onTabClick(data, index) {
				this.tabIndex = index
			}
		}
	}
</script>

<style lang="scss" scoped>
	.tab-block {
		background-color: #fff;
		padding: 30rpx 0;
		font-size: 28rpx;
		color: #3D3D3D;
		position: fixed;
		left: 0;
		top: 0;
		width: 750rpx;
		background-color: #fff;
		z-index: 999;
		
		.active {
			font-weight: bold;
			position: relative;
		
			&:before {
				content: '';
				width: 60rpx;
				height: 6rpx;
				background: #FD4A46;
				position: absolute;
				bottom: -30rpx;
			}
		}
	}
	.order-block {
		padding: 20rpx;
		margin-top: 80rpx;
	
		.order-item {
			margin-top: 20rpx;
			background: #fff;
	
			._header {
				padding: 20rpx;
				border-bottom: 1rpx solid #D8D8D8;
			}
	
			._content {
				padding: 20rpx;
			}
	
			.color1 {
				color: #FD4A46;
			}
	
			.info {
				color: #999999;
			}
	
			.image {
				width: 120rpx;
				height: 120rpx;
				border-radius: 10rpx;
				margin-right: 20rpx;
			}
	
			.button {
				width: 148rpx;
				height: 52rpx;
				border-radius: 8rpx;
				background: #FF3333;
				color: #FFFFFF;
				margin-left: 20rpx;
			}
	
			.button1 {
				width: 148rpx;
				height: 52rpx;
				border-radius: 8rpx;
				// background: #FFFFFF;
				color: #3D3D3D;
				border: 2rpx solid #3D3D3D;
				margin-left: 20rpx;
			}
		}
	}
	
	.flex {
		display: flex;
	}
	
	.flex1 {
		flex: 1;
	}
	
	.flex-column {
		display: flex;
		flex-direction: column;
	}
	
	.x-center {
		display: flex;
		justify-content: center;
	}
	
	.x-between {
		display: flex;
		justify-content: space-between;
	}
	
	.y-center {
		display: flex;
		align-items: center;
	}
	
	.xy-center {
		display: flex;
		justify-content: center;
		align-items: center;
	}
	
	.line1 {
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
	}
</style>
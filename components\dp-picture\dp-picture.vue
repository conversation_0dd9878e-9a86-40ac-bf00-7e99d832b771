<template>
<view class="dp-picture" :style="{
	backgroundColor:params.bgcolor,
	margin:params.margin_y*2.2+'rpx '+params.margin_x*2.2+'rpx 0',
	padding:params.padding_y*2.2+'rpx '+params.padding_x*2.2+'rpx'
}">
	<view v-for="(item,index) in data" :key="item.id" @click="goto" :data-url="item.hrefurl">
		<button class="picture-button" v-if="item.hrefurl=='contact::'" open-type="contact">
			<image class="picture-img" :src="item.imgurl" mode="widthFix" :style="{borderRadius:(params.borderradius*2.2)+'rpx'}"></image>
		</button>
		<button class="picture-button" v-else-if="item.hrefurl=='share::'" open-type="share">
			<image class="picture-img" :src="item.imgurl" mode="widthFix" :style="{borderRadius:(params.borderradius*2.2)+'rpx'}"></image>
		</button>
		<image v-else class="picture-img" :src="item.imgurl" mode="widthFix" :style="{borderRadius:(params.borderradius*2.2)+'rpx'}"></image>
	</view>
</view>
</template>
<script>
	export default {
		props: {
			params:{},
			data:{}
		}
	}
</script>
<style>
.dp-picture{height: auto; position: relative;}
.dp-picture .picture-img {width: 100%;height:auto; margin: 0px; padding: 0px;display:table}
.dp-picture .picture-button {line-height:0}
</style>
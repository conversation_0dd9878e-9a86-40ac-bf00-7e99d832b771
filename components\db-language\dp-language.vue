<template>
	<view class="changebox">
		<view class="language">
			语言 {{getLange()?getLange():''}}
		</view>
		<view class="change" @tap="tochange">
			Change language
			<image src="/static/img/changelang.png" class="imgset" />

		</view>
	</view>
</template>
<script>
	var app = getApp();

	export default {
		data() {
			return {
				"bannerindex": 0
			}
		},
		props: {
			params: {},
			data: {}
		},
		methods: {
			tochange() {
				app.goto('/pages/index/changelang?type=2', 'reLaunch')
			},
			getLange() {
				let language = uni.getStorageSync("mylang")
				console.log("language", language)
				if (language == "zh_cn") {
					return '中文'
				} else if (language == "zh_tw") {
					return '中文（繁体）'
				} else if (language == "en") {
					return 'English'
				} else if (language == "vnm") {
					return 'Tiếng Việt'
				} else if (language == "tha") {
					return 'ภาษาไทย'
				} else if (language == "in") {
					return 'भारत'
				} else if (language == "my") {
					return 'Malay'
				}
			},

		}
	}
</script>
<style>
	.changebox {
		/* position: absolute; */
		display: flex;
		justify-content: space-between;
		right:30rpx;
		/* top:8rpx; */
		text-align: right;
		padding: 15px;
		color: #8a8a8a;
	}

	.imgset {
		width: 15px;
		height: 15px;
		vertical-align: -.3em;
		margin-left: 0.1em;
	}
</style>
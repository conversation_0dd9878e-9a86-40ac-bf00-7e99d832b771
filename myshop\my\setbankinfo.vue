<template>
<view class="container">
	<block v-if="isload">
		<form @submit="formSubmit" @reset="formReset">
		<view class="form">
			<view class="form-item">
					<text class="label">
						<!-- 开户行 -->
						{{lang('Bank name')}}
						</text>
					<picker class="picker" mode="selector" name="bankname" value="0" :range="banklist" @change="bindBanknameChange">
						<view v-if="bankname">{{bankname}}</view>
						<view v-else>
							<!-- 请选择开户行 -->
							{{lang('please change ')}}
							</view>
					</picker>
			</view>
			<view class="form-item">
					<text class="label">
						<!-- 银行卡号 -->
						{{lang('Branch Code')}}
						</text>
					<input type="text" class="input" :placeholder="lang('please enter')" name="bankcardnum" :value="userinfo.bankcardnum" placeholder-style="color:#BBBBBB;font-size:28rpx"></input>
			</view>
			<view class="form-item">
					<text class="label">
						<!-- 所属分支行 -->
						{{lang('Account No.')}}
						</text>
					<input type="text" class="input" :placeholder="lang('please enter')"  name="bankaddress" :value="userinfo.bankaddress" placeholder-style="color:#BBBBBB;font-size:28rpx"></input>
			</view>
			<view class="form-item">
					<text class="label">
						<!-- 持卡人姓名 -->
						{{lang('Account Name')}}:
						</text>
					<input type="text" class="input" :placeholder="lang('please enter')"  name="bankcarduser" :value="userinfo.bankcarduser" placeholder-style="color:#BBBBBB;font-size:28rpx"></input>
			</view>
			<view class="form-item">
					<text class="label">
						<!-- 开户行 -->
						{{lang('Account Type')}}
						</text>
					<picker class="picker" mode="selector" name="accoounttype" value="0" :range="banklist1" @change="bindBanknameChange1">
						<view v-if="accoounttype">{{accoounttype}}</view>
						<view v-else>
							<!-- 请选择开户行 -->
							{{lang('please change')}}
							</view>
					</picker>
			</view>
			
		</view>
		<button class="set-btn" form-type="submit" :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}">{{lang('Save')}}</button>
		</form>
	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
			opt:{},
			loading:false,
      isload: false,
			menuindex:-1,
		
      banklist: [  'DBS BANK LTD','DBS Bank','POSB Bank','OCBC Bank','OVERSEA-CHINESE BANKING CORP LTD','UNITED OVERSEAS BANK LIMITED','UOB Bank','THE HONGKONG AND SHANGHAI BANKING CORPORATION LIMITED',
			'MAYBANK SINGAPORE LIMITED','Maybank','RHB BANK BERHAD','BANK OF CHINA LIMITED','BANK OF INDIA','HONG LEONG BANK BERHAD','HONG LEONG FINANCE BHD',
			'CIMB BANK BERHAD','CITIBANK SINGAPORE LIMITED','Citibank','HSBC BANK (SINGAPORE) LIMITED','HSBC Bank','MALAYAN BANKING BERHAD','STANDARD CHARTERED BANK (SINGAPORE) LIMITED'
		  // '工商银行', '农业银行', '中国银行', '建设银行', '招商银行', '邮储银行', '交通银行', '浦发银行', '民生银行', '兴业银行', '平安银行', '中信银行', '华夏银行', '广发银行', '光大银行', '北京银行', '宁波银行'
		  ],
		  banklist1:[
			  'Current','Saving'
		  ],
      bankname: '',
	  accoounttype:"",
			userinfo:{},
			textset:{},
    };
  },

  onLoad: function (opt) {

	  uni.getStorageSync('mylang')=='zh_cn'? uni.setLocale('zh'):uni.setLocale('en')
		this.opt = app.getopts(opt);
		this.isload = true
		this.getdata();
		uni.setNavigationBarTitle({
			title:app.lang("SGMAll"),
			success() {
				console.log(app.lang("SGMAll"))
			}
		})
  },
	onPullDownRefresh: function () {
		this.getdata();
	},
  methods: {
	  lang: function(k) {
	  	return app.lang(k);
	  }, 
		getdata: function () {
			var that = this;
			that.loading = true;
			app.get('ApiMy/set', {}, function (data) {
				that.loading = false;
				that.userinfo = data.userinfo;
				that.bankname = data.userinfo.bankname;
				that.accoounttype = data.userinfo.accoounttype
				that.loaded();
			});
		},
    formSubmit: function (e) {
      var formdata = e.detail.value;
			var bankname = this.bankname
			var bankcarduser = formdata.bankcarduser
			var bankcardnum = formdata.bankcardnum
			var bankaddress = formdata.bankaddress
			var accoounttype = this.accoounttype
			console.log('accoounttype',accoounttype)
			
      if (bankname == '') {
        app.alert('please eenter Bank name');return;
      }
			app.showLoading('saveing');
      app.post("ApiMy/setfield", {bankname:bankname,accoounttype:accoounttype,bankaddress:bankaddress,bankcarduser:bankcarduser,bankcardnum:bankcardnum}, function (data) {
				app.showLoading(false);
        if (data.status == 1) {
          app.success(data.msg);
          setTimeout(function () {
            app.goback(true);
          }, 1000);
        } else {
          app.error(data.msg);
        }
      });
    },
    bindBanknameChange: function (e) {
      this.bankname = this.banklist[e.detail.value];
	  console.log('122',e.detail.value)
    },
	bindBanknameChange1: function (e) {
	  this.accoounttype = this.banklist1[e.detail.value];
	  console.log('hdds', this.banklist1[e.detail.value])
	},
  }
};
</script>
<style>
.form{ width:94%;margin:20rpx 3%;border-radius:5px;padding:20rpx 20rpx;padding: 0 3%;background: #FFF;}
.form-item{display:flex;align-items:center;width:100%;border-bottom: 1px #ededed solid;height:98rpx;line-height:98rpx;}
.form-item:last-child{border:0}
.form-item .label{color: #000;width:200rpx;}
.form-item .input{flex:1;color: #000;}
.form-item .picker{height: 60rpx;line-height:60rpx;margin-left: 0;flex:1;color: #000;}
.set-btn{width: 90%;margin:60rpx 5%;height:96rpx;line-height:96rpx;border-radius:48rpx;color:#FFFFFF;font-weight:bold;}
</style>
<template>
<view class="container" :style="{backgroundColor:pageinfo.bgcolor}">
	<dp :pagecontent="pagecontent"></dp>
	<view v-if="copyright!=''" class="copyright">{{copyright}}</view>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
		<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();
export default {
	data() {
	return {
			opt:{},
			loading:false,
      isload: false,
			pageinfo: [],
			pagecontent: [],
			copyright:'',
		}
	},
	onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.getdata(); 
		uni.setNavigationBarTitle({
			title:app.lang("SGMAll"),
			success() {
				console.log(app.lang("SGMAll"))
			}
		})
	},
	onPullDownRefresh:function(e){
		this.getdata();
	},
	methods: {
		
		lang: function(k) {
			return app.lang(k);
		}, 
		getdata:function(){
			var that = this;
			that.loading = true;
			app.get('ApiMy/usercenter',{},function (data){
				that.loading = false;
			  var pagecontent = data.pagecontent;
				that.pageinfo = data.pageinfo;
				that.pagecontent = data.pagecontent;
				that.copyright = data.copyright;
				uni.setNavigationBarTitle({
					title: data.pageinfo.title
				});
				that.loaded();
			});
		}
	}
}
</script>
<style>

</style>
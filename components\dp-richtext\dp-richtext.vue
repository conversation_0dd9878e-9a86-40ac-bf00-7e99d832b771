<template>
<view class="dp-richtext" :style="{
	backgroundColor:params.bgcolor,
	margin:(params.margin_y*2.2)+'rpx '+(params.margin_x*2.2)+'rpx 0',
	padding:(params.padding_y*2.2)+'rpx '+(params.padding_x*2.2)+'rpx'
}">
		<!-- <rich-text style="background-color:{{params.bgcolor}}" nodes='{{content}}'></rich-text> -->
		<!-- <template is="wxParse" data="{{wxParseData:content}}"/> -->
		<!-- <parser html="{{content}}" /> -->
		<parse :content="content" @navigate="navigate"></parse>
	</view>
</template>
<script>
	export default {
		props: {
			params:{},
			data:{},
			content:{}
		}
	}
</script>
<style>
.dp-richtext{height: auto; position: relative;text-align:justify;display:block;word-wrap: break-word;overflow: hidden;font-size:32rpx}
</style>
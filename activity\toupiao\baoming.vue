<template>
<view class="container" style="padding:20rpx 0" :style="{background:toupiao.color1}">
	<block v-if="isload">
		<view style="color:red;padding:30rpx 30rpx;margin-top:20rpx;background:#fff;margin:0 28rpx;border-radius:8rpx" v-if="info.id && info.status==2">审核不通过：{{info.reason}}，请修改后再提交</view>
		<view style="color:red;padding:30rpx 30rpx;margin-top:20rpx;background:#fff;margin:0 28rpx;border-radius:8rpx" v-if="info.id && info.status==1">您已成功参与报名</view>
		<view style="color:green;padding:30rpx 30rpx;margin-top:20rpx;background:#fff;margin:0 28rpx;border-radius:8rpx" v-if="info.id && info.status==0">您已提交申请，请等待审核</view>
		<form @submit="formSubmit" @reset="formReset">

		
		<view class="apply_box">
			<view class="apply_item">
				<view>名称<text style="color:red"> *</text></view>
				<view class="flex-y-center"><input type="text" name="name" :value="info.name" placeholder="请输入选手名称"/></view>
			</view>
			<view class="apply_item">
				<view>联系方式<text style="color:red"> *</text></view>
				<view class="flex-y-center"><input type="text" name="weixin" :value="info.weixin" placeholder="请输入联系方式"></input></view>
			</view>
		</view>
		<view class="apply_box">
			<view class="apply_item" style="border-bottom:0"><text>首图<text style="color:red"> *</text></text></view>
			<view class="flex" style="flex-wrap:wrap;padding-bottom:20rpx;">
				<view v-if="pic" class="layui-imgbox">
					<view class="layui-imgbox-close" @tap="removepic"><image src="/static/img/ico-del.png"></image></view>
					<view class="layui-imgbox-img"><image :src="pic" @tap="previewImage" :data-url="pic" mode="widthFix"></image></view>
				</view>
				<view class="uploadbtn" :style="'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'" @tap="uploadpic" v-if="!pic"></view>
				<input type="text" hidden="true" name="pic" :value="pic" maxlength="-1"></input>
			</view>
		</view>
		<view class="apply_box">
			<view class="apply_item" style="border-bottom:0"><text>详情图片</text></view>
			<view class="flex" style="flex-wrap:wrap;padding-bottom:20rpx;">
				<view v-for="(item, index) in pics" :key="index" class="layui-imgbox">
					<view class="layui-imgbox-close" @tap="removeimg" :data-index="index" data-field="pics"><image src="/static/img/ico-del.png"></image></view>
					<view class="layui-imgbox-img"><image :src="item" @tap="previewImage" :data-url="item" mode="widthFix"></image></view>
				</view>
				<view class="uploadbtn" :style="'background:url('+pre_url+'/static/img/shaitu_icon.png) no-repeat 60rpx;background-size:80rpx 80rpx;background-color:#F3F3F3;'" @tap="uploadimg" data-field="pics"></view>
				<input type="text" hidden="true" name="pics" :value="pics.join(',')" maxlength="-1"></input>
			</view>
		</view>
		<view class="apply_box" style="padding-bottom:20rpx">
			<view class="apply_item flex-col">
				<view><text class="title">详情文字</text></view>
				<view class="flex-y-center"><textarea type="text" name="detail_txt" :value="info.detail_txt" placeholder="请输入详情文字~" placeholder-style="font-size:24rpx" style="height:100rpx;background:#F8F8F8;padding:10rpx 20rpx" maxlength="-1"></textarea></view>
			</view>
		</view>
		<button class="set-btn" form-type="submit" :style="{color:toupiao.color2}" v-if="!info.id || info.status==2">提 交</button>
		</form>
	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
			opt:{},
			loading:false,
      isload: false,
			menuindex:-1,
			pre_url:app.globalData.pre_url,
			
			pic:'',
			pics:[],
			info:{},
			toupiao:{},
      headimg:[],
    };
  },

  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.getdata();
  },
	onPullDownRefresh: function () {
		this.getdata();
	},
  methods: {
		getdata:function(){
			var that = this;
			that.loading = true;
			app.get('ApiToupiao/baoming', {id:that.opt.id}, function (res) {
				that.loading = false;
        that.info = res.info;
        that.toupiao = res.toupiao;
				
				var pics = res.info ? res.info.pics : '';
				if (pics) {
					pics = pics.split(',');
				} else {
					pics = [];
				}
				that.pics = pics;
				
				that.pic = res.info.pic ? res.info.pic : '';

				that.loaded();
			});
		},
    formSubmit: function (e) {
			var that = this;
      var formdata = e.detail.value;
			formdata.id = that.opt.id;
      if (formdata.name == '') {
        app.alert('请输入名称');return;
      }
      if (formdata.weixin == '') {
        app.alert('请输入联系方式');return;
      }
			app.showLoading('提交中');
      app.post("ApiToupiao/baoming",formdata, function (data) {
				app.showLoading(false);
        if (data.status == 1) {
          app.success(data.msg);
          setTimeout(function () {
            app.goto('index?id='+that.opt.id);
          }, 1000);
        } else {
          app.error(data.msg);
        }
      });
    },
		uploadpic:function(e){
			var that = this;
			app.chooseImage(function(urls){
				that.pic = urls[0];
			},1)
		},
		removepic:function(e){
			var that = this;
			that.pic = '';
		},
		uploadimg:function(e){
			var that = this;
			var field= e.currentTarget.dataset.field
			var pics = that[field]
			if(!pics) pics = [];
			app.chooseImage(function(urls){
				for(var i=0;i<urls.length;i++){
					pics.push(urls[i]);
				}
				if(field == 'pics') that.pics = pics;
			},1)
		},
		removeimg:function(e){
			var that = this;
			var index= e.currentTarget.dataset.index
			var field= e.currentTarget.dataset.field
			if(field == 'pics'){
				var pics = that.pics
				pics.splice(index,1);
				that.pics = pics;
			}
		},
  }
};
</script>
<style>
radio{transform: scale(0.6);}
checkbox{transform: scale(0.6);}
.apply_box{ padding:2rpx 24rpx 0 24rpx; background: #fff;margin: 24rpx;border-radius: 10rpx}
.apply_title { background: #fff}
.apply_title .qr_goback{ width:18rpx;height:32rpx; margin-left:24rpx;     margin-top: 34rpx;}
.apply_title .qr_title{ font-size: 36rpx; color: #242424;   font-weight:bold;margin: 0 auto; line-height: 100rpx;}

.apply_item{ line-height: 100rpx; display: flex;justify-content: space-between;border-bottom:1px solid #eee }
.apply_box .apply_item:last-child{ border:none}
.apply_item input{ width: 100%; border: none;color:#111;font-size:28rpx; text-align: right}
.apply_item input::placeholder{ color:#999999}
.apply_item textarea{ width:100%;min-height:200rpx;padding:20rpx 0;border: none;}
.apply_item .upload_pic{ margin:50rpx 0;background: #F3F3F3;width:90rpx;height:90rpx; text-align: center  }
.apply_item .upload_pic image{ width: 32rpx;height: 32rpx; }
.set-btn{width: 90%;margin:0 5%;height:96rpx;line-height:96rpx;border-radius:48rpx;color:#FFFFFF;font-size:30rpx;font-weight:bold;background:#fff}

.layui-imgbox{margin-right:16rpx;margin-bottom:10rpx;font-size:24rpx;position: relative;}
.layui-imgbox-close{position: absolute;display: block;width:32rpx;height:32rpx;right:-16rpx;top:-16rpx;z-index:90;color:#999;font-size:32rpx;background:#fff}
.layui-imgbox-close image{width:100%;height:100%}
.layui-imgbox-img{display: block;width:200rpx;height:200rpx;padding:2px;border: #d3d3d3 1px solid;background-color: #f6f6f6;overflow:hidden}
.layui-imgbox-img>image{max-width:100%;}
.layui-imgbox-repeat{position: absolute;display: block;width:32rpx;height:32rpx;line-height:28rpx;right: 2px;bottom:2px;color:#999;font-size:30rpx;background:#fff}
.uploadbtn{position:relative;height:200rpx;width:200rpx}
</style>
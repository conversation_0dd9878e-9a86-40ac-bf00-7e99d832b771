Stack trace:
Frame         Function      Args
0007FFFFB6C0  00021006116E (00021028DEE8, 000210272B3E, 0007FFFFB6C0, 0007FFFFA5C0) msys-2.0.dll+0x2116E
0007FFFFB6C0  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFFB6C0  0002100469F2 (00021028DF99, 0007FFFFB578, 0007FFFFB6C0, 000000000000) msys-2.0.dll+0x69F2
0007FFFFB6C0  00021006A3FE (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A3FE
0007FFFFB6C0  00021006A525 (0007FFFFB6D0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A525
0001004F94B7  00021006B985 (0007FFFFB6D0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B985
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFD8E880000 ntdll.dll
7FFD8DC90000 KERNEL32.DLL
7FFD8C1E0000 KERNELBASE.dll
7FFD8CBD0000 USER32.dll
7FFD8BEA0000 win32u.dll
7FFD8CB80000 GDI32.dll
7FFD8BF50000 gdi32full.dll
7FFD8C630000 msvcp_win.dll
7FFD8BD10000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFD8E5D0000 advapi32.dll
7FFD8DEE0000 msvcrt.dll
7FFD8CD80000 sechost.dll
7FFD8E4A0000 RPCRT4.dll
7FFD8B580000 CRYPTBASE.DLL
7FFD8BED0000 bcryptPrimitives.dll
7FFD8DDB0000 IMM32.DLL

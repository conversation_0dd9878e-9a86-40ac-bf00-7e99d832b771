<template>
<view class="container" :style="{backgroundColor:pageinfo.bgcolor}">
	<dp :pagecontent="pagecontent" :menuindex="menuindex" :latitude="latitude" :longitude="longitude"></dp>
	<view class="ggdialog" v-if="guanggaopic && hideguanggao==0">
		<view class="main">
			<view class="close" @tap="closegg"><image src="/static/img/close.png"></image></view>
			<image :src="guanggaopic" class="guanggaopic" @tap="goto" :data-url="guanggaourl" mode="widthFix"></image>
		</view>
	</view>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt" @getmenuindex="getmenuindex"></dp-tabbar>
	<popmsg ref="popmsg"></popmsg>
</view>
</template>
<script>
var app = getApp();
export default {
	data() {
	return {
			opt:{},
			loading:false,
      isload: false,
			menuindex:-1,
			id: 0,
			pageinfo: [],
			pagecontent: [],
			hideguanggao: 0,
			title: "",
			oglist: "", 
			guanggaopic: "",
			guanggaourl: "",
			latitude:'',
			longitude:'',
		}
	},
	onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.getdata();
	},
	onPullDownRefresh:function(e){
		this.getdata();
	},
	onShareAppMessage:function(){
		return this._sharewx({title:this.title});
	},
	onShareTimeline:function(){
		var sharewxdata = this._sharewx({title:this.title});
		var query = (sharewxdata.path).split('?')[1];
		console.log(sharewxdata)
		console.log(query)
		return {
			title: sharewxdata.title,
			imageUrl: sharewxdata.imageUrl,
			query: query
		}
	},
	methods: {
		getdata:function(){
			var that = this;
			var opt = this.opt
			var id = 0;
			if (opt && opt.id) {
			  id = opt.id;
			}
			that.loading = true;
			app.get('ApiIndex/index', {id: id,latitude:that.latitude,longitude:that.longitude}, function (data) {
				that.loading = false;
				uni.stopPullDownRefresh();
			  if (data.status == 2) {
			    //付费查看
			    app.goto('/pages/pay/pay?id='+data.payorderid, 'redirect');
			    return;
			  }
			  if (data.status == 1) {
			    var pagecontent = data.pagecontent;
					that.title = data.pageinfo.title;
					that.oglist = data.oglist;
					that.guanggaopic = data.guanggaopic;
					that.guanggaourl = data.guanggaourl;
					that.pageinfo = data.pageinfo;
					that.pagecontent = data.pagecontent;
			    uni.setNavigationBarTitle({
			      title: data.pageinfo.title
			    });
					that.loaded({title:that.title});
					if(that.latitude=='' && that.longitude=='' && data.needlocation){
						app.getLocation(function (res) {
							that.latitude = res.latitude;
							that.longitude = res.longitude;
							that.getdata();
						});
					}
			  } else {
			    if (data.msg) {
			      app.alert(data.msg, function () {
			        if (data.url) app.goto(data.url);
			      });
			    } else if (data.url) {
			      app.goto(data.url);
			    } else {
			      app.alert(app.lang('you do not have permission to view'));
			    }
			  }
			});
		},
		closegg: function () {
			this.hideguanggao = 1;
		},
	}
}
</script>
<style>
.ggdialog{ position:fixed;z-index:99991;width:100%;max-width:640px;height:100%;background:rgba(0,0,0,0.6);display:flex;justify-content:center;align-items:center;top:0;}
.ggdialog .main{ width:80%;height:70%;position:relative;display:flex;justify-content:center;align-items:center}
.ggdialog .close{ position:absolute;padding:20rpx;top:-80rpx;right:-40rpx}
.ggdialog .close image{ width:40rpx;height:40rpx;}
.ggdialog .guanggaopic{max-width:100%;height:auto;max-height:100%}
</style>
<template>
	<view class="container">
		<block v-if="isload">
			<view class="addfromwx" v-if="getplatform() == 'wx' && type!=1" @tap="getweixinaddress">
				<image src="/static/img/weixin.png" class="img" /> {{lang('get the delivery address on wechat')}}
				<view class="flex1"></view>
				<image src="/static/img/arrowright.png" style="width:30rpx;height:30rpx;" />
			</view>
			<form @submit="formSubmit">
				<view class="form">
					<view class="form-item">
						<text class="label">{{lang('the name')}}</text>
						<input class="input" type="text" :placeholder="lang('please enter a name')"
							placeholder-style="font-size:28rpx;color:#BBBBBB" name="name" :value="name"></input>
					</view>
					<view class="form-item" v-if="showCompany">
						<text class="label">{{lang('company')}}</text>
						<input class="input" type="text" :placeholder="lang('please enter the company name')"
							placeholder-style="font-size:28rpx;color:#BBBBBB" name="company" :value="company"></input>
					</view>
					<view class="form-item">
						<text class="label">{{lang('phone number')}}</text>
						<input class="input" type="number" :placeholder="lang('please enter your mobile phone number')"
							placeholder-style="font-size:28rpx;color:#BBBBBB" name="tel" :value="tel"></input>
					</view>

					<view class="form-item">
						<!-- v-if="!type" -->
						<text class="label flex0">{{lang('select your location')}}</text>
						<text class="flex1" style="text-align:right" :style="area ? '' : 'color:#BBBBBB'"
							@tap="selectzuobiao">{{area ? area : lang('select your location')}}</text>
						<!-- <input class="input" type="text" placeholder="请选择您的位置" placeholder-style="font-size:28rpx;color:#BBBBBB" name="area" :value="area" @tap="selectzuobiao"></input> -->
					</view>
					<!-- <view class="form-item" v-else>
						<text class="label flex1">{{lang('the area where it is located')}}</text>

						<uni-data-picker :localdata="items" :popup-title="lang('Please select')" :border="false"
							:placeholder="regiondata || lang('select your location')" @change="regionchange">
						</uni-data-picker>

						<picker mode="region" name="regiondata" :value="regiondata" class="input" @change="bindPickerChange">
						<view class="picker" v-if="regiondata">{{regiondata}}</view>
						<view v-else>{{lang('ttest')}}</view>
					</picker>
					</view> -->

					<view class="form-item">
						<text class="label">{{lang('full address')}}</text>
						<input class="input" type="text" :placeholder="lang('please enter a detailed address')"
							placeholder-style="font-size:28rpx;color:#BBBBBB" name="address" :value="address"></input>
					</view>
					<view class="form-item">
						<!-- <text class="label">{{lang('Postal code')}}</text> -->
						<text class="label">{{lang('review the note')}}</text>

						<input class="input" type="text" :placeholder="lang('please enter')"
							placeholder-style="font-size:28rpx;color:#BBBBBB" name="notes" :value="notes"></input>
					</view>
					<!-- <view class="item flex-y-center" v-if="type!=1">
						<view class="f2 flex-y-center flex1">
							<input id="addressxx" placeholder="粘贴地址信息，可自动识别并填写，如：张三，188********，广东省 东莞市 xx区 xx街道 xxxx"
								placeholder-style="font-size:24rpx;color:#BBBBBB"
								style="width:85%;font-size:24rpx;margin:20rpx 0;height:100rpx;padding:4rpx 10rpx"
								@input="setaddressxx"></input>
							<view style="width:15%;text-align:center;color:#999" @tap="shibie">{{lang('ttest')}}</view>
						</view>
					</view> -->
				</view>
				<button class="savebtn"
					:style="'background:linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'"
					form-type="submit">{{lang('save')}}</button>
			</form>
		</block>
		<loading v-if="loading"></loading>
		<dp-tabbar :opt="opt"></dp-tabbar>
		<popmsg ref="popmsg"></popmsg>
	</view>
</template>

<script>
	var app = getApp();

	export default {
		data() {
			return {
				opt: {},
				loading: false,
				isload: false,
				menuindex: -1,
				name: '',
				tel: '',
				area: '',
				address: '',
				notes: '',
				longitude: '',
				latitude: '',
				regiondata: '',
				type: 0,
				addressxx: '',
				company: '',
				items: [],
				showCompany: false,
			};
		},

		onLoad: function(opt) {
			console.log('opt', opt);
			this.opt = app.getopts(opt);
			this.type = this.opt.type || 0;

			var that = this;
			app.get('ApiIndex/getAreas', {}, function(res) {
				that.items = res;
			});
			// app.get('ApiIndex/getCustom', {}, function(customs) {
			// 	var url = app.globalData.pre_url + '/static/area.json';
			// 	if (customs.data.includes('plug_zhiming')) {
			// 		url = app.globalData.pre_url + '/static/area_gaoxin.json';
			// 	}
			// 	uni.request({
			// 		url: url,
			// 		data: {},
			// 		method: 'GET',
			// 		header: {
			// 			'content-type': 'application/json'
			// 		},
			// 		success: function(res2) {
			// 			that.items = res2.data
			// 		}
			// 	});
			// });

			this.getdata();
			uni.setNavigationBarTitle({
				title: app.lang("Edit Address"),
				success() {
					console.log(app.lang("Edit Address"))
				}
			})
		},
		onPullDownRefresh: function() {
			this.getdata();
		},
		methods: {
			lang: function(k) {
				return app.lang(k);
			},
			getdata: function() {
				uni.getStorageSync('mylang') == 'zh_cn' ? uni.setLocale('zh') : uni.setLocale('en')

				var that = this;
				var addressId = that.opt.id || '';
				app.get('ApiIndex/getCustom', {}, function(customs) {
					if (customs.data.includes('plug_xiongmao')) {
						that.showCompany = true;
					}
				});
				if (addressId) {
					that.loading = true;
					app.get('ApiAddress/addressadd', {
						id: addressId,
						type: that.type
					}, function(res) {
						that.loading = false;
						that.name = res.data.name;
						that.tel = res.data.tel;
						that.area = res.data.area;
						that.address = res.data.address;
						that.longitude = res.data.longitude;
						that.latitude = res.data.latitude;
						that.company = res.data.company;
						that.notes = res.data.notes;
						if (res.data.province) {
							var regiondata = res.data.province + ',' + res.data.city + ',' + res.data.district;
						} else {
							var regiondata = 'Asia/Singapore';
						}
						that.regiondata = regiondata
						that.loaded();
					});
				} else {
					that.loaded();
				}
			},

			regionchange(e) {
				// document.getElementsByClassName('selected-item-active')[0].innerHTML="<uni-text><span>Please select</span></uni-text>"

				console.log('-------->', e);

				// if (value == 1) {
				// 	this.regiondata = value[0].text
				// } else if (e.detail.value.length == 2) {
				// 	this.regiondata = value[0].text + ',' + value[1].text
				// } else if (e.detail.value.length == 2) {
				// 	this.regiondata = value[0].text + ',' + value[1].text + ',' + value[2].text + ',' + value[3].text
				// }
				// console.log(value.address);
				// this.regiondata = value.address
				// console.log('haha', this.regiondata);


			},
			selectzuobiao: function() {
				// uni.setLocale('en');
				console.log('selectzuobiao')
				var that = this;
				uni.chooseLocation({
					success: function(res) {

						that.area = res.address;
						that.address = res.name;
						that.latitude = res.latitude;
						that.longitude = res.longitude;
					},
					fail: function(res) {
						console.log('res', res)
						if (res.errMsg == 'chooseLocation:fail auth deny') {
							//$.error('获取位置失败，请在设置中开启位置信息');
							app.confirm(app.lang(
								'failed to obtain the location. please enable the location information in the settings'
							), function() {
								uni.openSetting({});
							});
						}
					}
				});
			},
			formSubmit: function(e) {
				console.log(this.regiondata);
				var that = this;
				var formdata = e.detail.value;
				var addressId = that.opt.id || '';
				var name = formdata.name;
				var tel = formdata.tel;
				var regiondata = that.regiondata;

				// if (that.type == 1) {

				var area = that.area;
				console.log(area);
				// 	if (area == '') {
				// 		app.error(app.lang('please enter a detailed address'));
				// 		return;
				// 	}
				// } else {

				// 	var area = regiondata;
				// 	console.log(area);
				// 	if (area == '') {
				// 		app.error(app.lang('please enter a detailed address'));
				// 		return;
				// 		return;
				// 	}
				// }
				var address = formdata.address;
				var longitude = that.longitude;
				var latitude = that.latitude;
				var company = formdata.company;
				var notes = formdata.notes
				if (name == '' || tel == '' || address == '') {
					app.error(app.lang('please fill in the complete information'));
					return;
				}
				app.showLoading(app.lang('please wait'));
				app.post('ApiAddress/addressadd', {
					type: that.type,
					addressid: addressId,
					name: name,
					tel: tel,
					area: area,
					address: address,
					latitude: latitude,
					longitude: longitude,
					company: company,
					notes: notes
				}, function(res) {
					app.showLoading(false);
					if (res.status == 0) {
						app.alert(res.msg);
						return;
					}
					app.success(app.lang('save success'));
					setTimeout(function() {
						app.goback(true);
					}, 1000);
				});
			},
			delAddress: function() {
				var that = this;
				var addressId = that.opt.id;
				app.confirm(app.lang('delete address ask'), function() {
					app.showLoading(app.lang('please wait'));
					app.post('ApiAddress/del', {
						addressid: addressId
					}, function() {
						app.showLoading(false);
						app.success(app.lang('delete success'));
						setTimeout(function() {
							app.goback(true);
						}, 1000);
					});
				});
			},
			bindPickerChange: function(e) {
				var val = e.detail.value;
				this.regiondata = val;
			},
			setaddressxx: function(e) {
				this.addressxx = e.detail.value;
			},
			shibie: function() {
				var that = this;
				var addressxx = that.addressxx;
				app.post('ApiAddress/shibie', {
					addressxx: addressxx
				}, function(res) {
					var isrs = 0;
					if (res.province) {
						isrs = 1;
						that.regiondata = res.province + ',' + res.city + ',' + res.county
					}
					if (res.detail) {
						isrs = 1;
						that.address = res.detail
					}
					if (res.person) {
						isrs = 1;
						that.name = res.person
					}
					if (res.phonenum) {
						isrs = 1;
						that.tel = res.phonenum
					}
					if (isrs == 0) {
						app.error(app.lang('failed to identify'));
					} else {
						app.success(app.lang('recognition completed'));
					}
				});
			},
			getweixinaddress: function() {
				var that = this;
				wx.chooseAddress({
					success(res) {
						app.showLoading(app.lang('please wait'));
						app.post('ApiAddress/addressadd', {
							type: that.type,
							addressid: '',
							name: res.userName,
							tel: res.telNumber,
							area: res.provinceName + ',' + res.cityName + ',' + res.countyName,
							address: res.detailInfo
						}, function(res) {
							app.showLoading(false);
							if (res.status == 0) {
								app.alert(res.msg);
								return;
							}
							app.success(app.lang('save success'));
							setTimeout(function() {
								app.goback(true);
							}, 1000);
						});
					}
				})
			},


		}
	};
</script>
<style>
	.container {
		display: flex;
		flex-direction: column
	}

	.addfromwx {
		width: 94%;
		margin: 20rpx 3% 0 3%;
		border-radius: 5px;
		padding: 20rpx 3%;
		background: #FFF;
		display: flex;
		align-items: center;
		color: #666;
		font-size: 28rpx;
	}

	.addfromwx .img {
		width: 40rpx;
		height: 40rpx;
		margin-right: 20rpx;
	}

	.form {
		width: 94%;
		margin: 20rpx 3%;
		border-radius: 5px;
		padding: 0 3%;
		background: #FFF;
	}

	.form-item {
		display: flex;
		align-items: center;
		width: 100%;
		border-bottom: 1px #ededed solid;
		height: 98rpx;
	}

	.form-item:last-child {
		border: 0
	}

	.form-item .label {
		color: #8B8B8B;
		font-weight: bold;
		height: 60rpx;
		line-height: 60rpx;
		text-align: left;
		width: 100px;
		padding-right: 20rpx
	}

	.form-item .input {
		flex: 1;
		height: 60rpx;
		line-height: 60rpx;
		text-align: right
	}

	.savebtn {
		width: 90%;
		height: 96rpx;
		line-height: 96rpx;
		text-align: center;
		border-radius: 48rpx;
		color: #fff;
		font-weight: bold;
		margin: 0 5%;
		margin-top: 60rpx;
		border: none;
	}
</style>
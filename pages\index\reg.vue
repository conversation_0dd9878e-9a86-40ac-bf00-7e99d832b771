<template>
  <view class="container">
	  
    <block v-if="isload">
		<!-- <view class="logoimage">
		  <image class="logoimages" src="@/static/img/sign-up_03.png" alt="">
		</view> -->
      <block v-if="logintype!=4">
        <form @submit="formSubmit" @reset="formReset">
          <view class="title" :style="{color:t('color1')}">{{lang('register')}}</view>
          <view class="regform">
			  
		<!-- 	<view class="form-item">
				<image src="/static/img/sign-up_07.jpg" class="img"/>
				<select  class="select" @change=changes() v-model="selected" style="width: 80%;font-size:30rpx;" placeholder-style="font-size:30rpx;color:#B2B5BE"  name="selectList" >
					<option :value="item.code" v-for="(item,index) in countries" :key="index">{{item.name}}</option>
				</select>
		
			</view> -->
            <view class="form-item">
              <image src="/static/img/sign-up_06.jpg" class="img" />
              <input type="text" class="input" :placeholder="lang('please enter your mobile phone number')" placeholder-style="font-size:30rpx;color:#B2B5BE"
                name="tel" value="" @input="telinput" />
            </view>
            <view class="form-item" v-if="needsms">
              <image src="/static/img/sign-up_08.jpg" class="img" />
              <input type="text" class="input" placeholder="" placeholder-style="font-size:30rpx;color:#B2B5BE"
                name="smscode" value="" />
              <view class="code" :style="{color:t('color1')}" @tap="smscode">{{smsdjs||lang('obtaining the verification code')}}</view>
            </view>
            <view class="form-item">
              <image src="/static/img/sign-up_10.jpg" class="img" />
              <input type="text" class="input" :placeholder="lang('password combination tips')"
                placeholder-style="font-size:30rpx;color:#B2B5BE" name="pwd" value="" :password="true" />
            </view>
            <view class="form-item">
              <image src="/static/img/sign-up_10.jpg" class="img" />
              <input type="text" class="input" :placeholder="lang('enter the login password again')" placeholder-style="font-size:30rpx;color:#B2B5BE"
                name="repwd" value="" :password="true" />
            </view>
            <view class="form-item" v-if="reg_invite_code && !parent">
              <image src="/static/img/sign-up_12.jpg" class="img" />
              <input type="text" class="input"
                :placeholder="(reg_invite_code_type==0?lang('invite the phone number'):lang('invitation code'))+(reg_invite_code==2?lang('mandatory'):'')"
                placeholder-style="font-size:30rpx;color:#B2B5BE" name="yqcode" value="" />
            </view>
            <view class="form-item" v-if="reg_invite_code && parent" style="color:#666;">
              <block v-if="reg_invite_code_type == 0 ">
                {{lang('the inviter')}}：<image :src="parent.headimg" style="width: 80rpx; height: 80rpx;border-radius: 50%;"></image>
                {{parent.nickname}}
              </block>
              <block v-else>
                {{lang('invitation code')}}：{{parent.yqcode}}
              </block>
            </view>

            <view class="xieyi-item" v-if="xystatus==1">
              <checkbox-group @change="isagreeChange"><label class="flex-y-center">
                  <checkbox class="checkbox" value="1" :checked="isagree" />{{lang('i have read and agree')}}</label></checkbox-group>
              <text :style="{color:t('color1')}" @tap="showxieyiFun">{{xyname}}</text>
            </view>

            <button class="form-btn"
              :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}"
              form-type="submit">{{lang('register')}}</button>
          </view>
        </form>
        <view class="tologin1" @tap="goto" data-url="login" data-opentype="redirect"><span>{{lang('Already have an account')}}?</span><span class="clos1">{{lang('existing account')}}</span></view>

        <block v-if="logintype_2 || logintype_3">
          <view class="othertip">
            <view class="othertip-line"></view>
            <view class="othertip-text">
              <text class="txt">{{lang('log in by other means')}}</text>
            </view>
            <view class="othertip-line"></view>
          </view>
          <view class="othertype">
            <view class="othertype-item" v-if="logintype_3" @tap="weixinlogin">
              <image class="img" :src="pre_url+'/static/img/login-'+platformimg+'.png'" />
              <text class="txt">{{platformname}}{{lang('login')}}</text>
            </view>
            <view class="othertype-item" v-if="logintype_2" @tap="goto" data-url="login?logintype=2"
              data-opentype="redirect">
              <image class="img" src="/static/img/reg-tellogin.png" />
              <text class="txt">{{lang('log in by phone means')}}</text>
            </view>
          </view>
        </block>
      </block>
      <!-- 绑定手机号 -->
      <block v-if="logintype==4">
        <block v-if="platform == 'wx'">
          <view class="authlogin">
            <view class="authlogin-logo">
              <image :src="logo" style="width:100%;height:100%" />
            </view>
            <view class="authlogin-name">{{lang('authorized login')}}{{name}}</view>
            <button class="authlogin-btn" open-type="getPhoneNumber" @getphonenumber="getPhoneNumber"
              :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}">{{platformname}}{{lang('authorization bind the mobile phone number')}}</button>
            <button class="authlogin-btn2" @tap="nobindregister" v-if="login_bind==1">{{lang('dont bind for now')}}</button>
          </view>
        </block>
        <block v-else>
          <form @submit="bindregister" @reset="formReset">
            <view class="title" :style="{color:t('color1')}">{{lang('bind phone number')}}</view>
            <view class="regform">
              <view class="form-item">
                <image src="/static/img/reg-tel.png" class="img" />
                <input type="text" class="input" :placeholder="lang('please enter your mobile phone number')" placeholder-style="font-size:30rpx;color:#B2B5BE"
                  name="tel" value="" @input="telinput" />
              </view>
              <view class="form-item">
                <image src="/static/img/reg-code.png" class="img" />
                <input type="text" class="input" :placeholder="lang('please enter your mobile verify code')" placeholder-style="font-size:30rpx;color:#B2B5BE"
                  name="smscode" value="" />
                <view class="code" :style="{color:t('color1')}" @tap="smscode">{{smsdjs||lang('obtaining the verification code')}}</view>
              </view>
              <button class="form-btn" form-type="submit"
                :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}">{{lang('confirm')}}</button>
              <button class="form-btn2" @tap="nobindregister" v-if="login_bind==1">{{lang('dont bind for now')}}</button>
            </view>
          </form>
        </block>
      </block>
      <view v-if="showxieyi" class="xieyibox">
        <view class="xieyibox-content">
          <view style="overflow:scroll;height:100%;">
            <parse :content="xycontent" @navigate="navigate"></parse>
          </view>
          <view
            style="position:absolute;z-index:9999;bottom:10px;left:0;right:0;margin:0 auto;text-align:center; width: 50%;height: 60rpx; line-height: 60rpx; color: #fff; border-radius: 8rpx;"
            :style="{background:'linear-gradient(90deg,'+t('color1')+' 0%,rgba('+t('color1rgb')+',0.8) 100%)'}"
            @tap="hidexieyi">{{lang('i have read and agree')}}</view>
        </view>
      </view>

    </block>
    <loading v-if="loading"></loading>
    <dp-tabbar :opt="opt"></dp-tabbar>
    <popmsg ref="popmsg"></popmsg>
  </view>
</template>

<script>
var app = getApp();

export default {
  data () {
    return {
      opt: {},
      loading: false,
      isload: false,
      menuindex: -1,
      pre_url: app.globalData.pre_url,
      platform2: app.globalData.platform2,

      platform: '',
      platformname: '',
      platformimg: 'weixin',
      logintype: 0,
      logintype_1: true,
      logintype_2: false,
      logintype_3: false,
      logo: '',
      name: '',
      xystatus: 0,
      xyname: '',
      xycontent: '',
      needsms: false,
      showxieyi: false,
      isagree: false,
      smsdjs: '',
      tel: '',
      hqing: 0,
      frompage: '/myshop/my/usercenter',
      wxloginclick: false,
      login_bind: 0,
      reg_invite_code: 0,
      reg_invite_code_text: '',
      reg_invite_code_type: 0,
      parent: {},
	   selected:"+65",
	  countries:   [ 
		  {
		    "code": "+61",
		    "name": app.lang("Australia +61")
		  },
		  {
		    "code": "+673",
		    "name": app.lang("Brunei +673") 
		  },
		  {
		    "code": "+880",
		    "name": app.lang("Bangladesh +880") 
		  },
		  {
		    "code": "+855",
		    "name":  app.lang("Cambodia +855")
		  },
		  {
		    "code": "+86",
		    "name": app.lang("China +86") 
		  },
		  {
		    "code": "+886",
		    "name":  app.lang("Hong Kong +852")
		  },
		
		  {
		    "code": "+91",
		    "name": app.lang("India +91") 
		  },
		  {
		    "code": "+81",
		    "name":  app.lang("Japan +81")
		  },
		  {
		    "code": "+60",
		    "name":  app.lang("Malaysia +60")
		  },
		  {
		    "code": "+95",
		    "name":  app.lang("Myanmar +95")
		  },
		  {
		    "code": "+64",
		    "name":  app.lang("New Zealand +64")
		  },
		  
		  {
		    "code": "+968",
		    "name":  app.lang("Oman +968")
		  },
		  {
		    "code": "+63",
		    "name":  app.lang("Philippines +63")
		  },
			{
			  "code": "+65",
			  "name":  app.lang("Singapore +65")
			},
			
			
			{
			  "code": "+82",
			  "name": app.lang( "South Korea +82")
			},
			{
			  "code": "+94",
			  "name": app.lang( "Sri Lanka +94")
			},
		
			{
			  "code": "+66",
			  "name": app.lang("Thailand +66") 
			},
			{
			  "code": "+886",
			  "name":  app.lang("Taiwan   +886")
			},
			{
			  "code": "+84",
			  "name": app.lang("Vietnam +84") 
			},
			{
			  "code": "+62",
			  "name": app.lang("Indonesia +62") 
			},
		
		] ,
    };
  },

  onLoad: function (opt) {
    this.opt = app.getopts(opt);
    if (this.opt.frompage) this.frompage = decodeURIComponent(this.opt.frompage);
    if (this.opt.logintype) this.logintype = this.opt.logintype;
    if (this.opt.login_bind) this.login_bind = this.opt.login_bind;
    this.getdata();
	this.getcodedata()
  },
  onPullDownRefresh: function () {
    this.getdata();
	this.getcodedata()
  },
  methods: {
	lang:function(k){
		return app.lang(k);
	},  
	  changes:function(e){
			  console.log(this.selected)
		  },
	getcodedata:function(){
		var that = this;
		that.loading = true;
		app.get('ApiIndex/areas', {  }, function (res) {
		  that.loading = false;
		  console.log('codedata',res)
		  that.countries=res.data
		   that.selected= res.default
		  })
	},  
    getdata: function () {
      var that = this;
      that.loading = true;
      app.get('ApiIndex/reg', { pid: app.globalData.pid }, function (res) {
        that.loading = false;
        if (res.status == 0) {
          app.alert(res.msg); return;
        }
        that.logintype_2 = res.logintype_2;
        that.logintype_3 = res.logintype_3;
        that.logintype_3 = res.logintype_3;
        // #ifdef APP-PLUS
        if (that.platform2 == 'ios') {
          if (plus.runtime.isApplicationExist({ pname: 'com.tencent.mm', action: 'weixin://' })) {

          } else {
            that.logintype_3 = false;
          }
        }
        // #endif

        that.xystatus = res.xystatus;
        that.xyname = res.xyname;
        that.xycontent = res.xycontent;
        that.logo = res.logo;
        that.name = res.name;
        that.needsms = res.needsms;
        that.platform = res.platform;
        that.reg_invite_code = res.reg_invite_code;
        that.reg_invite_code_text = res.reg_invite_code_text;
        that.reg_invite_code_type = res.reg_invite_code_type;
        that.parent = res.parent;
        if (that.platform == 'mp' || that.platform == 'wx' || that.platform == 'app') {
          that.platformname = '微信';
          that.platformimg = 'weixin';
        }
        if (that.platform == 'toutiao') {
          that.platformname = '头条';
          that.platformimg = 'toutiao';
        }
        if (that.platform == 'alipay') {
          that.platformname = '支付宝';
          that.platformimg = 'alipay';
        }
        if (that.platform == 'qq') {
          that.platformname = 'QQ';
          that.platformimg = 'qq';
        }
        if (that.platform == 'baidu') {
          that.platformname = '百度';
          that.platformimg = 'baidu';
        }
        that.loaded();
      });
    },
    formSubmit: function (e) {
      var that = this;
      var formdata = e.detail.value;
      if (formdata.tel == '') {
        app.alert(app.lang('please enter your mobile phone number'));
        return;
      }
      if (formdata.pwd == '') {
        app.alert(app.lang('please enter your password'));
        return;
      }
      if (formdata.pwd.length < 6) {
        app.alert(app.lang('password combination tips'));
        return;
      }
      if (formdata.repwd == '') {
        app.alert(app.lang('enter the login password again'));
        return;
      }
      if (formdata.pwd != formdata.repwd) {
        app.alert(app.lang('two passwords are different'));
        return;
      }
      if (that.needsms) {
        if (formdata.smscode == '') {
          app.alert(app.lang('please enter your mobile verify code'));
          return;
        }
      } else {
        formdata.smscode = '';
      }

      if (that.xystatus == 1 && !that.isagree) {
        app.error(app.lang('please read and agree to the user registration agreement first'));
        return false;
      }
	  if (formdata.yqcode == '') {
	    app.error(app.lang('please enter the referral code'));
	    return false;
	  }
      app.showLoading(app.lang('please wait'));
	  const editedText = that.selected.slice(1) 
      app.post("ApiIndex/regsub", {code:editedText, tel: formdata.tel, pwd: formdata.pwd, smscode: formdata.smscode, pid: app.globalData.pid, yqcode: formdata.yqcode }, function (data) {
        app.showLoading(false);
        if (data.status == 1) {
          app.success(data.msg);
          setTimeout(function () {
            app.goto('/myshop/my/usercenter', 'redirect');
          }, 1000);
        } else {
          app.error(data.msg);
        }
      });
    },
    getPhoneNumber: function (e) {
      var that = this
      if (e.detail.errMsg == "getPhoneNumber:fail user deny") {
        app.error(app.lang('please agree to authorize access to the phone number')); return;
      }
      wx.login({
        success (res1) {
          console.log(res1);
          var code = res1.code;
          //用户允许授权
          app.post('ApiIndex/wxRegister', { iv: e.detail.iv, encryptedData: e.detail.encryptedData, code: code, pid: app.globalData.pid }, function (res2) {
            if (res2.status == 1) {
              app.success(res2.msg);
              setTimeout(function () {
                app.goto(that.frompage, 'redirect');
              }, 1000);
            } else {
              app.error(res2.msg);
            }
            return;
          })
        }
      });
    },
    bindregister: function (e) {
      var that = this;
      var formdata = e.detail.value;
      if (formdata.tel == '') {
        app.alert(app.lang('please enter your mobile phone number'));
        return;
      }
      if (formdata.smscode == '') {
        app.alert(app.lang('please enter your mobile verify code'));
        return;
      }
      that.register(formdata.tel, formdata.smscode);
    },
    nobindregister: function () {
      this.register('', '');
    },
    register: function (tel, smscode) {
      var that = this;
      var url = '';
      if (that.platform == 'app') {
        url = 'ApiIndex/appwxRegister';
      } else if (that.platform == 'mp' || that.platform == 'h5') {
        url = 'ApiIndex/shouquanRegister';
      } else {
        url = 'ApiIndex/' + that.platform + 'Register';
      }
      app.post(url, { tel: tel, smscode: smscode, pid: app.globalData.pid }, function (res2) {
        if (res2.status == 1) {
          app.success(res2.msg);
          setTimeout(function () {
            app.goto(that.frompage, 'redirect');
          }, 1000);
        } else {
          app.error(res2.msg);
        }
        return;
      });
    },
    weixinlogin: function () {
      var that = this;
      if (that.xystatus == 1 && !that.isagree) {
        that.showxieyi = true;
        that.wxloginclick = true;
        return;
      }
      that.wxloginclick = false;
      app.authlogin(function (res) {
        if (res.status == 1) {
          app.success(res.msg);
          setTimeout(function () {
            app.goto(that.frompage, 'redirect');
          }, 1000);
        } else if (res.status == 2) {
          that.logintype = 4;
          that.login_bind = res.login_bind
        } else {
          app.error(res.msg);
        }
      });
    },
    isagreeChange: function (e) {
      var val = e.detail.value;
      if (val.length > 0) {
        this.isagree = true;
      } else {
        this.isagree = false;
      }
      console.log(this.isagree);
    },
    showxieyiFun: function () {
      this.showxieyi = true;
    },
    hidexieyi: function () {
      this.showxieyi = false;
      this.isagree = true;
      if (this.wxloginclick) {
        this.weixinlogin();
      }
    },
    telinput: function (e) {
      this.tel = e.detail.value
    },
    smscode: function () {
      var that = this;
      if (that.hqing == 1) return;
      that.hqing = 1;
      var tel = that.tel;
      if (tel == '') {
        app.alert(app.lang('please enter your mobile phone number'));
        that.hqing = 0;
        return false;
      }
      // if (!/^1[3456789]\d{9}$/.test(tel)) {
      //   app.alert(app.lang('wrong cell phone number'));
      //   that.hqing = 0;
      //   return false;
      // }
	  	const editedText = that.selected.slice(1) 
      app.post("ApiIndex/sendsms", { code:editedText,tel: tel }, function (data) {
        if (data.status != 1) {
          app.alert(data.msg);
        }
      });
      var time = 120;
      var interval1 = setInterval(function () {
        time--;
        if (time < 0) {
          that.smsdjs = app.lang('obtain again');
          that.hqing = 0;
          clearInterval(interval1);
        } else if (time >= 0) {
          that.smsdjs = time + app.lang('second');
        }
      }, 1000);
    }
  }
};
</script>

<style>
page {
  background: #ffffff;
}
.container {
  width: 100%;
}
.title {
  margin: 20rpx 50rpx 20rpx 40rpx;
  height: 60rpx;
  line-height: 60rpx;
  font-size: 48rpx;
/*  font-weight: bold; */
  color: #a0d346;
  text-align: center;
}
.regform {
  width: 100%;
  padding: 0 50rpx;
  border-radius: 5px;
  background: #fff;
}
.regform .form-item {
  display: flex;
  align-items: center;
  width: 100%;
  border-bottom: 1px #ededed solid;
  height: 88rpx;
  line-height: 88rpx;
  border-bottom: 1px solid #f0f3f6;
  margin-top: 20rpx;
}
.regform .form-item:last-child {
  border: 0;
}
.regform .form-item .img {
  width: 32rpx;
  height: 44rpx;
  margin-right: 30rpx;
}
.regform .form-item .input {
  flex: 1;
  color: #000;
}
.regform .form-item .code {
  font-size: 30rpx;
}
.regform .xieyi-item {
  display: flex;
  align-items: center;
  margin-top: 50rpx;
}
.regform .xieyi-item {
  font-size: 24rpx;
  color: #b2b5be;
}
.regform .xieyi-item .checkbox {
  transform: scale(0.6);
}
.regform .form-btn {
  margin-top: 20rpx;
  width: 100%;
  height: 96rpx;
  line-height: 96rpx;
  color: #fff;
  font-size: 30rpx;
  border-radius: 48rpx;
}
.regform .form-btn2 {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  background: #eeeeee;
  border-radius: 40rpx;
  color: #a9a9a9;
  margin-top: 30rpx;
}
.tologin {

  color: #737785;
  font-size: 26rpx;
  display: flex;
  width: 100%;
  padding: 0 80rpx;
  margin-top: 30rpx;
}
.tologin1{
	color: #737785;
	font-size: 26rpx;

	width: 100%;
	padding: 0 80rpx;
	margin-top: 30rpx;
	text-align: center !important;
} 
.othertip {
  height: auto;
  overflow: hidden;
  display: flex;
  align-items: center;
  width: 580rpx;
  padding: 20rpx 20rpx;
  margin: 0 auto;
  margin-top: 60rpx;
}
.othertip-line {
  height: auto;
  padding: 0;
  overflow: hidden;
  flex: 1;
  height: 0;
  border-top: 1px solid #f2f2f2;
}
.othertip-text {
  padding: 0 32rpx;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}
.othertip-text .txt {
  color: #a3a3a3;
  font-size: 22rpx;
}

.othertype {
  width: 70%;
  margin: 20rpx 15%;
  display: flex;
  justify-content: center;
}
.othertype-item {
  width: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.othertype-item .img {
  width: 88rpx;
  height: 88rpx;
  margin-bottom: 20rpx;
}
.othertype-item .txt {
  color: #a3a3a3;
  font-size: 24rpx;
}

.xieyibox {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 99;
  background: rgba(0, 0, 0, 0.7);
}
.xieyibox-content {
  width: 90%;
  margin: 0 auto;
  height: 80%;
  margin-top: 20%;
  background: #fff;
  color: #333;
  padding: 5px 10px 50px 10px;
  position: relative;
  border-radius: 2px;
}

.authlogin {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.authlogin-logo {
  width: 180rpx;
  height: 180rpx;
  margin-top: 120rpx;
}
.authlogin-name {
  color: #999999;
  font-size: 30rpx;
  margin-top: 60rpx;
}
.authlogin-btn {
  width: 580rpx;
  height: 96rpx;
  line-height: 96rpx;
  background: #51b1f5;
  border-radius: 48rpx;
  color: #fff;
  margin-top: 100rpx;
}
.authlogin-btn2 {
  width: 580rpx;
  height: 96rpx;
  line-height: 96rpx;
  background: #eeeeee;
  border-radius: 48rpx;
  color: #a9a9a9;
  margin-top: 20rpx;
}
.logoimages {
  /* 	height: 10px;
	margin: 20rpx auto; */
}
.logoimage {
  text-align: center;
  /* width: 50%; */
  margin: 30px auto;
}
.logoimage > image {
	margin-top: 20px;
 width: 260px;
 height: 100px;
}
.clos1{
	color: #9cd23f;
}
.select{
	border: none;
	background: #f6f6f6;
	height: 23px;
	width: 85%!important;
	color:#b2b5be !important;
}
</style>
<template>
<view class="container">
	<block v-if="isload">
		<form @submit="formSubmit" @reset="formReset" report-submit="true">
		<view class="form-content">
			<view class="form-item">
				<text class="label">退款原因</text>
				<view class="input-item"><textarea placeholder="请输入退款原因" placeholder-style="color:#999;" name="reason"></textarea></view>
			</view>
			<view class="form-item">
				<text class="label">退款金额(元)</text>
				<view class="flex"><input name="money" type="number" :value="totalprice" placeholder="请输入退款金额" placeholder-style="color:#999;"></input></view>
			</view>
		</view>
		<button class="btn" form-type="submit" :style="{background:t('color1')}">确定</button>
		</form>
	</block>
	<loading v-if="loading"></loading>
	<dp-tabbar :opt="opt"></dp-tabbar>
</view>
</template>

<script>
var app = getApp();

export default {
  data() {
    return {
			opt:{},
			loading:false,
      isload: false,
			menuindex:-1,

      orderid: '',
      totalprice: 0
    };
  },

  onLoad: function (opt) {
		this.opt = app.getopts(opt);
		this.orderid = this.opt.orderid,
		this.totalprice = this.opt.price
		this.getdata();
  },
	onPullDownRefresh: function () {
		this.getdata();
	},
  methods: {
		getdata: function () {
			var that = this;
			that.loading = true;
			app.post('ApiRestaurantTakeaway/refundinit', {}, function (res) {
				that.loading = false;
				that.tmplids = res.tmplids;
				that.loaded();
			});
		},
    formSubmit: function (e) {
      var that = this;
      var orderid = that.orderid;
      var reason = e.detail.value.reason;
      var money = parseFloat(e.detail.value.money);

      if (reason == '') {
        app.alert('请填写退款原因');
        return;
      }
      if (money <= 0 || money > parseFloat(that.totalprice)) {
        app.alert('退款金额有误');
        return;
      }
			app.showLoading('提交中');
      app.post('ApiRestaurantTakeaway/refund', {orderid: orderid,reason: reason,money: money}, function (res) {
				app.showLoading(false);
        app.alert(res.msg);
        if (res.status == 1) {
          that.subscribeMessage(function () {
            setTimeout(function () {
              app.goback(true);
            }, 1000);
          });
        }
      });
    }
  }
};
</script>
<style>
.form-content{width:94%;margin:10rpx 3%;border-radius:10rpx;display:flex;flex-direction:column;background:#fff;overflow:hidden}
.form-item{ width:100%;padding: 32rpx 20rpx;}
.form-item .label{ width:100%;height:60rpx;line-height:60rpx}
.form-item .input-item{ width:100%;}
.form-item textarea{ width:100%;height:200rpx;border: 1px #eee solid;padding: 20rpx;}
.form-item input{ width:100%;border: 1px #f5f5f5 solid;padding: 10rpx;height:80rpx}
.form-item .mid{ height:80rpx;line-height:80rpx;padding:0 20rpx;}
.btn{ height:100rpx;line-height: 100rpx;width:90%;margin:0 auto;border-radius:50rpx;margin-top:50rpx;color: #fff;font-size: 30rpx;font-weight:bold}
</style>
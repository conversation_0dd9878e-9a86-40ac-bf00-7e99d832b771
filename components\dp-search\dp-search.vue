<template>
<view class="dp-search" :style="{
	backgroundColor:params.bgcolor,
	margin:(params.margin_y*2.2)+'rpx '+(params.margin_x*2.2)+'rpx',
	padding:(params.padding_y*2.2)+'rpx '+(params.padding_x*2.2)+'rpx'
}">
	<view class="dp-search-search"
		:style="{borderColor:params.bordercolor,borderRadius:params.borderradius+'px'}">
		<view class="dp-search-search-f1"></view>
		<view class="dp-search-search-f2">
			<input class="dp-search-search-input" @confirm="searchgoto" :data-url="params.hrefurl" name="keyword"
				:placeholder="params.placeholder|| lang('input the keyword search in the shop')" placeholder-style="color:#aaa;font-size:28rpx" :style="params.color?'color:'+params.color:''" />
		</view>
	</view>
</view>
</template>
<script>
	var app =getApp();
	export default {
		props: {
			params: {},
			data: {}
		},
		methods:{
			lang: function(k) {
				return app.lang(k);
			},
			searchgoto:function(e){
				console.log('数据', this.params)
				var url = e.currentTarget.dataset.url;
					if (url.indexOf('?') > 0) {
						url += '&keyword='+e.detail.value;
					}else{
						url += '?keyword='+e.detail.value;
					}
					var opentype = e.currentTarget.dataset.opentype
					app.goto(url,opentype);
			}
		}
	}
</script>
<style>
.dp-search {padding:20rpx;height: auto; position: relative;}
.dp-search-search {height:72rpx;background: #fff;border: 1px solid #c0c0c0;border-radius: 6rpx;overflow: hidden;display:flex}
.dp-search-search-f1 {height:72rpx;width:72rpx;color: #666;border: 0px;padding: 0px;margin: 0px;background:url('~@/static/img/search_ico.png') center no-repeat; background-size:30rpx;}
.dp-search-search-f2{height: 72rpx;flex:1}
.dp-search-search-input {height:72rpx;width: 100%;border: 0px;padding: 0px;margin: 0px;outline: none;color: #666;}
</style>
